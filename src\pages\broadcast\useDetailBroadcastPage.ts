import { BroadcastRepository } from '@/repositories/broadcast-repository';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import { useState } from 'react';
import type { IResDetailBroadcast } from '@/types/response/IResDetailBroadcast';
import { useUi } from '@/hooks/useUi';

export function useDetailBroadcastPage() {
  const { id } = useParams();
  const broadcastRepository = new BroadcastRepository();
  const [showModalOpen, setShowModalOpen] = useState<'APPROVE' | 'REJECT' | 'SEND' | undefined>(undefined);
  const [dataDetail, setDataDetail] = useState<IResDetailBroadcast | undefined>();
  const [reason, setReason] = useState<string>('');

  const { toast } = useUi();

  const queryDetail = useQuery({
    queryKey: ['detail_broadcast', id],
    enabled: !!id,
    queryFn: async () =>
      await broadcastRepository.detailBroadcast(id || '').then((res) => {
        if (res) setDataDetail(res);
        return res;
      }),
  });

  const mutationApprove = useMutation({
    mutationFn: async () =>
      await broadcastRepository.approveBroadcast(id || '').then((res) => {
        if (res) setDataDetail(res);
        toast.success('Broadcast berhasil disetujui');
        setShowModalOpen(undefined);
        return res;
      }),
  });

  const mutationReject = useMutation({
    mutationFn: async () =>
      await broadcastRepository.rejectBroadcast(id || '', reason).then((res) => {
        if (res) setDataDetail(res);
        toast.success('Broadcast berhasil ditolak');
        setShowModalOpen(undefined);
        return res;
      }),
  });

  const mutationSend = useMutation({
    mutationFn: async () =>
      await broadcastRepository.sendBroadcast(id || '').then((res) => {
        if (res) setDataDetail(res);
        toast.success('Broadcast berhasil dikirim');
        setShowModalOpen(undefined);
        return res;
      }),
  });

  function onCloseModalReject() {
    setShowModalOpen(undefined);
    setReason('');
  }

  return {
    queryDetail,
    showModalOpen,
    setShowModalOpen,
    mutationApprove,
    dataDetail,
    onCloseModalReject,
    mutationReject,
    reason,
    setReason,
    mutationSend,
  };
}
