// components/ui/IconContainer.tsx
import * as Icons from 'lucide-react';

type IconName = keyof typeof Icons;
type ColorVariant = 'blue' | 'yellow' | 'amber' | 'pink' | 'green' | 'red' | 'gray' | 'purple' | 'orange' | 'teal';

interface IconContainerProps {
  icon?: IconName;
  variant?: ColorVariant;
  size?: number;
}

const colorVariants: Record<ColorVariant, { bg: string; text: string; border: string }> = {
  blue: {
    bg: 'bg-blue-50',
    text: 'text-blue-500',
    border: 'border-blue-500',
  },
  yellow: {
    bg: 'bg-yellow-50',
    text: 'text-yellow-500',
    border: 'border-yellow-500',
  },
  amber: {
    bg: 'bg-amber-50',
    text: 'text-amber-500',
    border: 'border-amber-500',
  },
  pink: {
    bg: 'bg-pink-50',
    text: 'text-pink-500',
    border: 'border-pink-500',
  },
  green: {
    bg: 'bg-green-50',
    text: 'text-green-500',
    border: 'border-green-500',
  },
  red: {
    bg: 'bg-red-50',
    text: 'text-red-500',
    border: 'border-red-500',
  },
  gray: {
    bg: 'bg-gray-50',
    text: 'text-gray-500',
    border: 'border-gray-500',
  },
  purple: {
    bg: 'bg-purple-50',
    text: 'text-purple-500',
    border: 'border-purple-500',
  },
  orange: {
    bg: 'bg-orange-50',
    text: 'text-orange-500',
    border: 'border-orange-500',
  },
  teal: {
    bg: 'bg-teal-50',
    text: 'text-teal-500',
    border: 'border-teal-500',
  },
};

export default function IconContainer({ icon = 'Info', variant = 'amber', size = 16 }: IconContainerProps) {
  const LucideIcon = Icons[icon] as any;
  const { bg, text } = colorVariants[variant];

  return (
    <div className={`h-10 w-10 flex items-center justify-center rounded-md  ${bg} ${text} `}>
      <LucideIcon size={size} />
    </div>
  );
}
