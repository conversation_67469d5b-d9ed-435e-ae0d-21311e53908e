export interface IResRecapHistoryAttendance {
 id: string;
  account_id: string;
  account_nip: string;
  account_name: string;
  account_profile_picture: string;
  work_unit_id: string;
  work_unit_name: string;
  agency_id: string;
  agency_name: string;
  time_table_type: string;
  time_table_name: string;
  time_table_id: string;
  date: string; // format: 'YYYY-MM-DD'
  time: string; // format: 'HH:mm:ss'
  lat: number | null;
  lng: number | null;
  record_start_time: string | null; // format: 'HH:mm:ss' or null
  record_end_time: string | null;   // format: 'HH:mm:ss' or null
  time_gap: number | null;
  date_time: string; // ISO format e.g., '2025-07-22T00:00:00'
  status: string | null;
  reason_type: string | null;
  reason_type_string: string | null;
  shift_name: string;
  shift_id: string;
  shift_start_time: string; // format: 'HH:mm:ss'
  shift_end_time: string;   // format: 'HH:mm:ss'
  attendance_type_enum: string;
  attendance_type_string: string;
  reason: string | null;
  is_attending: boolean;
}