import type { EmployeeStatusType } from '../type/EmployeeStatusType';
import type { PrivilegeType } from '../type/PrivilegeType';

export interface IResGetMe {
  id: string;
  email: string;
  role: string;
  profile_picture: string;
  name: string;
  status: EmployeeStatusType;
  work_unit_id: string;
  work_unit_name: string;
  agency_id: string;
  agency_name: string;
  privileges: PrivilegeType[];
}
