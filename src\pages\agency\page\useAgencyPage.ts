import { useEffect, useState } from 'react';
import { useAgencyRepository } from './useAgencyRepository';
import type { IResListAgency } from '@/types/response/IResListAgency';

export function useAgencyPage() {

  const [searchValue, setSearchValue] = useState<string>('');
  const [dataList, setDataList] = useState<IResListAgency[]>([]);
  const repository = useAgencyRepository();

  useEffect(() => {
      if (repository.listAgency.data) {
        if(!searchValue){
          setDataList(repository.listAgency.data);
        } else {
          setDataList(
            repository.listAgency.data.filter((e) => {
              return e.name.toLowerCase().includes(searchValue.toLowerCase());
            }),
          );
        }
      }
  }, [searchValue, repository.listAgency.data]);


  function handleReset(){
    setSearchValue('');
  }

  
  const loadingList = repository.listAgency?.isFetching;
  return { dataList, loadingList, setSearchValue, searchValue, handleReset };
}
