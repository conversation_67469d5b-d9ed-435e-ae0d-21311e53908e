import { useEffect, useState } from 'react';
import { useAgencyRepository } from './useAgencyRepository';
import type { IResListAgency } from '@/types/response/IResListAgency';

export function useAgencyPage() {

  const [searchValue, setSearchValue] = useState<string>('');
  const [dataList, setDataList] = useState<IResListAgency[]>([]);
  const repository = useAgencyRepository();

  useEffect(() => {
      if (repository.listAgencyAdmin.data) {
        if(!searchValue){
          setDataList(repository.listAgencyAdmin.data);
        } else {
          setDataList(
            repository.listAgencyAdmin.data.filter((e) => {
              return e.name.toLowerCase().includes(searchValue.toLowerCase());
            }),
          );
        }
      }
  }, [searchValue, repository.listAgencyAdmin.data]);


  function handleReset(){
    setSearchValue('');
  }

  
  const loadingList = repository.listAgency?.isFetching;
  return { dataList, loadingList, setSearchValue, searchValue, handleReset };
}
