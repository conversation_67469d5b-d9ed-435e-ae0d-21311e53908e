import { HttpService } from '@/services/http.service.ts';
import ErrorService from '@/services/error.service.ts';
import { useAppDispatch } from '@/redux/store.ts';
import { useQuery } from '@tanstack/react-query';
import { ENDPOINT } from '@/constants/endpoint.ts';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { ILabelValue } from '@/types/type/ILabelValue.ts';
import { MasterDataAction } from '@/redux/actions/master-data.action.ts';

export function useOnLoad() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const masterDataAction = new MasterDataAction();
  const dispatch = useAppDispatch();

  useQuery({
    queryKey: ['list_religion'],
    queryFn: () =>
      httpService
        .GET(ENDPOINT.LIST_RELIGION())
        .then((res: BaseResponse<ILabelValue<string>[]>) => {
          dispatch(masterDataAction.listReligion(res.data.response_data));
          return [];
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        }),
  });

  useQuery({
    queryKey: ['list_marital_status'],
    queryFn: () =>
      httpService
        .GET(ENDPOINT.LIST_MARITAL_STATUS())
        .then((res: BaseResponse<ILabelValue<string>[]>) => {
          dispatch(masterDataAction.listMaritalStatus(res.data.response_data));
          return [];
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        }),
  });
  return {};
}
