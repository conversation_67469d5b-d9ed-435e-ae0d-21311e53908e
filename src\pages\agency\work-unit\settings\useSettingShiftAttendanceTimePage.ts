import { useNavigate, useParams } from 'react-router-dom';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { IReqSettingShiftAttendanceTime } from '@/types/request/IReqSettingShiftAttendanceTime.ts';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { normalizeTime } from '@/lib/utils.ts';
import { useUi } from '@/hooks/useUi.ts';
import * as Yup from 'yup';
export function useSettingShiftAttendanceTimePage() {
  const { id } = useParams();
  const { toast } = useUi();
  const navigate = useNavigate();

  const workUnitRepository = new WorkUnitRepository();
  const [loadingDelete, setLoadingDelete] = useState<string | undefined>(undefined);

  const initState: { data: IReqSettingShiftAttendanceTime[]; checked: boolean } = {
    checked: false,
    data: [],
  };

  const settingShiftAttendanceSchema = Yup.object().shape({
    checked: Yup.boolean(),
    data: Yup.array().of(
      Yup.object().shape({
        shift_id: Yup.string().required('Shift ID wajib diisi'),
        name: Yup.string().required('Nama shift wajib diisi'),
        code: Yup.string().required('Kode shift wajib diisi'),
        start_time: Yup.string().required('Jam mulai wajib diisi'),
        end_time: Yup.string().required('Jam selesai wajib diisi'),
        data: Yup.array()
          .of(
            Yup.object().shape({
              id: Yup.string().optional(),
              name: Yup.string().required('Nama wajib diisi'),
              start_time: Yup.string().required('Jam mulai wajib diisi'),
              end_time: Yup.string().required('Jam selesai wajib diisi'),
            }),
          )
          .min(1, 'Minimal satu waktu hadir harus diisi')
          .required('Waktu hadir tidak boleh kosong'),
      }),
    ),
  });

  const mutationSubmit = useMutation({
    mutationFn: (e: any) =>
      workUnitRepository.settingShiftAttendanceTime(id || '', e).then(() => {
        queryShift.refetch().then();
        queryLisTimeTable.refetch().then();
        toast.success('Jadwal absensi berhasil diubah');
        navigate(-1);
      }),
  });

  const formik = useFormik({
    initialValues: initState,
    validationSchema: settingShiftAttendanceSchema,
    onSubmit: (e) => {
      const data: any[] = [];
      e.data.map((item) => {
        item.data.map((v) => {
          const d = {
            id: v.id,
            shift_id: item.shift_id,
            name: v.name,
            start_time: normalizeTime(v.start_time),
            end_time: normalizeTime(v.end_time),
          };
          data.push(d);
        });
      });
      mutationSubmit.mutate(data);
    },
  });

  const queryShift = useQuery({
    queryKey: ['shift_work_unit', id],
    queryFn: () => workUnitRepository.getListShift(id || ''),
  });

  const queryLisTimeTable = useQuery({
    queryKey: ['schedule_list', id],
    queryFn: () => workUnitRepository.getTimeTableSchedule(id || ''),
  });

  const dataShift = queryShift.data || [];
  const listTimeTable = queryLisTimeTable.data || [];

  useEffect(() => {
    if (!dataShift || dataShift.length === 0) return;

    const mappedData = dataShift.map((shift) => {
      const matchingTimeData = listTimeTable ? listTimeTable.filter((entry) => entry.shift_id === shift.id) : [];

      return {
        name: shift.name,
        end_time: shift.end_time,
        start_time: shift.start_time,
        shift_id: shift.id,
        code: shift.code,
        data:
          matchingTimeData.length > 0
            ? matchingTimeData.map((item) => ({
                name: item.name,
                id: item.id,
                start_time: item.start_time,
                end_time: item.end_time,
              }))
            : [
                {
                  id: '',
                  name: '',
                  start_time: '',
                  end_time: '',
                },
              ],
      };
    });

    formik.setFieldValue('data', mappedData);
  }, [dataShift, listTimeTable]);

  async function onRemove(i: number, j: number) {
    setLoadingDelete(i.toString() + j.toString());
    const newData = [...formik.values.data];
    const findDataId = formik.values.data[i]?.data[j]?.id;
    if (findDataId) {
      workUnitRepository.deleteSchedule(findDataId).then(() => {
        if (Array.isArray(newData[i]?.data)) {
          newData[i].data.splice(j, 1);
          formik.setFieldValue('data', newData);
          setLoadingDelete(undefined);
        }
      });
    }

    setLoadingDelete(undefined);
  }

  return { dataShift, queryShift, listTimeTable, queryLisTimeTable, formik, onRemove, loadingDelete, mutationSubmit };
}
