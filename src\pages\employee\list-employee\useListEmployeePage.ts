import { initialFilterTable } from '@/constants/data-constans';
import { useAuth } from '@/hooks/use-auth';
import { AgencyRepository } from '@/repositories/agency-repository';
import { EmployeeRepository } from '@/repositories/employee-repostiory';
import { MasterDataRepository } from '@/repositories/master-data-repository';
import type { EmployeeStatusType } from '@/types/type/EmployeeStatusType';
import type { IFilterList } from '@/types/type/IFilterList';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

export interface EmployeeFilter {
  agency_id?: string;
  q?: string;
  page: number;
  size: number;
}

export function useListEmployeePage() {
  const employeeRepository = new EmployeeRepository();
  const agencyRepository = new AgencyRepository();
  const masterDataRepository = new MasterDataRepository();
  const auth = useAuth();

  const getInitialFilter = (): IFilterList => {
    return {
      agency_id: undefined,
      q: undefined,
      page: 0,
      size: 10,
      status: undefined,
      role: auth.user?.role === 'MAYOR' ? 'PD_HEAD' : undefined,
    };
  };

  const [openFilter, setOpenFilter] = useState<boolean>(false);
  const [filterData, setFilterData] = useState<IFilterList>(getInitialFilter());
  const [searchValue, setSearchValue] = useState(filterData.q);

  const queryListAgency = useQuery({
    queryKey: ['list_agency_page_and_filter'],
    queryFn: async () => agencyRepository.listAgency(),
  });

  const querySummary = useQuery({
    queryKey: ['list_employee_summar'],
    queryFn: async () => await employeeRepository.getEmployeeSummary(),
  });

  const queryRole = useQuery({
    queryKey: ['list_all_role'],
    queryFn: async () => await masterDataRepository.listAllRole(),
  });

  const queryList = useQuery({
    queryKey: ['list_employe_admin', filterData],
    queryFn: async () => await employeeRepository.listEmployee(filterData),
  });

  function submitFilter() {
    setOpenFilter(false);
    queryList.refetch();
  }

  function handleResetFilter() {
    setSearchValue('');
    setOpenFilter(false);
    setFilterData({
      agency_id: '',
      q: '',
      page: 0,
      size: 10,
    });
  }

  function handleSearch() {
    const searchText = searchValue;
    if (searchText) {
      setFilterData((prev) => ({
        ...prev,
        q: searchText,
        page: 0,
      }));
    }
  }

  function handlePaginationChange(params: { page?: number; size?: number }) {
    setFilterData((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }

  function handleResetSearch() {
    setSearchValue('');
    setFilterData((prev) => ({
      ...prev,
      q: '',
      page: 0,
    }));
  }

  const dataList = queryList?.data?.response_data as any || [];
  const dataAgencyFilter: ILabelValue<string>[] = (queryListAgency.data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });
  const loading = queryList.isPending;

  function isActiveSearch() {
    return !!filterData.agency_id || !!filterData.q;
  }

  const dataRole: ILabelValue<string | undefined>[] = [
    {
      label: 'Semua',
      value: 'all',
    },
    ...((Array.isArray(queryRole?.data) ? queryRole.data : []) as Array<{ name: string; role_enum: string }>).map(
      (e) => ({
        label: e.name,
        value: e.role_enum,
      }),
    ),
  ];

  function onChangeFilterRole(e?: string) {
    setFilterData({ ...filterData, page: 0, role: e === 'all' ? undefined : e });
  }

  function onChangeFilter(e: EmployeeStatusType) {
    setFilterData(
      filterData.status === e && filterData.status ? initialFilterTable : { ...initialFilterTable, status: e },
    );
  }

  return {
    dataList,
    openFilter,
    setOpenFilter,
    dataAgencyFilter,
    filterData,
    setFilterData,
    loading,
    handleResetSearch,
    submitFilter,
    handleResetFilter,
    handleSearch,
    handlePaginationChange,
    isActiveSearch,
    searchValue,
    setSearchValue,
    dataRole,
    onChangeFilterRole,
    onChangeFilter,
    querySummary,
    queryList
  };
}
