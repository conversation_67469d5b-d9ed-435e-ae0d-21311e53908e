import type { ColorType } from '@/types/type/ColorType';
import type { LucideIcon } from 'lucide-react';

interface IProps {
  label: string;
  description?: string;
  value?: string;
  icon?: LucideIcon;
  color?: ColorType;
}

export default function CardSummaryCount(props: IProps) {
  const { label, description, value, icon: Icon, color = 'blue' } = props;

  const colorVariants = {
    red: {
      bg: 'bg-red-50 ',
      border: 'border-red-300',
      iconBg: 'bg-red-100',
      iconColor: 'text-red-600',
      valueColor: 'text-red-700',
      labelColor: 'text-red-800',
      descColor: 'text-red-600',
    },
    green: {
      bg: 'bg-green-50 ',
      border: 'border-green-300',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600',
      valueColor: 'text-green-700',
      labelColor: 'text-green-800',
      descColor: 'text-green-600',
    },
    yellow: {
      bg: 'bg-yellow-50 ',
      border: 'border-yellow-300',
      iconBg: 'bg-yellow-100',
      iconColor: 'text-yellow-600',
      valueColor: 'text-yellow-700',
      labelColor: 'text-yellow-800',
      descColor: 'text-yellow-600',
    },
    purple: {
      bg: 'bg-purple-50 ',
      border: 'border-purple-300',
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600',
      valueColor: 'text-purple-700',
      labelColor: 'text-purple-800',
      descColor: 'text-purple-600',
    },
    blue: {
      bg: 'bg-blue-50 ',
      border: 'border-blue-300',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600',
      valueColor: 'text-blue-700',
      labelColor: 'text-blue-800',
      descColor: 'text-blue-600',
    },
    gray: {
      bg: 'bg-gray-50 ',
      border: 'border-gray-300',
      iconBg: 'bg-gray-100',
      iconColor: 'text-gray-600',
      valueColor: 'text-gray-700',
      labelColor: 'text-gray-800',
      descColor: 'text-gray-600',
    },
    orange: {
      bg: 'bg-orange-50 ',
      border: 'border-orange-300',
      iconBg: 'bg-orange-100',
      iconColor: 'text-orange-600',
      valueColor: 'text-orange-700',
      labelColor: 'text-orange-800',
      descColor: 'text-orange-600',
    },
  };

  const currentTheme = colorVariants[color];

  return (
    <div
      className={`
        bg-white
      ${currentTheme.border} 
      border-1
      rounded-sm 
      relative
      p-3
      overflow-hidden
    `}
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-24 h-24 opacity-10">
        <div
          className={`${currentTheme.iconBg} w-full h-full rounded-full transform translate-x-8 -translate-y-8`}
        ></div>
      </div>

      <div className="relative z-10">
        {/* Header with icon and label */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {Icon && <Icon className={`w-6 h-6 ${currentTheme.iconColor}`} />}
            <div>
              <h3
                className={`
                text-sm 
                font-medium 
                ${currentTheme.labelColor} 
                mb-1
              `}
              >
                {label}
              </h3>
              {description && (
                <p
                  className={`
                  text-xs 
                  ${currentTheme.descColor}
                  opacity-80
                `}
                >
                  {description}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="mt-4">
          <div
            className={`
            text-3xl 
            font-bold 
            ${currentTheme.valueColor}
            leading-none
          `}
          >
            {value || '0'}
          </div>
        </div>
      </div>
    </div>
  );
}
