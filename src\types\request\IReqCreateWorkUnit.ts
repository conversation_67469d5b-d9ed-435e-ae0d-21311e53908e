export interface ITimeTable {
  name: string;
  start_time: string;
  end_time: string;
}

export interface IShift {
  name: string;
  start_time: string;
  end_time: string;
  time_tables: ITimeTable[];
}

export interface ILocation {
  lat: number;
  lng: number;
  radius: number;
  name: string;
}

export interface IReqCreateWorkUnit {
  name: string;
  description: string;
  agency_id: string;
  type: 'REGULAR' | 'SHIFT';
  checked?: boolean;
  timetable: ITimeTable[];
  shifts?: IShift[];
  location: {
    lat?: number;
    lng?: number;
    radius?: number;
    name: string;
  }[];
  day: {
    index: number;
    label: string;
    data: {
      name: string;
      start_time: string;
      end_time: string;
    }[];
  }[];
}
