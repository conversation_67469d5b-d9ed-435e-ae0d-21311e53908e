import Confirmation<PERSON>heckBox from '@/components/ConfirmationCheckbox';
import IconContainer from '@/components/IconContainer';
import InputRadioGroup from '@/components/InputRadioGroup';
import InputText from '@/components/InputText';
import InputTextArea from '@/components/InputTextArea';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { dataTimeTableType } from '@/constants/data-constans';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { FormikProvider } from 'formik';
import { Link } from 'react-router-dom';
import { useFormWorkUnit } from './useFormWorkUnit';

export default function FormWorkUnit() {
  const page = useFormWorkUnit();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Perangkat daerah',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: 'Detail Perangkat daerah',
      path: ROUTES.DETAIL_AGENCY(page.agencyId || ''),
    },
    {
      label: page.id ? 'Edit unit kerja' : 'Detail Perangkat daerah',
      path: ROUTES.DETAIL_AGENCY(page.agencyId || ''),
    },
  ];

  return (
    <PageContainer>
      <AlertDialog open={page.showSuccess}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Berhasil</AlertDialogTitle>
            <AlertDialogDescription>
              {page?.id ? 'Unit kerja berhasil diupdate' : 'Unit kerja berhasil ditambahkan'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          {page?.id ? (
            <AlertDialogFooter className="grid ">
              <Link className="flex-1 w-full " to={ROUTES.DETAIL_WORK_UNIT(page?.id || '')}>
                <AlertDialogCancel className="w-full">Kembali</AlertDialogCancel>
              </Link>
            </AlertDialogFooter>
          ) : (
            <AlertDialogFooter className="grid grid-cols-2">
              <Link className="flex-1 w-full " to={ROUTES.DETAIL_AGENCY(page.agencyId || '')}>
                <AlertDialogCancel className="w-full">Kembali ke Perangkat daerah</AlertDialogCancel>
              </Link>
              {page?.mutateCreate?.data?.id && (
                <Link className="flex-1 w-full " to={ROUTES.DETAIL_WORK_UNIT(page?.mutateCreate?.data?.id || '')}>
                  <AlertDialogAction className="w-full">Lihat detail unit kerja</AlertDialogAction>
                </Link>
              )}
            </AlertDialogFooter>
          )}
        </AlertDialogContent>
      </AlertDialog>
      <PageTitle title={page.id ? 'Edit unit kerja' : 'Tambah Unit Kerja'} breadcrumb={breadcrumb} />
      <Card className=" overflow-hidden">
        <CardContent className="bg-primary/ ">
          <div className="flex gap-3 items-center">
            <IconContainer icon="Building" variant="green" />
            <div>
              <div className="font-bold ">Informasi Unit Kerja</div>
              <p>
                {page?.id
                  ? 'Lengkapi form di bawah untuk mengedit unit kerja '
                  : 'Lengkapi form di bawah untuk menambahkan unit kerja baru'}
              </p>
            </div>
          </div>
        </CardContent>
        <Separator />
        <CardContent>
          <FormikProvider value={page.formik}>
            <div className="grid gap-3">
              <InputText id="name" name="name" placeholder="Masukan nama unit kerja" label="Nama unit kerja" required />
              <InputTextArea
                id="description"
                name="description"
                placeholder="Masukan deskripsi unit kerja"
                label="Deskripsi"
              />
              {!page?.id && (
                <InputRadioGroup
                  orientation="horizontal"
                  id="type"
                  name="type"
                  options={dataTimeTableType}
                  label="Tipe Unit kerja"
                  required
                  gridCols={2}
                />
              )}
              <div className="py-5">
                <ConfirmationCheckBox
                  label="Konfirmasi"
                  name="checked"
                  onCheckedChange={(e) => page.formik.setFieldValue('checked', e)}
                  checked={page.formik.values.checked}
                  description={
                    page?.id
                      ? 'Saya yakin untuk memperbarui data unit kerja dengan informasi yang telah dimasukkan'
                      : 'Saya yakin untuk menambahkan unit kerja baru dengan informasi yang telah dimasukkan'
                  }
                />
              </div>
              <Button
                loading={page?.mutateCreate?.isPending || page?.mutationEdit?.isPending}
                disabled={!(page.formik.isValid && page.formik.values.checked && page.formik.values.name)}
                onClick={() => page.formik.handleSubmit()}
              >
                Kirim
              </Button>
            </div>
          </FormikProvider>
        </CardContent>
      </Card>
    </PageContainer>
  );
}
