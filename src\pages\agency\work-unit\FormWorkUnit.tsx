import InputRadioGroup from '@/components/InputRadioGroup';
import InputText from '@/components/InputText';
import InputTextArea from '@/components/InputTextArea';
import MapsSearch from '@/components/MapsSearch';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { dataTimeTableType } from '@/constants/data-constans';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { FieldArray, FormikProvider } from 'formik';
import { AlertCircleIcon, Plus, Trash2 } from 'lucide-react';
import { MdAdd, MdClose } from 'react-icons/md';
import { useParams } from 'react-router-dom';
import { useFormWorkUnit } from './useFormWorkUnit';

export default function FormWorkUnit() {
  const { agencyId } = useParams();
  const page = useFormWorkUnit();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: page?.dataDetail?.name || '',
      path: ROUTES.DETAIL_AGENCY(agencyId || ''),
    },
    {
      label: `Unit Kerja `,
      path: ROUTES.DETAIL_AGENCY(agencyId || ''),
    },
    {
      label: 'Tambahkan unit Kerja',
      path: ROUTES.DETAIL_AGENCY(agencyId || ''),
    },
  ];

  const accordionData = [
    {
      label: 'Informasi Unit Kerja',
      content: informationForm(),
    },
    {
      label: 'Jadwal',
      content: ScheduleForm(),
    },
    {
      label: 'Lokasi absensi dan radius',
      content: MapForm(),
    },
  ];

  function informationForm() {
    return (
      <div className="grid gap-3">
        <InputText name="name" id="name" label="Nama unit kerja" placeholder="Masukan nama unit kerja" required />
        <InputTextArea
          name="description"
          id="description"
          label="description"
          placeholder="Masukan deskripsi (opsional)"
        />
        <InputRadioGroup
          gridCols={2}
          required
          orientation="horizontal"
          name="type"
          options={dataTimeTableType}
          label="Tipe penjadwalan"
          id="schedule_type"
        />
        <div className="flex justify-between">
          <div className="flex items-center gap-2">
            {page.formik.values.type === 'SHIFT' && (
              <div className="text-sm text-muted-foreground bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg">
                💡 Mode Shift: Anda akan mengatur shift kerja dan jadwal absensi untuk setiap shift
              </div>
            )}
            {page.formik.values.type === 'REGULAR' && (
              <div className="text-sm text-muted-foreground bg-green-50 dark:bg-green-900/20 px-3 py-2 rounded-lg">
                📅 Mode Regular: Anda akan mengatur jadwal absensi harian
              </div>
            )}
          </div>
          <Button
            onClick={() => page.setActiveForm('1')}
            disabled={!(page.formik.values.agency_id && page.formik.values.name && page.formik.values.type)}
          >
            Selanjutnya
          </Button>
        </div>
      </div>
    );
  }

  function MapForm() {
    const { values, setFieldValue } = page.formik;

    const addLocation = () => {
      const newLocation = {
        name: '',
        radius: '',
        lat: null,
        lng: null,
      };
      setFieldValue('location', [...values.location, newLocation]);
    };

    const removeLocation = (index: number) => {
      // Pastikan minimal ada 1 item yang tersisa
      if (values.location.length > 1) {
        const updatedLocations = values.location.filter((_, i) => i !== index);
        setFieldValue('location', updatedLocations);
      }
    };

    const updateLocationCoords = (index: number, coords: { lat?: number; lng?: number }) => {
      setFieldValue(`location[${index}].lat`, coords.lat);
      setFieldValue(`location[${index}].lng`, coords.lng);
    };

    return (
      <div className="space-y-4">
        {values.location.map((item, i) => (
          <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900/50" key={i}>
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Lokasi {i + 1}</h3>
              <button
                type="button"
                onClick={() => removeLocation(i)}
                disabled={values.location.length === 1}
                className={`p-1.5 rounded-md transition-colors cursor-pointer ${
                  values.location.length === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700'
                }`}
                title={values.location.length === 1 ? 'Minimal harus ada 1 lokasi' : 'Hapus lokasi'}
              >
                <Trash2 size={16} />
              </button>
            </div>

            <div className="grid gap-3">
              <div className="grid grid-cols-2 gap-3">
                <InputText
                  name={`location[${i}].name`}
                  id={`name-${i}`}
                  label="Nama Lokasi"
                  placeholder="Masukan nama lokasi"
                  required
                />
                <InputText
                  name={`location[${i}].radius`}
                  id={`radius-${i}`}
                  label="Masukan radius"
                  placeholder="Masukan radius dalam meter"
                  required
                  type="number"
                  endIcon={<div className="text-sm text-gray-500">M</div>}
                />
              </div>

              <MapsSearch
                radius={item.radius || undefined}
                onChange={(coords) => updateLocationCoords(i, coords)}
                value={{ lat: item.lat, lng: item.lng }}
              />
            </div>
          </div>
        ))}

        <button
          type="button"
          onClick={addLocation}
          className="w-full flex cursor-pointer items-center justify-center gap-2 py-3 px-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-blue-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
        >
          <Plus size={18} />
          <span>Tambah Lokasi</span>
        </button>
      </div>
    );
  }

  function ScheduleForm() {
    const { values } = page.formik;

    return (
      <div className="space-y-6">
        {/* Header Section */}
        <div className="text-center space-y-2">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r bg-primary rounded-lg mb-3">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-foreground">
            {values.type === 'SHIFT' ? 'Pengaturan Shift Kerja' : 'Pengaturan Jadwal Absensi'}
          </h3>
          <p className="text-sm text-muted-foreground">
            {values.type === 'SHIFT'
              ? 'Atur shift kerja dan jadwal absensi untuk setiap shift'
              : 'Atur waktu absensi dan toleransi untuk setiap hari kerja'}
          </p>
        </div>

        {values.type === 'SHIFT' ? renderShiftForm() : renderRegularForm()}
      </div>
    );
  }

  function renderShiftForm() {
    const { values, setFieldValue } = page.formik;

    const addShift = () => {
      const newShift = {
        name: '',
        start_time: '',
        end_time: '',
        time_tables: [
          {
            name: 'Jam Masuk',
            start_time: '',
            end_time: '',
          },
          {
            name: 'Jam Pulang',
            start_time: '',
            end_time: '',
          },
        ],
      };
      setFieldValue('shifts', [...(values.shifts || []), newShift]);
    };

    const removeShift = (index: number) => {
      if ((values.shifts || []).length > 1) {
        const updatedShifts = (values.shifts || []).filter((_, i) => i !== index);
        setFieldValue('shifts', updatedShifts);
      }
    };

    const addTimeTable = (shiftIndex: number) => {
      const newTimeTable = {
        name: '',
        start_time: '',
        end_time: '',
      };
      const currentShifts = [...(values.shifts || [])];
      currentShifts[shiftIndex].time_tables.push(newTimeTable);
      setFieldValue('shifts', currentShifts);
    };

    const removeTimeTable = (shiftIndex: number, timeTableIndex: number) => {
      const currentShifts = [...(values.shifts || [])];
      if (currentShifts[shiftIndex].time_tables.length > 1) {
        currentShifts[shiftIndex].time_tables.splice(timeTableIndex, 1);
        setFieldValue('shifts', currentShifts);
      }
    };

    return (
      <div className="space-y-6">
        {(values.shifts || []).map((shift, shiftIndex) => (
          <Card key={shiftIndex} className="relative">
            {(values.shifts || []).length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                type="button"
                onClick={() => removeShift(shiftIndex)}
                className="absolute top-2 right-2 h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 z-10"
              >
                <MdClose className="w-3 h-3" />
              </Button>
            )}

            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-medium text-sm">
                    {shiftIndex + 1}
                  </div>
                  <h4 className="font-medium text-foreground">Shift {shiftIndex + 1}</h4>
                </div>

                {/* Shift Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <InputText
                    name={`shifts[${shiftIndex}].name`}
                    label="Nama Shift"
                    placeholder="Contoh: Shift Pagi"
                    required
                  />
                  <InputText name={`shifts[${shiftIndex}].start_time`} label="Jam Mulai Shift" type="time" required />
                  <InputText name={`shifts[${shiftIndex}].end_time`} label="Jam Selesai Shift" type="time" required />
                </div>

                {/* Time Tables */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium text-sm text-foreground">Jadwal Absensi</h5>
                    <Button type="button" variant="outline" size="sm" onClick={() => addTimeTable(shiftIndex)}>
                      <Plus size={16} className="mr-1" />
                      Tambah Jadwal
                    </Button>
                  </div>

                  {shift.time_tables.map((_timeTable, timeTableIndex) => (
                    <Card key={timeTableIndex} className="relative bg-muted/30">
                      {shift.time_tables.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          type="button"
                          onClick={() => removeTimeTable(shiftIndex, timeTableIndex)}
                          className="absolute top-2 right-2 h-6 w-6 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 size={12} />
                        </Button>
                      )}

                      <CardContent className="pt-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <InputText
                            name={`shifts[${shiftIndex}].time_tables[${timeTableIndex}].name`}
                            label="Nama Absensi"
                            placeholder="Contoh: Jam Masuk"
                            required
                          />
                          <InputText
                            name={`shifts[${shiftIndex}].time_tables[${timeTableIndex}].start_time`}
                            label="Waktu Mulai"
                            type="time"
                            required
                          />
                          <InputText
                            name={`shifts[${shiftIndex}].time_tables[${timeTableIndex}].end_time`}
                            label="Waktu Selesai"
                            type="time"
                            required
                          />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        <div className="flex justify-center">
          <Button type="button" variant="outline" onClick={addShift}>
            <Plus size={16} className="mr-2" />
            Tambah Shift
          </Button>
        </div>
      </div>
    );
  }

  function renderRegularForm() {
    const { values } = page.formik;

    return (
      <Accordion type="single" className="space-y-3" defaultValue={'0'}>
        {values.day &&
          values.day.length >= 1 &&
          values.day.map((dayItem, i) => (
            <AccordionItem
              key={dayItem.index}
              value={dayItem.index.toString()}
              className="border border-border rounded-lg   duration-200 overflow-hidden bg-card"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 transition-colors duration-200 [&[data-state=open]]:bg-accent/70">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r bg-primary rounded-full text-white font-medium text-sm">
                    {dayItem.label.charAt(0).toUpperCase()}
                  </div>
                  <div className="text-left">
                    <h4 className="font-medium text-foreground">{dayItem.label}</h4>
                    <p className="text-xs text-muted-foreground">
                      {dayItem.data.length > 0 ? `${dayItem.data.length} jadwal absensi` : 'Belum ada jadwal'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>

              <AccordionContent className="px-4 pb-4 bg-muted/30">
                <FieldArray name={`day[${i}].data`}>
                  {({ push, remove }) => (
                    <div className="space-y-3">
                      {dayItem.data.map((_dataItem, j) => (
                        <Card key={j} className="relative shadow-none bg-card duration-200">
                          {dayItem.data.length > 1 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              type="button"
                              onClick={() => remove(j)}
                              className="absolute top-2 right-2 h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 z-10"
                            >
                              <MdClose className="w-3 h-3" />
                            </Button>
                          )}

                          <div className="absolute top-2 left-2 text-xs font-medium px-2 py-0.5 rounded-xs">
                            Jadwal {j + 1}
                          </div>

                          <CardContent className="pt-8 pb-4">
                            <div className="space-y-4">
                              {/* Name Input */}
                              <div className="space-y-2">
                                <label className="text-sm font-medium text-foreground">Nama Absensi</label>
                                <InputText name={`day[${i}].data[${j}].name`} placeholder="Contoh: Clock In" />
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <label className="text-sm font-medium text-foreground flex items-center gap-1">
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                      />
                                    </svg>
                                    Waktu Absen
                                  </label>
                                  <InputText required type="time" name={`day[${i}].data[${j}].start_time`} />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-sm font-medium text-foreground flex items-center gap-1">
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                                      />
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                      />
                                    </svg>
                                    Waktu Toleransi
                                  </label>
                                  <InputText required type="time" name={`day[${i}].data[${j}].end_time`} />
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}

                      <div className="flex justify-center pt-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() =>
                            push({
                              name: '',
                              start_time: '',
                              end_time: '',
                            })
                          }
                        >
                          <MdAdd className="w-4 h-4 mr-2" />
                          Tambah Jadwal
                        </Button>
                      </div>
                    </div>
                  )}
                </FieldArray>
              </AccordionContent>
            </AccordionItem>
          ))}
      </Accordion>
    );
  }

  function alertValidation() {
    const formik = page.formik;
    return (
      <>
        {Object.keys(formik.errors).length > 0 && formik.submitCount > 0 && (
          <Alert variant="destructive">
            <AlertCircleIcon />
            <AlertTitle>Formulir tidak valid.</AlertTitle>
            <AlertDescription>
              <p>Periksa kembali isian berikut:</p>
              <ul className="list-inside list-disc text-sm mt-2">
                {Object.entries(formik.errors).map(([key, value], i) => {
                  if (typeof value === 'string') {
                    return <li key={i}>{value}</li>;
                  }

                  if (key === 'day' && Array.isArray(value)) {
                    return value.map((dayItem: any, dayIndex: number) => {
                      if (!dayItem || typeof dayItem !== 'object') return null;
                      return dayItem.data?.map((dataItem: any, dataIndex: number) => {
                        if (!dataItem || typeof dataItem !== 'object') return null;
                        return Object.entries(dataItem).map(([field, message], k) => {
                          return (
                            <li key={`${dayIndex}-${dataIndex}-${k}`}>
                              Hari {dayIndex + 1} - Jadwal {dataIndex + 1} - {field}: {message as string}
                            </li>
                          );
                        });
                      });
                    });
                  }

                  return null;
                })}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </>
    );
  }

  return (
    <PageContainer>
      <PageTitle title="Tambahkan unit kerja" breadcrumb={breadcrumb} />
      <FormikProvider value={page.formik}>
        <Card className="p-0">
          <Accordion
            type="single"
            collapsible
            className="w-full"
            defaultValue={page.activeForm}
            value={page.activeForm}
            onValueChange={page.setActiveForm}
          >
            {accordionData.map((item, i) => (
              <AccordionItem value={i.toString()} key={i}>
                <CardContent>
                  <AccordionTrigger className="font-semibold">{item.label}</AccordionTrigger>
                </CardContent>
                <AccordionContent className="flex flex-col gap-4 text-balance ">
                  <CardContent className="">{item.content}</CardContent>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </Card>
      </FormikProvider>
      {alertValidation()}
      <Card>
        <CardContent className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={page.formik.values.checked}
              onCheckedChange={() => page.formik.setFieldValue('checked', page.formik.values.checked ? false : true)}
              id="terms"
            />
            <div>{'Yakin untuk menambahkan unit kerja beserta jadwal ?'}</div>
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                const formData = page.formik.values;
                const previewData: any = {
                  name: formData.name,
                  description: formData.description,
                  agency_id: formData.agency_id,
                  type: formData.type,
                  location: formData.location.map((loc) => ({
                    ...loc,
                    radius: loc.radius ? parseFloat(loc.radius.toString()) : 200,
                    lat: loc.lat || 0,
                    lng: loc.lng || 0,
                  })),
                };

                if (formData.type === 'REGULAR') {
                  previewData.timetable = formData.day
                    .flatMap((dayItem) =>
                      dayItem.data.map((entry) => ({
                        day: dayItem.index,
                        name: entry.name,
                        start_time: entry.start_time + ':00',
                        end_time: entry.end_time + ':00',
                      })),
                    )
                    .filter((item) => item.name && item.start_time && item.end_time);
                } else if (formData.type === 'SHIFT') {
                  previewData.timetable = [];
                  previewData.shifts = (formData.shifts || []).map((shift) => ({
                    ...shift,
                    start_time: shift.start_time ? shift.start_time + ':00' : '',
                    end_time: shift.end_time ? shift.end_time + ':00' : '',
                    time_tables: shift.time_tables.map((timeTable) => ({
                      ...timeTable,
                      start_time: timeTable.start_time ? timeTable.start_time + ':00' : '',
                      end_time: timeTable.end_time ? timeTable.end_time + ':00' : '',
                    })),
                  }));
                }

                console.log('Preview Data:', JSON.stringify(previewData, null, 2));
                alert('Data preview telah dicetak ke console. Buka Developer Tools untuk melihat.');
              }}
            >
              Preview Data
            </Button>
            <Button
              loading={page.mutationCreate.isPending}
              onClick={() => page.formik.handleSubmit()}
              disabled={!page.formik.values.checked}
            >
              Tambahkan
            </Button>
          </div>
        </CardContent>
      </Card>
    </PageContainer>
  );
}
