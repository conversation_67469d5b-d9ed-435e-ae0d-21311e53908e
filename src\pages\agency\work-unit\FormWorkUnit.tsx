import InputText from '@/components/InputText';
import InputTextArea from '@/components/InputTextArea';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { FormikProvider } from 'formik';
import { useFormWorkUnit } from './useFormWorkUnit';
import { Link, Routes } from 'react-router-dom';

export default function FormWorkUnit() {
  const page = useFormWorkUnit();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: 'Detail Instansi',
      path: ROUTES.DETAIL_AGENCY(page.agencyId || ''),
    },
  ];
  return (
    <PageContainer>
      <AlertDialog open={page.showSuccess}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Berhasil</AlertDialogTitle>
            <AlertDialogDescription>
              Unit kerja baru berhasil di tambahkan, silahkan lihat ke detail, atau kembali ke detail instansi
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="grid grid-cols-2">
            <AlertDialogCancel>Kembali ke instansi</AlertDialogCancel>
            {page?.mutateCreate?.data?.id && (
              <Link
                className="flex-1 w-full "
                to={ROUTES.DETAIL_WORK_UNIT(page.agencyId || '', page.mutateCreate.data?.id || '')}
              >
                <AlertDialogAction className="w-full">Lihat detail unit kerja</AlertDialogAction>
              </Link>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <PageTitle title="Tambah Unit Kerja" breadcrumb={breadcrumb} />
      <Card>
        <CardContent>
          <FormikProvider value={page.formik}>
            <div className="grid gap-3">
              <InputText id="name" name="name" placeholder="Masukan nama unit kerja" label="Nama unit kerja" required />
              <InputTextArea
                id="description"
                name="description"
                placeholder="Masukan deskripsi unit kerja"
                label="Deskripsi"
              />
              <div className="flex gap-2 items-center">
                <Checkbox onCheckedChange={(e) => page.formik.setFieldValue("checked", e) } id="checked" name="checked" />
                <label htmlFor="checked">Konfirmasi</label>
              </div>
              <Button
                disabled={page.formik.isValid && page.formik.values.checked && page.formik.values.name ? false : true}
                onClick={() => page.formik.handleSubmit()}
              >
                Kirim
              </Button>
            </div>
          </FormikProvider>
        </CardContent>
      </Card>
    </PageContainer>
  );
}
