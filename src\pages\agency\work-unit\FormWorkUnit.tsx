import InputText from '@/components/InputText';
import InputTextArea from '@/components/InputTextArea';
import PageContainer from '@/components/PageContainer';
import { Card, CardContent } from '@/components/ui/card';
import { FormikProvider } from 'formik';
import { useFormWorkUnit } from './useFormWorkUnit';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import PageTitle from '@/components/page-title';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { ROUTES } from '@/routes/routes';

export default function FormWorkUnit() {
  const page = useFormWorkUnit();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Instansi',
      path: ROUTES.DETAIL_AGENCY(page.agencyId || ''),
    },
  ];
  return (
    <PageContainer>
      <PageTitle title="Tambah unit kerja baru" breadcrumb={breadcrumb} />
      <Card>
        <CardContent>
          <FormikProvider value={page.formik}>
            <InputText id="name" name="name" required label="Nama" placeholder="Masukan nama unit kerja" />
            <InputTextArea id="description" name="description" label="Deskripsi" placeholder="Masukan deskripsi" />
            <div>
              <Checkbox id="checked" checked={page.formik.values.checked} />
              <label htmlFor="checked">Konfirmasi</label>
            </div>
            <Button onClick={() => page.formik.handleSubmit()}>BUAT</Button>
          </FormikProvider>
        </CardContent>
      </Card>
    </PageContainer>
  );
}
