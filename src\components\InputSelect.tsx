import Label from '@/components/ui/Label.tsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils.ts';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import type { ReactNode } from 'react';

interface IProps<T = any> {
  id: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: T;
  onValueChange?: (value: T) => void;
  options: ILabelValue<T>[];
  disabled?: boolean;
  dataTestId?: string;
  disableFormik?: boolean;
  alignment?: 'horizontal' | 'vertical';
}

export default function InputSelect<T = string>(props: IProps<T>) {
  const formik = !props.disableFormik ? useFormikContext<any>() : undefined;

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  const handleValueChange = (value: string) => {
    // Find the actual value from options
    const selectedOption = props.options.find((option) => String(option.value) === value);
    const actualValue = selectedOption ? selectedOption.value : value;

    if (props.onValueChange) {
      props.onValueChange(actualValue as T);
    }

    // Update formik field
    if (formik) {
      formik.setFieldValue(props.name, actualValue);
    }
  };

  return (
    <div className={props?.alignment === 'horizontal' ? 'grid grid-cols-2' : 'grid'}>
      {props.label && <Label className="flex items-center" label={props.label} required={props.required} />}
      <div className={cn('relative flex items-center')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3 z-10">{props.startIcon}</span>
        )}

        <div className="w-full">
          <Select
            data-testid={props.dataTestId}
            value={String(currentValue)}
            onValueChange={handleValueChange}
            disabled={props.disabled}
          >
            <SelectTrigger
              id={props.id}
              className={cn(
                'w-full cursor-pointer',
                props.startIcon ? 'pl-12' : '',
                props.endIcon ? 'pr-9' : '',
                errorMessage ? 'outline-red-500 border-red-500 bg-red-100' : '',
                'dark:bg-card bg-white',
              )}
            >
              <SelectValue placeholder={props.placeholder || 'Pilih opsi...'} />
            </SelectTrigger>
            <SelectContent>
              {props.options.map((option, index) => (
                <SelectItem className="cursor-pointer" key={index} value={String(option.value)}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {props.endIcon && (
            <span className="absolute text-gray-500 right-3 flex items-center pl-3 z-10">{props.endIcon}</span>
          )}
          {(errorMessage || props.helperText) && (
            <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
              {errorMessage || props.helperText}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
