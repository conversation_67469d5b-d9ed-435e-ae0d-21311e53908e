import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb.tsx';
import { ROUTES } from '@/routes/routes.ts';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { ArrowLeft } from 'lucide-react';
import { Fragment } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';

export default function PageTitle(props: IProps) {
  const navigate = useNavigate();
  const dataBreadcrumb: IBreadcrumbData[] = [{ path: ROUTES.HOME(), label: 'Dashboard' }, ...(props.breadcrumb || [])];
  return (
    <div>
      <div className="flex items-center gap-2">
        <Button onClick={() => navigate(-1)} size={'icon'} variant={'secondary'}>
          <ArrowLeft />
        </Button>
        <h1 className={'capitalize text-2xl'}>{props.title}</h1>
      </div>
      {props.breadcrumb && (
        <Breadcrumb className={'mt-2'}>
          <BreadcrumbList>
            {dataBreadcrumb.map((item: IBreadcrumbData, index) => (
              <Fragment key={index}>
                {index !== 0 && <BreadcrumbSeparator />}
                {index + 1 !== dataBreadcrumb.length ? (
                  <BreadcrumbItem key={index}>
                    <BreadcrumbLink href={item.path}>{item.label}</BreadcrumbLink>
                  </BreadcrumbItem>
                ) : (
                  <BreadcrumbItem>
                    <BreadcrumbPage>{item.label}</BreadcrumbPage>
                  </BreadcrumbItem>
                )}
              </Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      )}
    </div>
  );
}

interface IProps {
  title: string;
  breadcrumb?: IBreadcrumbData[];
}
