import { initialFilterTable } from '@/constants/data-constans';
import { useAuth } from '@/hooks/use-auth';
import { useUi } from '@/hooks/useUi.ts';
import { MasterDataRepository } from '@/repositories/master-data-repository';
import { SubmissionRepository } from '@/repositories/submission-repository.ts';
import type { IResListSubmission } from '@/types/response/IResListSubmission.ts';
import type { IPaginatedParams } from '@/types/response/IResModel.ts';
import type { IFilterList } from '@/types/type/IFilterList.ts';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

export function useListSubmissionPage() {
  const submissionRepository = new SubmissionRepository();
  const masterDataRepository = new MasterDataRepository();
  const auth = useAuth()
  
  const [dataDetail, setDataDetail] = useState<IResListSubmission | undefined>();
  const [loadingApproveReject, setLoadingApproveReject] = useState<boolean>(false);
  const defaultAgencyId = auth?.user?.role !== 'SUPER_ADMIN' ? auth?.user?.agency_id : undefined;

  const initFilter: IFilterList = {
    page: 0,
    role: auth.user?.role === 'MAYOR' ? 'PD_HEAD' : undefined,
    agency_id: defaultAgencyId,
    size: 10,
  };
  const [filter, setFilter] = useState<IFilterList>(initFilter);
  const [searchValue, setSearchValue] = useState<string>('');
  const [openFilter, setOpenFilter] = useState<boolean>(false);

  const { toast } = useUi();
  const queryList = useQuery({
    queryKey: ['list_submission', filter],
    queryFn: async () => await submissionRepository.getListSubmission(filter),
  });


  const queryRole = useQuery({
    queryKey: ['list_all_role'],
    queryFn: async () => await masterDataRepository.listAllRole(),
  });


  const queryAgency = useQuery({
    queryKey: ['list_agency_master_data'],
    queryFn: async () => await masterDataRepository.getAgency(),
  });

  function handleResetFilter() {
    setFilter(initFilter);
  }

  function submitFilter() {
    setOpenFilter(false);
    queryList.refetch();
  }

  function onCloseSubmission() {
    setDataDetail(undefined);
  }

  async function onApproveSubmission(id: string) {
    setLoadingApproveReject(true);
    await submissionRepository.approveSubmission(id).then(() => {
      toast.success('Pengajuan berhasil disetujui');
      queryList.refetch();
      setDataDetail(undefined);
      setLoadingApproveReject(false);
    });
  }

  async function onRejectSubmission(id: string) {
    setLoadingApproveReject(true);
    await submissionRepository.rejectSubmission(id).then(() => {
      toast.success('Pengajuan ditolak disetujui');
      queryList.refetch();
      setDataDetail(undefined);
      setLoadingApproveReject(false);
    });
  }

  function onChangePagination(param: IPaginatedParams) {
    setFilter({
      page: param.page,
      size: param.size,
    });
  }

  function handleSearch() {
    const searchText = searchValue;
    if (searchText) {
      setFilter((prev) => ({
        ...prev,
        q: searchText,
        page: 0,
      }));
    }
  }

  function handleResetSearch() {
    setSearchValue('');
    setFilter((prev) => ({
      ...prev,
      q: '',
      page: 0,
    }));
  }

  const dataAgency: ILabelValue<string>[] = (queryAgency.data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });


  const dataRole: ILabelValue<string | undefined>[] = [
    {
      label: 'Semua',
      value: 'all',
    },
    ...((Array.isArray(queryRole?.data) ? queryRole.data : []) as Array<{ name: string; role_enum: string }>).map(
      (e) => ({
        label: e.name,
        value: e.role_enum,
      }),
    ),
  ];

  function onChangeAgency(e: string) {
    setFilter((prev) => ({
      ...prev,
      ...initialFilterTable,
      work_unit_id: undefined,
      agency_id: e,
    }));
  }

  function onChangeFilterRole(e?: string) {
    if (e !== 'all') {
      setFilter({ ...filter, role: e,  ...initialFilterTable });
    } else {
      setFilter({ ...filter, role: undefined, ...initialFilterTable });
    }
  }

  return {
    onChangeFilterRole,
    queryList,
    openFilter,
    setOpenFilter,
    onCloseSubmission,
    setDataDetail,
    dataDetail,
    onApproveSubmission,
    onRejectSubmission,
    loadingApproveReject,
    onChangePagination,
    searchValue,
    submitFilter,
    handleSearch,
    handleResetSearch,
    filter,
    setSearchValue,
    handleResetFilter,
    dataAgency,
    onChangeAgency,
    dataRole
  };
}
