import type { AttendanceStatusType } from '@/types/type/AttendanceStatusType.ts';

export interface IResListAttendance {
  id: string;
  account_id: string;
  account_nip: string;
  account_name: string;
  work_unit_id: string;
  work_unit_name: string;
  agency_id: string;
  agency_name: string;
  type: string;
  time_table_name: string;
  date: string;
  time: string;
  lat: number;
  lng: number;
  time_gap: number;
  record_start_time: string;
  record_end_time: string;
  date_time: Date;
  status: AttendanceStatusType;
}
