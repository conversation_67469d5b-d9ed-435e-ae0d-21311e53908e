import React from 'react';
import { Formik, Form } from 'formik';
import InputTime from './InputTime';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

// Example usage of the enhanced InputTime component
export default function InputTimeExample() {
  return (
    <div className="p-8 space-y-8 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Enhanced InputTime Component Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Example 1: Basic usage with custom time picker */}
          <div>
            <h3 className="text-lg font-semibold mb-4">1. Basic Time Input with Custom Picker</h3>
            <Formik
              initialValues={{ startTime: '09:00' }}
              onSubmit={(values) => console.log('Form submitted:', values)}
            >
              {({ values }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="startTime"
                    label="Start Time"
                    placeholder="Select start time"
                    required
                    showTimePicker={true} // Enable custom time picker
                  />
                  <div className="text-sm text-gray-600">
                    Current value: {values.startTime || 'Not set'}
                  </div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

          {/* Example 2: Native time picker (fallback) */}
          <div>
            <h3 className="text-lg font-semibold mb-4">2. Native Time Input (24-hour format)</h3>
            <Formik
              initialValues={{ endTime: '17:30' }}
              onSubmit={(values) => console.log('Form submitted:', values)}
            >
              {({ values }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="endTime"
                    label="End Time"
                    placeholder="Select end time"
                    required
                    showTimePicker={false} // Use native time picker
                  />
                  <div className="text-sm text-gray-600">
                    Current value: {values.endTime || 'Not set'}
                  </div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

          {/* Example 3: Custom step and validation */}
          <div>
            <h3 className="text-lg font-semibold mb-4">3. Custom Step (15-minute intervals)</h3>
            <Formik
              initialValues={{ meetingTime: '' }}
              onSubmit={(values) => console.log('Form submitted:', values)}
            >
              {({ values }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="meetingTime"
                    label="Meeting Time"
                    placeholder="Select meeting time"
                    required
                    step={15} // 15-minute intervals
                    helperText="Time will be rounded to 15-minute intervals"
                  />
                  <div className="text-sm text-gray-600">
                    Current value: {values.meetingTime || 'Not set'}
                  </div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

          {/* Example 4: With custom onChange handler */}
          <div>
            <h3 className="text-lg font-semibold mb-4">4. With Custom Change Handler</h3>
            <Formik
              initialValues={{ customTime: '12:00' }}
              onSubmit={(values) => console.log('Form submitted:', values)}
            >
              {({ values, setFieldValue }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="customTime"
                    label="Custom Time"
                    placeholder="Select time"
                    onChange={(time) => {
                      console.log('Time changed:', time);
                      setFieldValue('customTime', time);
                    }}
                  />
                  <div className="text-sm text-gray-600">
                    Current value: {values.customTime || 'Not set'}
                  </div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Key Features:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              <li><strong>24-hour format:</strong> Always displays time in HH:MM format</li>
              <li><strong>Custom time picker:</strong> Visual interface with hour/minute controls</li>
              <li><strong>Keyboard navigation:</strong> Use Space or Arrow Down to open picker</li>
              <li><strong>Click to open:</strong> Click on input field or clock icon</li>
              <li><strong>Outside click to close:</strong> Popup closes when clicking outside</li>
              <li><strong>Responsive design:</strong> Works on desktop and mobile</li>
              <li><strong>Form integration:</strong> Full Formik support with validation</li>
            </ul>
          </div>
          
          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Props:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              <li><code>showTimePicker</code>: Enable/disable custom time picker (default: true)</li>
              <li><code>step</code>: Minute intervals for time selection (default: 1)</li>
              <li><code>format</code>: Time format - always 24-hour regardless of setting</li>
              <li>All existing InputTime props are preserved and supported</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
