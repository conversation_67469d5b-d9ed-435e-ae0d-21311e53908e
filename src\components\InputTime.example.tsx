import { Form, Formik } from 'formik';
import InputTime from './InputTime';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

export default function InputTimeExample() {
  return (
    <div className="p-8 space-y-8 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Material Design Clock Time Picker Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Example 1: Material Design Clock Picker */}
          <div>
            <h3 className="text-lg font-semibold mb-4">1. Material Design Clock Interface</h3>
            <Formik
              initialValues={{ startTime: '09:00' }}
              onSubmit={(values) => console.info('Form submitted:', values)}
            >
              {({ values }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="startTime"
                    label="Start Time"
                    placeholder="Select start time"
                    required
                    showTimePicker={true} // Enable Material Design clock picker
                  />
                  <div className="text-sm text-gray-600">Current value: {values.startTime || 'Not set'}</div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

          {/* Example 2: Native time picker (fallback) */}
          <div>
            <h3 className="text-lg font-semibold mb-4">2. Native Time Input (24-hour format)</h3>
            <Formik initialValues={{ endTime: '17:30' }} onSubmit={(values) => console.info('Form submitted:', values)}>
              {({ values }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="endTime"
                    label="End Time"
                    placeholder="Select end time"
                    required
                    showTimePicker={false} // Use native time picker
                  />
                  <div className="text-sm text-gray-600">Current value: {values.endTime || 'Not set'}</div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

          {/* Example 3: Custom step and validation */}
          <div>
            <h3 className="text-lg font-semibold mb-4">3. Custom Step (15-minute intervals)</h3>
            <Formik initialValues={{ meetingTime: '' }} onSubmit={(values) => console.info('Form submitted:', values)}>
              {({ values }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="meetingTime"
                    label="Meeting Time"
                    placeholder="Select meeting time"
                    required
                    step={15} // 15-minute intervals
                    helperText="Time will be rounded to 15-minute intervals"
                  />
                  <div className="text-sm text-gray-600">Current value: {values.meetingTime || 'Not set'}</div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>

          {/* Example 4: With custom onChange handler */}
          <div>
            <h3 className="text-lg font-semibold mb-4">4. With Custom Change Handler</h3>
            <Formik
              initialValues={{ customTime: '12:00' }}
              onSubmit={(values) => console.info('Form submitted:', values)}
            >
              {({ values, setFieldValue }) => (
                <Form className="space-y-4">
                  <InputTime
                    name="customTime"
                    label="Custom Time"
                    placeholder="Select time"
                    onChange={(time) => {
                      console.info('Time changed:', time);
                      setFieldValue('customTime', time);
                    }}
                  />
                  <div className="text-sm text-gray-600">Current value: {values.customTime || 'Not set'}</div>
                  <Button type="submit">Submit</Button>
                </Form>
              )}
            </Formik>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Material Design Clock Features:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              <li>
                <strong>Clock face interface:</strong> Circular clock with clickable hour/minute numbers
              </li>
              <li>
                <strong>Visual clock hands:</strong> Animated hands showing selected time
              </li>
              <li>
                <strong>Material Design styling:</strong> Gradient header, proper shadows, and animations
              </li>
              <li>
                <strong>Mode switching:</strong> Toggle between hour and minute selection modes
              </li>
              <li>
                <strong>AM/PM toggle:</strong> Easy switching between morning and evening times
              </li>
              <li>
                <strong>24-hour format:</strong> Always displays and stores time in HH:MM format
              </li>
              <li>
                <strong>Interactive elements:</strong> Hover effects, scaling, and smooth transitions
              </li>
              <li>
                <strong>Keyboard navigation:</strong> Use Space or Arrow Down to open picker
              </li>
              <li>
                <strong>Click interactions:</strong> Click on input field, clock icon, or time numbers
              </li>
              <li>
                <strong>Responsive design:</strong> Optimized for desktop and mobile devices
              </li>
              <li>
                <strong>Form integration:</strong> Full Formik support with validation
              </li>
            </ul>
          </div>

          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Material Design Elements:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              <li>
                <strong>Gradient header:</strong> Blue to indigo gradient with white text
              </li>
              <li>
                <strong>Clock face:</strong> Circular interface with 12-hour layout
              </li>
              <li>
                <strong>Clock hands:</strong> Animated pointers with circular endpoints
              </li>
              <li>
                <strong>Number positioning:</strong> Mathematically positioned around clock perimeter
              </li>
              <li>
                <strong>Selection feedback:</strong> Highlighted selected numbers with scaling effects
              </li>
              <li>
                <strong>Smooth animations:</strong> 300ms transitions for all interactions
              </li>
              <li>
                <strong>Material shadows:</strong> Proper elevation and depth perception
              </li>
            </ul>
          </div>

          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Props:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              <li>
                <code>showTimePicker</code>: Enable/disable Material Design clock picker (default: true)
              </li>
              <li>
                <code>step</code>: Minute intervals for time selection (default: 1)
              </li>
              <li>
                <code>format</code>: Time format - always 24-hour regardless of setting
              </li>
              <li>All existing InputTime props are preserved and supported</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
