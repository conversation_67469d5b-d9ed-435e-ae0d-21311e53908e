import AppPagination from '@/components/AppPagination';
import AppTable, { type ITableColumn } from '@/components/AppTable.tsx';
import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import DateHelper from '@/lib/date-helper.ts';
import { formatTimeGapToInitials } from '@/lib/utils';
import { useAttendanceListPage } from '@/pages/attendance/useAttendanceListPage.ts';
import type { IResListAttendance } from '@/types/response/IResListAttendance.ts';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';

export default function AttendanceListPage() {
  const page = useAttendanceListPage();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Absensi',
    },
  ];

  const tableColumn: ITableColumn<IResListAttendance>[] = [
    {
      headerTitle: 'Pegawai',
      component: (e) => <div>{e.account_name}</div>,
    },
    {
      headerTitle: 'Waktu <PERSON>',
      component: (e) => (
        <div>
          {DateHelper.toFormatDate(e.date_time, 'HH:mm:ss')} ({formatTimeGapToInitials(e.time_gap)})
        </div>
      ),
    },
    {
      headerTitle: 'Nama Absensi',
      component: (e) => (
        <div>
          <div className={'font-semibold'}>{e.time_table_name}</div>
          <div>
            {e.record_start_time} - {e.record_end_time}
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Status',
      component: (e) => <div>{e.status}</div>,
    },
    {
      headerTitle: 'Unit Kerja',
      component: (e) => (
        <div>
          <p className={'font-semibold'}>{e.work_unit_name}</p>
          <p className={'text-xs text-muted-foreground'}>{e.agency_name}</p>
        </div>
      ),
    },
  ];
  return (
    <PageContainer>
      <PageTitle title={'Absensi hari ini'} breadcrumb={breadcrumb} />
      <AppTable data={page.dataList} column={tableColumn} loading={page.queryList.isFetching} />
      {page.queryList.data?.paginated_data && (
        <AppPagination
          onPaginationChange={page.handlePaginationChange}
          dataPagination={page.queryList.data?.paginated_data}
        />
      )}
    </PageContainer>
  );
}
