import type { BroadcastStatusTypeEnum } from '@/types/type/BroadcastStatusTypeEnum.ts';
import { twMerge } from 'tailwind-merge';

export default function BroadcastStatusText(props: IProps) {
  const getStatusColor = (status: BroadcastStatusTypeEnum) => {
    const colorMap = {
      SENT: '  text-green-700',
      PENDING: '  text-yellow-700',
      REJECT: '  text-red-700',
      READY_TO_SEND: '  text-blue-700',
    };
    return colorMap[status] || ' text-gray-700';
  };

  return <div className={twMerge(getStatusColor(props.status))}>{props.label}</div>;
}

interface IProps {
  label: string;
  status: BroadcastStatusTypeEnum;
}
