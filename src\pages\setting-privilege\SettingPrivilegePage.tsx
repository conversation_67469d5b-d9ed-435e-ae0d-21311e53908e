import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Card, CardContent } from '@/components/ui/card';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { useSettingPrivilegePage } from './useSettingPrivilegePage';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function SettingPrivilegePage() {
  const page = useSettingPrivilegePage();

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Setting Akses',
    },
  ];
  return (
    <PageContainer loading={page.loading}>
      <PageTitle title="Setting Akses" breadcrumb={breadcrumb} />
      <div>
        <Card className="py-0">
          <div className="flex py-0">
            <div className="border-r">
              <div className="">
                {page.dataRole.map((item, i) => (
                  <div
                    onClick={() => page.onClickRole(item)}
                    key={i}
                    className={cn(
                      page.selectedRole?.role_enum === item.role_enum && 'border-b-primary text-primary ',
                      'px-3 py-4 cursor-pointer hover:bg-primary/5 border-b-2 w-60 font-bold',
                    )}
                  >
                    {item.name}
                  </div>
                ))}
              </div>
            </div>
            <CardContent className="py-4 w-full">
              <div className="w-full grid gap-5">
                <div>
                  <div className="flex gap-2">
                    <Settings />
                    <div>
                      Edit Akses untuk role <span className="font-bold">{page.selectedRole?.name}</span>
                    </div>
                  </div>
                </div>
                <div className="grid gap-2">
                  {page.dataPrivileges.map((item, i) => (
                    <div className="border w-full p-3 rounded-md" key={i}>
                      <div className="flex items-start gap-3">
                        <Checkbox
                          className="cursor-pointer mt-1"
                          id={item.privilege_enum}
                          onCheckedChange={() => page.onChangePrivilege(item.privilege_enum)}
                          checked={page.selectedPrivileges.findIndex((v) => v === item.privilege_enum) > -1}
                        />
                        <div className="grid gap-2">
                          <label htmlFor={item.privilege_enum}>{item.privilege_name}</label>
                          <p className="text-muted-foreground text-sm">{item.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {page.selectedRole?.role_enum !== 'SUPER_ADMIN' && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={page.checked}
                        onCheckedChange={() => page.setChecked((e) => !e)}
                        className="cursor-pointer mt-1"
                        id={'checked'}
                      />
                      <label htmlFor={'checked'}>Konfirmasi perubahan settingan</label>
                    </div>
                    <Button
                      loading={page.mutationSubmit.isPending}
                      onClick={() => page.mutationSubmit.mutate()}
                      disabled={!page.checked}
                    >
                      SIMPAN
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </div>
        </Card>
      </div>
    </PageContainer>
  );
}
