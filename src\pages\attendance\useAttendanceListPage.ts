import DateHelper from '@/lib/date-helper';
import { AttendanceRepository } from '@/repositories/attendance-repository.ts';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildSearchParams } from '@/utils/search-params.utils';
import { useQuery } from '@tanstack/react-query';
import { addDays } from 'date-fns';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export function useAttendanceListPage() {
  const attendanceRepository = new AttendanceRepository();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterData, setFilterData] = useState<IFilterList>(getInitialFilter());

  function getInitialFilter(): IFilterList {
    return {
      agency_id: searchParams.get('agency_id') || '',
      page: parseInt(searchParams.get('page') || '0'),
      size: parseInt(searchParams.get('size') || '10'),
      start_date: searchParams.get('start_date') || DateHelper.toFormatDate(new Date(), 'yyyy-MM-dd'),
      end_date: searchParams.get('end_date') || DateHelper.toFormatDate(addDays(new Date(), 2), 'yyyy-MM-dd'),
    };
  }

  useEffect(() => {
    const params = buildSearchParams(filterData);
    setSearchParams(params);
  }, [filterData, setSearchParams]);

  const queryList = useQuery({
    queryKey: ['attendance_list', filterData],
    queryFn: () => attendanceRepository.getAttendance(filterData),
  });

  function handlePaginationChange(params: { page?: number; size?: number }) {
    setFilterData((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }

  const dataList = queryList.data?.response_data || [];
  return { queryList, dataList, handlePaginationChange };
}
