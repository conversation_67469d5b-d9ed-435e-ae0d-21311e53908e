import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqBroadcastForm } from '@/types/request/IReqBroadcastForm';
import type { IResDetailBroadcast } from '@/types/response/IResDetailBroadcast';
import type { IResListBroadcast } from '@/types/response/IResListBroadcast';
import type { BaseResponse, BaseResponsePaginated } from '@/types/response/IResModel';
import type { IResSummaryCountBroadcastStatus } from '@/types/response/IResSummaryCountBroadcastStatus';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';

export class BroadcastRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async listBroadcast(filter: IFilterList) {
    const params = buildSearchParams(filter);

    return await this.httpService
      .GET(ENDPOINT.LIST_BROADCAST() + buildQueryString(params))
      .then((res: BaseResponsePaginated<IResListBroadcast[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async getSummaryCount(filter: IFilterList) {
    const params = buildSearchParams(filter);
    return await this.httpService
      .GET(ENDPOINT.GET_SUMMARY_COUNT() + buildQueryString(params))
      .then((res: BaseResponse<IResSummaryCountBroadcastStatus[]>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async createBroadcast(data: IReqBroadcastForm) {
    return await this.httpService
      .POST(ENDPOINT.CREATE_NEW_BROADCAST(), data)
      .then(() => {
        return true;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        return false;
      });
  }

  async detailBroadcast(id: string) {
    return await this.httpService
      .GET(ENDPOINT.DETAIL_BROADCAST(id))
      .then((res: BaseResponse<IResDetailBroadcast>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
      });
  }

  async approveBroadcast(id: string) {
    return this.httpService
      .PATCH(ENDPOINT.APPROVE_BROADCAST(id))
      .then((res: BaseResponse<IResDetailBroadcast>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
      });
  }

  async rejectBroadcast(id: string, reason: string) {
    return this.httpService
      .PUT(ENDPOINT.REJECT_BROADCAST(id), { reason })
      .then((res: BaseResponse<IResDetailBroadcast>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
      });
  }

  async sendBroadcast(id: string) {
    return this.httpService
      .PATCH(ENDPOINT.SEND_BROADCAST(id))
      .then((res: BaseResponse<IResDetailBroadcast>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
      });
  }
}
