import AppPagination from '@/components/AppPagination';
import type { ITableColumn } from '@/components/AppTable';
import AppTable from '@/components/AppTable';
import EmployeeStatusText from '@/components/EmployeeStatusText';
import FilterList from '@/components/FilterList';
import InputSearch from '@/components/InputSearch';
import InputSelect from '@/components/InputSelect';
import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/routes/routes';
import type { IResListEmployee } from '@/types/response/IResListEmployee';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { Avatar, AvatarFallback, AvatarImage } from '@radix-ui/react-avatar';
import { InfoIcon, Mail, Phone, User } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useListEmployeePage } from './useListEmployeePage';

export default function ListEmployeePage() {
  const page = useListEmployeePage();

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Pegawai',
    },
  ];

  const tableColumn: ITableColumn<IResListEmployee>[] = [
    {
      headerTitle: 'Nama',
      component: (e) => (
        <div className="flex items-center gap-2">
          <div className="h-10 w-10">
            <Avatar>
              <AvatarFallback className="h-10 w-10">
                <User />
              </AvatarFallback>
              <AvatarImage className="h-8 w-8 rounded-full" src={e.profile_picture} />
            </Avatar>
          </div>
          <div>
            <div className="font-semibold">{e.name}</div>
            <p className="text-xs text-gray-600">{e.nip}</p>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Kontak',
      component: (e) => (
        <div>
          <div className="flex items-center gap-2">
            <Phone size={12} />
            <div>+{e?.phone || '-'}</div>
          </div>
          <div className="flex items-center gap-2">
            <Mail size={12} />
            <div>{e?.email || '-'}</div>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Status',
      component: (e) => (
        <div className="max-w-xs">
          <EmployeeStatusText string={e.status_string} status={e.status} />
        </div>
      ),
    },
    {
      headerTitle: 'Nama Perangkat daerah',
      component: (e) => (
        <div className="max-w-xs">
          <div className="truncate text-wrap">{e.agency_name}</div>
          <p className="text-muted-foreground">{e?.work_unit_name || '-'}</p>
        </div>
      ),
    },

    {
      headerTitle: '',
      component: (e) => (
        <Link to={ROUTES.DETAIL_EMPLOYEE(e.account_id)} className="px-10">
          <Button size={'icon'} variant={'outline'}>
            <InfoIcon />
          </Button>
        </Link>
      ),
    },
  ];

  return (
    <PageContainer>
      <div className="flex justify-between">
        <PageTitle title={'Pegawai'} breadcrumb={breadcrumb} />
      </div>

      <div className="flex items-center gap-3 justify-between mb-4">
        <div className="flex-1">
          <InputSearch
            active={page.isActiveSearch()}
            handleReset={page.handleResetSearch}
            searchValue={page.searchValue}
            setSearchValue={page.setSearchValue}
            placeholder="Cari nama / NIP pegawai..."
            handleSearch={() => page.handleSearch()}
          />
        </div>

        <FilterList
          open={page.openFilter}
          onOpenChange={page.setOpenFilter}
          onSubmit={() => page.submitFilter()}
          onReset={page.handleResetFilter}
        >
          <div className="grid gap-3">
            <InputSelect
              label="Perangkat daerah"
              placeholder="Pilih Perangkat daerah"
              disableFormik
              value={page.filterData?.agency_id}
              options={page.dataAgencyFilter}
              onValueChange={(e) =>
                page.setFilterData((v) => {
                  return {
                    ...v,
                    agency_id: e,
                    page: 0,
                  };
                })
              }
              name="filter_agency"
              id="filter_agency"
            />

            <InputSelect
              label="Role"
              placeholder="Pilih Role User"
              disableFormik
              options={page.dataRole}
              onValueChange={(e) => page.onChangeFilterRole(e)}
              name="filter_role"
              id="filter_role"
              value={page?.filterData?.role || 'all'}
            />
          </div>
        </FilterList>
      </div>
      <div className="flex gap-2 flex-wrap">
        {page.querySummary.data &&
          page.querySummary.data.map((item) => (
            <Button
              variant={page.filterData.status === item.status_enum ? 'default' : 'outline'}
              className="cursor-pointer rounded-full"
              onClick={() => page.onChangeFilter(item.status_enum)}
              key={item.status_enum}
            >
              {item.status_string} ({item.amount})
            </Button>
          ))}
      </div>
      <AppTable loading={page.loading} data={page.dataList} column={tableColumn} />
      {page.queryList.data?.paginated_data && (
        <AppPagination
          onPaginationChange={(e) => page.handlePaginationChange(e)}
          dataPagination={page.queryList.data?.paginated_data as any}
        />
      )}
    </PageContainer>
  );
}
