import AppPagination from '@/components/AppPagination';
import type { ITableColumn } from '@/components/AppTable';
import AppTable from '@/components/AppTable';
import FilterList from '@/components/FilterList';
import InputSearch from '@/components/InputSearch';
import InputSelect from '@/components/InputSelect';
import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/routes/routes';
import type { IResListEmployee } from '@/types/response/IResListEmployee';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { Avatar, AvatarFallback, AvatarImage } from '@radix-ui/react-avatar';
import { InfoIcon, Mail, Phone, User } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useListEmployeePage } from './useListEmployeePage';
import EmployeeStatusText from '@/components/EmployeeStatusText';

export default function ListEmployeePage() {
  const page = useListEmployeePage();

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Pegawai',
    },
  ];

  const tableColumn: ITableColumn<IResListEmployee>[] = [
    {
      headerTitle: 'Nama',
      component: (e) => (
        <div className="flex items-center gap-2">
          <div className="h-10 w-10">
            <Avatar>
              <AvatarFallback className="h-10 w-10">
                <User />
              </AvatarFallback>
              <AvatarImage className="h-8 w-8 rounded-full" src={e.profile_picture} />
            </Avatar>
          </div>
          <div>
            <div className="font-semibold">{e.name}</div>
            <p className="text-xs text-gray-600">{e.nip}</p>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Kontak',
      component: (e) => (
        <div>
          <div className="flex items-center gap-2">
            <Phone size={12} />
            <div>+{e?.phone || '-'}</div>
          </div>
          <div className="flex items-center gap-2">
            <Mail size={12} />
            <div>{e?.email || '-'}</div>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Status',
      component: (e) => (
        <div className="max-w-xs">
          <EmployeeStatusText status={e.status} />
        </div>
      ),
    },
    {
      headerTitle: 'Nama instansi',
      component: (e) => (
        <div className="max-w-xs">
          <div className="truncate text-wrap">{e.agency_name}</div>
          <p className="text-muted-foreground">{e?.work_unit_name || '-'}</p>
        </div>
      ),
    },

    {
      headerTitle: '',
      component: (e) => (
        <Link to={ROUTES.DETAIL_EMPLOYEE(e.account_id)} className="px-10">
          <Button size={'icon'} variant={'outline'}>
            <InfoIcon />
          </Button>
        </Link>
      ),
    },
  ];

  return (
    <PageContainer>
      <div className="flex justify-between">
        <PageTitle title={'Pegawai'} breadcrumb={breadcrumb} />
        <Link to={ROUTES.CREATE_EMPLOYEE()}>
          <Button>Tambahkan pegawai</Button>
        </Link>
      </div>

      <div className="flex items-center gap-3 justify-between mb-4">
        <div className="flex-1">
          <InputSearch
            active={page.isActiveSearch()}
            handleReset={page.handleResetSearch}
            searchValue={page.searchValue}
            setSearchValue={page.setSearchValue}
            placeholder="Cari nama / NIP pegawai..."
            handleSearch={() => page.handleSearch()}
          />
        </div>

        <FilterList
          open={page.openFilter}
          onOpenChange={page.setOpenFilter}
          onSubmit={() => page.submitFilter()}
          onReset={page.handleResetFilter}
        >
          <InputSelect
            label="Instansi"
            placeholder="Pilih Instansi"
            disableFormik
            value={page.filterData?.agency_id}
            options={page.dataAgencyFilter}
            onValueChange={(e) =>
              page.setFilterData((v) => {
                return {
                  ...v,
                  agency_id: e,
                  page: 0,
                };
              })
            }
            name="filter_agency"
            id="filter_agency"
          />
        </FilterList>
      </div>

      <AppTable loading={page.loading} data={page.dataList} column={tableColumn} />

      <AppPagination onPaginationChange={(e) => page.handlePaginationChange(e)} dataPagination={page.pagination} />
    </PageContainer>
  );
}
