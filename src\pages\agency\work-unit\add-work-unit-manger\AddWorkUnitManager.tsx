import PageTitle from '@/components/page-title.tsx';
import PageContainer from '@/components/PageContainer.tsx';
import { useAddWorkUnitManager } from '@/pages/agency/work-unit/add-work-unit-manger/useAddWorkUnitManager.ts';
import { ROUTES } from '@/routes/routes.ts';
import AppTable, { type ITableColumn } from '@/components/AppTable.tsx';
import type { IResListEmployee } from '@/types/response/IResListEmployee.ts';
import { Checkbox } from '@/components/ui/checkbox.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Send } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';

export default function AddWorkUnitManager() {
  const page = useAddWorkUnitManager();
  const breadcrumb = [
    {
      label: 'Unit Kerja',
      path: ROUTES.LIST_WORK_UNIT(),
    },
    {
      label: page?.detail?.name || '',
      path: ROUTES.DETAIL_WORK_UNIT(page.detail?.id || ''),
    },
    {
      label: 'Tambah atasan unit kerja',
    },
  ];

  const tableColumn: ITableColumn<IResListEmployee>[] = [
    {
      headerTitle: 'Nama',
      component: (e) => (
        <div className={'flex items-center gap-2'}>
          <Checkbox onCheckedChange={() => page.onChangeDataSelected(e)} checked={page.onCheckCheckbox(e)} />
          <div>{e.name}</div>
        </div>
      ),
    },
    {
      headerTitle: 'Unit Kerja',
      component: (e) => (
        <div className={'flex items-center gap-2'}>
          <div>{e.work_unit_name}</div>
        </div>
      ),
    },
  ];

  return (
    <PageContainer className={'relative'} loading={page.queryDetail.isPending || page.queryEmployee.isPending}>
      <PageTitle title="Tambah atasan unit kerja" breadcrumb={breadcrumb} />
      <div>
        <AppTable data={page.dataEmployee} column={tableColumn} />
      </div>
      <AnimatePresence>
        {page.selectedData.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.3 }}
            className="bg-primary py-3 px-5 text-white flex items-center justify-between fixed bottom-18 rounded-md w-5xl "
          >
            <div>{page.selectedData.length} pegawai terpilih untuk menjadi atasan unit kerja</div>
            <Button
              loading={page.mutationSubmit.isPending}
              onClick={() => page.onSubmitManager()}
              variant="link"
              className="text-white"
            >
              <Send />
              Kirim
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </PageContainer>
  );
}
