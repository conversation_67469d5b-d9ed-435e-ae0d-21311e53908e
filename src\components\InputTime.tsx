import type { FocusEventHandler, ReactNode } from 'react';
import { cn } from '@/lib/utils.ts';
import Label from '@/components/ui/Label.tsx';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Input } from '@/components/ui/input.tsx';
import { Clock, ChevronUp, ChevronDown } from 'lucide-react';
import { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface IProps {
  id?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLInputElement>;
  onChange?: (value: string) => void;
  autoComplete?: string;
  dataTestId?: string;
  format?: '12' | '24'; // Format 12 jam atau 24 jam
  step?: number; // Step untuk menit (default: 1)
  showTimePicker?: boolean; // Enable custom time picker popup
}

// Time Picker Popup Component
const TimePickerPopup = ({
  isOpen,
  onClose,
  value,
  onChange,
  step = 1,
}: {
  isOpen: boolean;
  onClose: () => void;
  value: string;
  onChange: (time: string) => void;
  step?: number;
}) => {
  const [selectedHour, setSelectedHour] = useState(0);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const popupRef = useRef<HTMLDivElement>(null);

  // Parse current value
  useEffect(() => {
    if (value && value.match(/^\d{2}:\d{2}$/)) {
      const [hour, minute] = value.split(':').map(Number);
      setSelectedHour(hour);
      setSelectedMinute(minute);
    }
  }, [value]);

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      if (event.key === 'Escape') {
        onClose();
      } else if (event.key === 'Enter') {
        handleTimeSelect();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedHour, selectedMinute]);

  const handleTimeSelect = useCallback(() => {
    const timeString = `${selectedHour.toString().padStart(2, '0')}:${selectedMinute.toString().padStart(2, '0')}`;
    onChange(timeString);
    onClose();
  }, [selectedHour, selectedMinute, onChange, onClose]);

  const adjustHour = (delta: number) => {
    setSelectedHour((prev) => {
      const newHour = prev + delta;
      if (newHour < 0) return 23;
      if (newHour > 23) return 0;
      return newHour;
    });
  };

  const adjustMinute = (delta: number) => {
    setSelectedMinute((prev) => {
      const newMinute = prev + delta * step;
      if (newMinute < 0) return 60 - step;
      if (newMinute >= 60) return 0;
      return newMinute;
    });
  };

  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 z-50 mt-1 w-full">
      <Card ref={popupRef} className="shadow-2xl border-2 border-blue-100 bg-white/95 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Header */}
            <div className="text-center">
              <h3 className="text-sm font-semibold text-gray-700 mb-1">Pilih Waktu</h3>
              <div className="text-2xl font-bold text-blue-600 font-mono">
                {selectedHour.toString().padStart(2, '0')}:{selectedMinute.toString().padStart(2, '0')}
              </div>
            </div>

            {/* Time Controls */}
            <div className="grid grid-cols-2 gap-4">
              {/* Hour Control */}
              <div className="text-center space-y-2">
                <label className="text-xs font-medium text-gray-600 uppercase tracking-wider">Jam</label>
                <div className="flex flex-col items-center space-y-1">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => adjustHour(1)}
                    className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  >
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <div className="h-12 w-16 bg-blue-50 rounded-lg flex items-center justify-center border-2 border-blue-200">
                    <span className="text-xl font-bold text-blue-700 font-mono">
                      {selectedHour.toString().padStart(2, '0')}
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => adjustHour(-1)}
                    className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Minute Control */}
              <div className="text-center space-y-2">
                <label className="text-xs font-medium text-gray-600 uppercase tracking-wider">Menit</label>
                <div className="flex flex-col items-center space-y-1">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => adjustMinute(1)}
                    className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  >
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <div className="h-12 w-16 bg-blue-50 rounded-lg flex items-center justify-center border-2 border-blue-200">
                    <span className="text-xl font-bold text-blue-700 font-mono">
                      {selectedMinute.toString().padStart(2, '0')}
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => adjustMinute(-1)}
                    className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1 text-sm">
                Batal
              </Button>
              <Button type="button" onClick={handleTimeSelect} className="flex-1 bg-blue-600 hover:bg-blue-700 text-sm">
                Pilih
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default function InputTime(props: IProps) {
  const formik = useFormikContext<any>();
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showTimePicker, setShowTimePicker] = useState(false);

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  // Force 24-hour format
  useEffect(() => {
    if (inputRef.current) {
      // Set the input to always use 24-hour format
      inputRef.current.style.setProperty('--webkit-appearance', 'none');
      inputRef.current.style.setProperty('-moz-appearance', 'textfield');

      // Force locale to use 24-hour format
      try {
        // Check if browser supports changing the locale behavior
        const isTime24 =
          new Intl.DateTimeFormat('en-GB', {
            hour: 'numeric',
            hour12: false,
          }).resolvedOptions().hour12 === false;

        if (!isTime24) {
          // Add data attribute to help with CSS targeting
          inputRef.current.setAttribute('data-format', '24');
        }
      } catch {
        // Fallback: just set the attribute
        inputRef.current.setAttribute('data-format', '24');
      }
    }
  }, []);

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const timeValue = e.target.value;

    if (timeValue && timeValue.match(/^\d{2}:\d{2}$/)) {
      if (props.onChange) {
        props.onChange(timeValue);
      } else if (formik?.setFieldValue) {
        formik.setFieldValue(props.name, timeValue);
      }
    }
  };

  const handleTimePickerChange = useCallback(
    (timeValue: string) => {
      if (props.onChange) {
        props.onChange(timeValue);
      } else if (formik?.setFieldValue) {
        formik.setFieldValue(props.name, timeValue);
      }
    },
    [props.onChange, formik?.setFieldValue, props.name],
  );

  const handleInputClick = () => {
    if (props.showTimePicker !== false) {
      setShowTimePicker(true);
    }
  };

  const handleInputFocus = () => {
    if (props.showTimePicker !== false) {
      setShowTimePicker(true);
    }
  };

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  return (
    <div className="grid">
      {props.label && <Label label={props.label} required={props.required} />}
      <div ref={containerRef} className={cn('relative flex items-center dark:bg-card bg-white')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3 z-10">{props.startIcon}</span>
        )}

        <Input
          ref={inputRef}
          data-testid={props.dataTestId}
          autoComplete={props.autoComplete}
          onBlur={props.onBlur ?? formik?.handleBlur}
          onChange={handleTimeChange}
          onClick={handleInputClick}
          onFocus={handleInputFocus}
          value={currentValue}
          name={props.name}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && props.onEnter) {
              props.onEnter();
            }
            // Open time picker with Space or Arrow Down
            if ((e.key === ' ' || e.key === 'ArrowDown') && props.showTimePicker !== false) {
              e.preventDefault();
              setShowTimePicker(true);
            }
          }}
          type="time"
          step={props.step || 60} // Default step 60 detik (1 menit)
          placeholder={props.placeholder || 'HH:MM'}
          lang="en-GB" // Force British English locale which uses 24-hour format
          className={cn(
            props.startIcon ? 'pl-12' : 'pl-10', // Space untuk default clock icon
            props.endIcon ? 'pr-9' : '',
            errorMessage ? ' outline-red-500 border-red-500 bg-red-100' : '',
            // Additional class for 24-hour format styling
            '[&::-webkit-calendar-picker-indicator]:opacity-100',
            // Enhanced styling for better UX
            'cursor-pointer hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200',
            // Hide native time picker when custom picker is enabled
            props.showTimePicker !== false &&
              '[&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden',
          )}
          id={props.id}
          style={{
            // Additional inline styles to ensure 24-hour format
            colorScheme: 'light dark',
          }}
          readOnly={props.showTimePicker !== false} // Make readonly when using custom picker
        />

        {/* Enhanced clock icon with click handler */}
        {!props.startIcon && (
          <button
            type="button"
            onClick={handleInputClick}
            className="absolute left-3 h-4 w-4 text-gray-500 hover:text-blue-500 transition-colors duration-200 cursor-pointer z-10"
            tabIndex={-1}
          >
            <Clock className="h-4 w-4" />
          </button>
        )}

        {props.endIcon && (
          <span className="absolute text-gray-500 right-3 flex items-center pl-3 z-10">{props.endIcon}</span>
        )}

        {/* Custom Time Picker Popup */}
        {props.showTimePicker !== false && (
          <TimePickerPopup
            isOpen={showTimePicker}
            onClose={() => setShowTimePicker(false)}
            value={currentValue}
            onChange={handleTimePickerChange}
            step={props.step || 1}
          />
        )}
      </div>

      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
