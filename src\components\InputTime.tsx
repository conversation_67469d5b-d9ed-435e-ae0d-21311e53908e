import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FocusEventHandler, ReactNode } from 'react';
import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils.ts';
import Label from '@/components/ui/Label.tsx';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Input } from '@/components/ui/input.tsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface IProps {
  id?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLInputElement>;
  onChange?: ChangeEventHandler<HTMLInputElement>;
  autoComplete?: string;
  dataTestId?: string;
}

export default function InputTime(props: IProps) {
  const formik = useFormikContext<any>();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHour, setSelectedHour] = useState('00');
  const [selectedMinute, setSelectedMinute] = useState('00');
  const inputRef = useRef<HTMLInputElement>(null);

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  // Generate hours (00-24)
  const hours = Array.from({ length: 25 }, (_, i) => i.toString().padStart(2, '0'));

  // Generate minutes (00-59)
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));

  // Konversi HH:mm:ss ke HH:MM
  const convertTimeFormat = (value: string): string => {
    if (!value) return '';

    if (value.includes(':')) {
      const parts = value.split(':');
      if (parts.length >= 2) {
        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);

        if (!isNaN(hours) && !isNaN(minutes)) {
          const validHours = Math.min(Math.max(hours, 0), 24);
          const validMinutes = Math.min(Math.max(minutes, 0), 59);

          return `${validHours.toString().padStart(2, '0')}:${validMinutes.toString().padStart(2, '0')}`;
        }
      }
    }

    return value;
  };

  // Parse current value untuk set selected hour/minute
  const parseCurrentValue = (value: string) => {
    if (!value) return;

    const formattedValue = convertTimeFormat(value);
    if (formattedValue.includes(':')) {
      const [hour, minute] = formattedValue.split(':');
      setSelectedHour(hour);
      setSelectedMinute(minute);
    }
  };

  // Get current value
  const rawValue = props.value ?? getIn(formik?.values, props.name) ?? '';
  const currentValue = rawValue ? convertTimeFormat(rawValue) : '';

  // Update selected time when value changes
  useEffect(() => {
    parseCurrentValue(currentValue);
  }, [currentValue]);

  // Dialog will handle click outside and escape key automatically

  const handleInputFocus = () => {
    setIsOpen(true);
  };

  const handleTimeSelect = (hour: string, minute: string) => {
    const timeValue = `${hour}:${minute}`;

    // Create synthetic event
    const event = {
      target: {
        name: props.name,
        value: timeValue,
      },
    } as React.ChangeEvent<HTMLInputElement>;

    if (props.onChange) {
      props.onChange(event);
    } else if (formik?.handleChange) {
      formik.handleChange(event);
    }

    setIsOpen(false);
    inputRef.current?.focus();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Jika input kosong, langsung set kosong
    if (!inputValue) {
      e.target.value = '';
      if (props.onChange) {
        props.onChange(e);
      } else if (formik?.handleChange) {
        formik.handleChange(e);
      }
      return;
    }

    // Format input manual
    const numbers = inputValue.replace(/\D/g, '');
    let formattedValue = '';

    if (numbers.length <= 2) {
      const hour = Math.min(parseInt(numbers) || 0, 24);
      formattedValue = hour.toString().padStart(2, '0');
    } else if (numbers.length <= 4) {
      const hour = Math.min(parseInt(numbers.slice(0, 2)) || 0, 24);
      const minute = Math.min(parseInt(numbers.slice(2)) || 0, 59);
      formattedValue = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    }

    e.target.value = formattedValue;

    // Panggil onChange handler
    if (props.onChange) {
      props.onChange(e);
    } else if (formik?.handleChange) {
      formik.handleChange(e);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow special keys
    const allowedKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
    ];
    if (allowedKeys.includes(e.key)) {
      if (e.key === 'Escape') {
        setIsOpen(false);
      }
      return;
    }

    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    if ((e.ctrlKey || e.metaKey) && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
      return;
    }

    // Only allow numbers and colon
    if (!/^[0-9:]$/.test(e.key)) {
      e.preventDefault();
    }

    // Handle Enter key
    if (e.key === 'Enter' && props.onEnter) {
      props.onEnter();
    }
  };

  return (
    <div className="grid relative">
      {props.label && <Label label={props.label} required={props.required} />}
      <div className={cn('relative flex items-center dark:bg-card bg-white')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3">{props.startIcon}</span>
        )}
        <Input
          ref={inputRef}
          data-testid={props.dataTestId}
          autoComplete={props.autoComplete}
          onBlur={props.onBlur ?? formik?.handleBlur}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          value={currentValue}
          name={props.name}
          onKeyDown={handleKeyDown}
          type="text"
          placeholder={props.placeholder || 'HH:MM'}
          maxLength={5}
          aria-label={props.label || 'Time input'}
          aria-describedby={errorMessage ? `${props.name}-error` : undefined}
          className={cn(
            props.startIcon ? 'pl-12' : '',
            props.endIcon ? 'pr-9' : '',
            errorMessage ? ' outline-red-500 border-red-500 bg-red-100' : '',
          )}
          id={props.id}
        />
        {props.endIcon && (
          <span className="absolute text-gray-500 right-3 flex items-center pl-3">{props.endIcon}</span>
        )}
      </div>

      {/* Time Picker Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="w-96 max-w-md" showCloseButton={true} aria-describedby="time-picker-description">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">Pilih Waktu</DialogTitle>
          </DialogHeader>

          {/* Content */}
          <div className="space-y-6">
            {/* Current Time Display */}
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                {selectedHour}:{selectedMinute}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">Format 24 Jam</div>
            </div>

            <div className="flex space-x-4">
              {/* Hours Column */}
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 text-center">Jam</div>
                <div className="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg">
                  {hours.map((hour) => (
                    <button
                      key={hour}
                      type="button"
                      onClick={() => setSelectedHour(hour)}
                      className={cn(
                        'w-full px-4 py-3 text-sm text-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors',
                        selectedHour === hour
                          ? 'bg-blue-500 text-white hover:bg-blue-600'
                          : 'text-gray-700 dark:text-gray-300',
                      )}
                    >
                      {hour}
                    </button>
                  ))}
                </div>
              </div>

              {/* Minutes Column */}
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 text-center">Menit</div>
                <div className="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg">
                  {minutes.map((minute) => (
                    <button
                      key={minute}
                      type="button"
                      onClick={() => setSelectedMinute(minute)}
                      className={cn(
                        'w-full px-4 py-3 text-sm text-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors',
                        selectedMinute === minute
                          ? 'bg-blue-500 text-white hover:bg-blue-600'
                          : 'text-gray-700 dark:text-gray-300',
                      )}
                    >
                      {minute}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div>
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Pilihan Cepat</div>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedHour('00');
                    setSelectedMinute('00');
                  }}
                  className="flex-1"
                >
                  00:00
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedHour('12');
                    setSelectedMinute('00');
                  }}
                  className="flex-1"
                >
                  12:00
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedHour('24');
                    setSelectedMinute('00');
                  }}
                  className="flex-1"
                >
                  24:00
                </Button>
              </div>
            </div>
          </div>

          {/* Footer */}
          <DialogFooter className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Batal
            </Button>
            <Button type="button" onClick={() => handleTimeSelect(selectedHour, selectedMinute)}>
              Pilih Waktu
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
