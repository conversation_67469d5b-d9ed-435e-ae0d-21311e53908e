import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input.tsx';
import Label from '@/components/ui/Label.tsx';
import { cn } from '@/lib/utils.ts';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Clock } from 'lucide-react';
import type { FocusEventHandler, ReactNode } from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface IProps {
  id?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLInputElement>;
  onChange?: (value: string) => void;
  autoComplete?: string;
  dataTestId?: string;
  format?: '12' | '24'; // Format 12 jam atau 24 jam
  step?: number; // Step untuk menit (default: 1)
  showTimePicker?: boolean; // Enable custom time picker popup
}

// Material Design Clock Time Picker Popup Component
const TimePickerPopup = ({
  isOpen,
  onClose,
  value,
  onChange,
}: {
  isOpen: boolean;
  onClose: () => void;
  value: string;
  onChange: (time: string) => void;
  step?: number;
}) => {
  const [selectedHour, setSelectedHour] = useState(0);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [mode, setMode] = useState<'hour' | 'minute'>('hour');
  const [isAM, setIsAM] = useState(true);
  const popupRef = useRef<HTMLDivElement>(null);
  const clockRef = useRef<HTMLDivElement>(null);

  // Parse current value
  useEffect(() => {
    if (value && value.match(/^\d{2}:\d{2}$/)) {
      const [hour, minute] = value.split(':').map(Number);
      setSelectedHour(hour);
      setSelectedMinute(minute);
      setIsAM(hour < 12);
    }
  }, [value]);

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      if (event.key === 'Escape') {
        onClose();
      } else if (event.key === 'Enter') {
        handleTimeSelect();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedHour, selectedMinute]);

  const handleTimeSelect = useCallback(() => {
    const timeString = `${selectedHour.toString().padStart(2, '0')}:${selectedMinute.toString().padStart(2, '0')}`;
    onChange(timeString);
    onClose();
  }, [selectedHour, selectedMinute, onChange, onClose]);

  // Clock face interaction handlers
  const handleHourClick = (hour: number) => {
    const adjustedHour = isAM ? hour % 12 : (hour % 12) + 12;
    setSelectedHour(adjustedHour);
    setMode('minute');
  };

  const handleMinuteClick = (minute: number) => {
    setSelectedMinute(minute);
  };

  const handleAMPMToggle = () => {
    setIsAM(!isAM);
    setSelectedHour((prev) => {
      if (isAM) {
        // Switching to PM
        return prev < 12 ? prev + 12 : prev;
      } else {
        // Switching to AM
        return prev >= 12 ? prev - 12 : prev;
      }
    });
  };

  // Generate clock positions for numbers
  const getClockPosition = (index: number, total: number, radius: number) => {
    const angle = (index * 360) / total - 90; // Start from 12 o'clock
    const radian = (angle * Math.PI) / 180;
    return {
      x: Math.cos(radian) * radius,
      y: Math.sin(radian) * radius,
    };
  };

  // Generate hour numbers (1-12)
  const hourNumbers = Array.from({ length: 12 }, (_, i) => i + 1);

  // Generate minute numbers (0, 5, 10, 15, ..., 55)
  const minuteNumbers = Array.from({ length: 12 }, (_, i) => i * 5);

  // Get display hour for 12-hour format
  const getDisplayHour = (hour: number) => {
    if (hour === 0) return 12;
    if (hour > 12) return hour - 12;
    return hour;
  };

  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 z-50 mt-1 w-full min-w-[320px]">
      <Card ref={popupRef} className="shadow-2xl border-0 bg-white backdrop-blur-sm overflow-hidden">
        <CardContent className="p-0">
          {/* Material Design Time Picker Layout */}
          <div className="bg-gradient-to-br from-blue-600 to-indigo-700 text-white p-6">
            {/* Time Display Header */}
            <div className="flex items-center justify-center space-x-2">
              <button
                type="button"
                onClick={() => setMode('hour')}
                className={`text-4xl font-light transition-all duration-200 ${
                  mode === 'hour' ? 'text-white' : 'text-white/60 hover:text-white/80'
                }`}
              >
                {getDisplayHour(selectedHour).toString().padStart(2, '0')}
              </button>
              <span className="text-4xl font-light text-white/80">:</span>
              <button
                type="button"
                onClick={() => setMode('minute')}
                className={`text-4xl font-light transition-all duration-200 ${
                  mode === 'minute' ? 'text-white' : 'text-white/60 hover:text-white/80'
                }`}
              >
                {selectedMinute.toString().padStart(2, '0')}
              </button>
            </div>

            {/* AM/PM Toggle for 24-hour display */}
            <div className="flex justify-center mt-3">
              <div className="flex bg-white/20 rounded-lg p-1">
                <button
                  type="button"
                  onClick={handleAMPMToggle}
                  className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                    isAM ? 'bg-white text-blue-600' : 'text-white/80 hover:text-white'
                  }`}
                >
                  AM
                </button>
                <button
                  type="button"
                  onClick={handleAMPMToggle}
                  className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                    !isAM ? 'bg-white text-blue-600' : 'text-white/80 hover:text-white'
                  }`}
                >
                  PM
                </button>
              </div>
            </div>
          </div>

          {/* Clock Face */}
          <div className="p-8">
            <div className="relative w-64 h-64 mx-auto">
              {/* Clock Circle Background */}
              <div ref={clockRef} className="absolute inset-0 rounded-full bg-gray-50 border-2 border-gray-100">
                {/* Center Dot */}
                <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-blue-600 rounded-full transform -translate-x-1/2 -translate-y-1/2 z-20"></div>

                {/* Clock Hand */}
                {mode === 'hour' && (
                  <div
                    className="absolute top-1/2 left-1/2 origin-bottom bg-blue-600 rounded-full transition-all duration-300 z-10"
                    style={{
                      width: '2px',
                      height: '80px',
                      transform: `translate(-50%, -100%) rotate(${(getDisplayHour(selectedHour) % 12) * 30 - 90}deg)`,
                    }}
                  >
                    <div className="absolute -top-3 -left-2 w-6 h-6 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                )}

                {mode === 'minute' && (
                  <div
                    className="absolute top-1/2 left-1/2 origin-bottom bg-blue-600 rounded-full transition-all duration-300 z-10"
                    style={{
                      width: '2px',
                      height: '100px',
                      transform: `translate(-50%, -100%) rotate(${selectedMinute * 6 - 90}deg)`,
                    }}
                  >
                    <div className="absolute -top-3 -left-2 w-6 h-6 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                )}

                {/* Hour Numbers */}
                {mode === 'hour' &&
                  hourNumbers.map((hour) => {
                    const position = getClockPosition(hour - 1, 12, 100);
                    const isSelected = getDisplayHour(selectedHour) === hour;

                    return (
                      <button
                        key={hour}
                        type="button"
                        onClick={() => handleHourClick(hour)}
                        className={`absolute w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 transform -translate-x-1/2 -translate-y-1/2 hover:bg-blue-100 hover:scale-110 ${
                          isSelected
                            ? 'bg-blue-600 text-white shadow-lg scale-110'
                            : 'text-gray-700 hover:text-blue-600'
                        }`}
                        style={{
                          left: `calc(50% + ${position.x}px)`,
                          top: `calc(50% + ${position.y}px)`,
                        }}
                      >
                        {hour}
                      </button>
                    );
                  })}

                {/* Minute Numbers */}
                {mode === 'minute' &&
                  minuteNumbers.map((minute, index) => {
                    const position = getClockPosition(index, 12, 100);
                    const isSelected = selectedMinute === minute;

                    return (
                      <button
                        key={minute}
                        type="button"
                        onClick={() => handleMinuteClick(minute)}
                        className={`absolute w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 transform -translate-x-1/2 -translate-y-1/2 hover:bg-blue-100 hover:scale-110 ${
                          isSelected
                            ? 'bg-blue-600 text-white shadow-lg scale-110'
                            : 'text-gray-700 hover:text-blue-600'
                        }`}
                        style={{
                          left: `calc(50% + ${position.x}px)`,
                          top: `calc(50% + ${position.y}px)`,
                        }}
                      >
                        {minute.toString().padStart(2, '0')}
                      </button>
                    );
                  })}
              </div>
            </div>

            {/* Mode Toggle Buttons */}
            <div className="flex justify-center mt-6 space-x-2">
              <Button
                type="button"
                variant={mode === 'hour' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMode('hour')}
                className="px-4"
              >
                Jam
              </Button>
              <Button
                type="button"
                variant={mode === 'minute' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMode('minute')}
                className="px-4"
              >
                Menit
              </Button>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-6">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                Batal
              </Button>
              <Button type="button" onClick={handleTimeSelect} className="flex-1 bg-blue-600 hover:bg-blue-700">
                Pilih
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default function InputTime(props: IProps) {
  const formik = useFormikContext<any>();
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showTimePicker, setShowTimePicker] = useState(false);

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  // Force 24-hour format
  useEffect(() => {
    if (inputRef.current) {
      // Set the input to always use 24-hour format
      inputRef.current.style.setProperty('--webkit-appearance', 'none');
      inputRef.current.style.setProperty('-moz-appearance', 'textfield');

      // Force locale to use 24-hour format
      try {
        // Check if browser supports changing the locale behavior
        const isTime24 =
          new Intl.DateTimeFormat('en-GB', {
            hour: 'numeric',
            hour12: false,
          }).resolvedOptions().hour12 === false;

        if (!isTime24) {
          // Add data attribute to help with CSS targeting
          inputRef.current.setAttribute('data-format', '24');
        }
      } catch {
        // Fallback: just set the attribute
        inputRef.current.setAttribute('data-format', '24');
      }
    }
  }, []);

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const timeValue = e.target.value;

    if (timeValue && timeValue.match(/^\d{2}:\d{2}$/)) {
      if (props.onChange) {
        props.onChange(timeValue);
      } else if (formik?.setFieldValue) {
        formik.setFieldValue(props.name, timeValue);
      }
    }
  };

  const handleTimePickerChange = useCallback(
    (timeValue: string) => {
      if (props.onChange) {
        props.onChange(timeValue);
      } else if (formik?.setFieldValue) {
        formik.setFieldValue(props.name, timeValue);
      }
    },
    [props.onChange, formik?.setFieldValue, props.name],
  );

  const handleInputClick = () => {
    if (props.showTimePicker !== false) {
      setShowTimePicker(true);
    }
  };

  const handleInputFocus = () => {
    if (props.showTimePicker !== false) {
      setShowTimePicker(true);
    }
  };

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  return (
    <div className="grid">
      {props.label && <Label label={props.label} required={props.required} />}
      <div ref={containerRef} className={cn('relative flex items-center dark:bg-card bg-white')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3 z-10">{props.startIcon}</span>
        )}

        <Input
          ref={inputRef}
          data-testid={props.dataTestId}
          autoComplete={props.autoComplete}
          onBlur={props.onBlur ?? formik?.handleBlur}
          onChange={handleTimeChange}
          onClick={handleInputClick}
          onFocus={handleInputFocus}
          value={currentValue}
          name={props.name}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && props.onEnter) {
              props.onEnter();
            }
            // Open time picker with Space or Arrow Down
            if ((e.key === ' ' || e.key === 'ArrowDown') && props.showTimePicker !== false) {
              e.preventDefault();
              setShowTimePicker(true);
            }
          }}
          type="time"
          step={props.step || 60} // Default step 60 detik (1 menit)
          placeholder={props.placeholder || 'HH:MM'}
          lang="en-GB" // Force British English locale which uses 24-hour format
          className={cn(
            props.startIcon ? 'pl-12' : 'pl-10', // Space untuk default clock icon
            props.endIcon ? 'pr-9' : '',
            errorMessage ? ' outline-red-500 border-red-500 bg-red-100' : '',
            // Additional class for 24-hour format styling
            '[&::-webkit-calendar-picker-indicator]:opacity-100',
            // Enhanced styling for better UX
            'cursor-pointer hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200',
            // Hide native time picker when custom picker is enabled
            props.showTimePicker !== false &&
              '[&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden',
          )}
          id={props.id}
          style={{
            // Additional inline styles to ensure 24-hour format
            colorScheme: 'light dark',
          }}
          readOnly={props.showTimePicker !== false} // Make readonly when using custom picker
        />

        {/* Enhanced clock icon with click handler */}
        {!props.startIcon && (
          <button
            type="button"
            onClick={handleInputClick}
            className="absolute left-3 h-4 w-4 text-gray-500 hover:text-blue-500 transition-colors duration-200 cursor-pointer z-10"
            tabIndex={-1}
          >
            <Clock className="h-4 w-4" />
          </button>
        )}

        {props.endIcon && (
          <span className="absolute text-gray-500 right-3 flex items-center pl-3 z-10">{props.endIcon}</span>
        )}

        {/* Custom Time Picker Popup */}
        {props.showTimePicker !== false && (
          <TimePickerPopup
            isOpen={showTimePicker}
            onClose={() => setShowTimePicker(false)}
            value={currentValue}
            onChange={handleTimePickerChange}
            step={props.step || 1}
          />
        )}
      </div>

      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
