import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FocusEventHandler, ReactNode } from 'react';
import { cn } from '@/lib/utils.ts';
import Label from '@/components/ui/Label.tsx';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Input } from '@/components/ui/input.tsx';

interface IProps {
  id?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLInputElement>;
  onChange?: ChangeEventHandler<HTMLInputElement>;
  autoComplete?: string;
  dataTestId?: string;
}

export default function InputTime(props: IProps) {
  const formik = useFormikContext<any>();

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  // Format time input dengan masking
  const formatTimeInput = (value: string): string => {
    // Hapus semua karakter non-digit
    const numbers = value.replace(/\D/g, '');

    // Jika kosong, return kosong
    if (!numbers) return '';

    // Format berdasarkan panjang input
    if (numbers.length <= 2) {
      return numbers;
    } else if (numbers.length <= 4) {
      return `${numbers.slice(0, 2)}:${numbers.slice(2)}`;
    } else {
      return `${numbers.slice(0, 2)}:${numbers.slice(2, 4)}`;
    }
  };

  // Validasi format waktu 24 jam
  const validateTime = (value: string): boolean => {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Jika user mengetik langsung dengan format HH:MM, validasi
    if (inputValue.includes(':')) {
      const parts = inputValue.split(':');
      if (parts.length === 2) {
        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);

        // Validasi hours (0-23) dan minutes (0-59)
        if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
          const formattedValue = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          e.target.value = formattedValue;
        }
      }
    } else {
      // Format dengan masking untuk input numerik
      const formattedValue = formatTimeInput(inputValue);
      e.target.value = formattedValue;
    }

    // Panggil onChange handler
    if (props.onChange) {
      props.onChange(e);
    } else if (formik?.handleChange) {
      formik.handleChange(e);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow backspace, delete, tab, escape, enter, and arrow keys
    if ([8, 9, 27, 13, 37, 38, 39, 40, 46].includes(e.keyCode)) {
      return;
    }

    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    if ((e.ctrlKey || e.metaKey) && [65, 67, 86, 88].includes(e.keyCode)) {
      return;
    }

    // Only allow numbers and colon
    if (![48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 186, 59].includes(e.keyCode)) {
      e.preventDefault();
    }

    // Handle Enter key
    if (e.key === 'Enter' && props.onEnter) {
      props.onEnter();
    }
  };

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  return (
    <div className="grid ">
      {props.label && <Label label={props.label} required={props.required} />}
      <div className={cn('relative flex items-center dark:bg-card bg-white')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3">{props.startIcon}</span>
        )}
        <Input
          data-testid={props.dataTestId}
          autoComplete={props.autoComplete}
          onBlur={props.onBlur ?? formik?.handleBlur}
          onChange={handleInputChange}
          value={currentValue}
          name={props.name}
          onKeyDown={handleKeyDown}
          type="text"
          placeholder={props.placeholder || 'HH:MM'}
          maxLength={5}
          className={cn(
            props.startIcon ? 'pl-12' : '',
            props.endIcon ? 'pr-9' : '',
            errorMessage ? ' outline-red-500 border-red-500 bg-red-100' : '',
          )}
          id={props.id}
        />
        {props.endIcon && (
          <span className="absolute text-gray-500 right-3 flex items-center pl-3">{props.endIcon}</span>
        )}
      </div>
      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
