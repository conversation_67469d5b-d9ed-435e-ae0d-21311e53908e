import PageContainer from '@/components/PageContainer';
import { useDetailSubmissionPage } from './useDetailSubmissionPage';
import PageTitle from '@/components/page-title';
import type { IResDetailSubmission } from '@/types/response/IResDetailSubmission';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { ROUTES } from '@/routes/routes';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  AlertCircle,
  Building,
  CalendarDays,
  CheckCircle,
  Clock,
  Download,
  FileText,
  Mail,
  Phone,
  User,
  XCircle,
} from 'lucide-react';
import type { SubmissionTypeEnum } from '@/types/type/enum-type';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const getStatusColor = (status: any) => {
  switch (status) {
    case 'APPROVED':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'REJECTED':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getStatusIcon = (status: any) => {
  switch (status) {
    case 'APPROVED':
      return <CheckCircle className="h-4 w-4" />;
    case 'REJECTED':
      return <XCircle className="h-4 w-4" />;
    case 'PENDING':
      return <AlertCircle className="h-4 w-4" />;
    default:
      return <AlertCircle className="h-4 w-4" />;
  }
};

const getTypeColor = (type?: SubmissionTypeEnum) => {
  switch (type) {
    case 'COORDINATION':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'OVERTIME':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'WFH':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export default function DetailSubmissionPage() {
  const page = useDetailSubmissionPage();
  const data: IResDetailSubmission | undefined = page?.queryDetail?.data || undefined;

  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Pengajuan',
      path: ROUTES.SUBMISSION_LIST(),
    },
    {
      label: data?.id || '',
    },
  ];
  return (
    <PageContainer>
      <div className="flex items-center justify-between">
        <PageTitle breadcrumb={breadcrumbs} title="Detail Pengajuan" />
        <div className="flex items-center gap-2">
          <Badge className={`${getStatusColor(data?.status)} flex items-center gap-1`}>
            {getStatusIcon(data?.status)}
            {data?.status || '-'}
          </Badge>
          <Badge className={`${getTypeColor(data?.type)}`}>{data?.type || '-'}</Badge>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Pengaju Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informasi Pengaju
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={data?.account_profile_picture || '-'} />
                  <AvatarFallback>
                    {data?.account_name ||
                      '-'
                        .split(' ')
                        .map((n) => n[0])
                        .join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-2">
                  <h3 className="text-xl font-semibold">{data?.account_name || '-'}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Mail className="h-4 w-4" />
                      {data?.account_email || '-'}
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Phone className="h-4 w-4" />
                      {data?.account_phone || '-'}
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <User className="h-4 w-4" />
                      NIP: {data?.account_nip || '-'}
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Building className="h-4 w-4" />
                      {data?.work_unit_name || '-'}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Detail Pengajuan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Tanggal Mulai</label>
                  <div className="flex items-center gap-2 mt-1">
                    <CalendarDays className="h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{formatDate(data?.start || '-')}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tanggal Selesai</label>
                  <div className="flex items-center gap-2 mt-1">
                    <CalendarDays className="h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{formatDate(data?.end || '-')}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Tanggal Pengajuan</label>
                <div className="flex items-center gap-2 mt-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <p className="text-gray-900">{formatDateTime(data?.created_date || '-')}</p>
                </div>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium text-gray-500">Alasan Pengajuan</label>
                <p className="text-gray-900 mt-1 leading-relaxed">{data?.reason || '-'}</p>
              </div>

              {data?.document_url ||
                (data?.document_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Dokumen Pendukung</label>
                    <div className="mt-1">
                      <button className="flex items-center gap-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                        <Download className="h-4 w-4 text-blue-600" />
                        <span className="text-blue-600 text-sm font-medium">Unduh Dokumen</span>
                      </button>
                    </div>
                  </div>
                ))}
            </CardContent>
          </Card>

          {/* Approval Information */}
          {(data?.status === 'APPROVE' || data?.status === 'REJECT') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {data?.status === 'APPROVE' ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  Informasi {data?.status === 'APPROVE' ? 'Persetujuan' : 'Penolakan'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={data?.approve_reject_by_profile_picture || '-'} />
                    <AvatarFallback>
                      {data?.approve_reject_by_name ||
                        '-'
                          .split(' ')
                          .map((n) => n[0])
                          .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h4 className="font-semibold">{data?.approve_reject_by_name || '-'}</h4>
                    <p className="text-sm text-gray-500">ID: {data?.approve_reject_by_id || '-'}</p>
                    <p className="text-sm text-gray-500 mt-1">{formatDateTime(data?.approve_date || '-')}</p>
                  </div>
                </div>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-gray-500">Catatan</label>
                  <p className="text-gray-900 mt-1">{data?.approve_reject_reason || '-'}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Summary */}
        <div className="space-y-6">
          {/* Organization Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building className="h-5 w-5" />
                Informasi Organisasi
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Perangkat daerah</label>
                <p className="text-sm font-medium text-gray-900 mt-1">{data?.agency_name || '-'}</p>
              </div>
              <Separator />
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Unit Kerja</label>
                <p className="text-sm font-medium text-gray-900 mt-1">{data?.work_unit_name || '-'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Ringkasan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Durasi</span>
                <span className="text-sm font-medium">
                  {data?.start && data?.end && (
                    <>
                      {Math.ceil(
                        (new Date(data.end).getTime() - new Date(data.start).getTime()) / (1000 * 60 * 60 * 24),
                      )}{' '}
                      hari
                    </>
                  )}
                </span>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Jenis</span>
                <Badge className={`${getTypeColor(data?.type)} text-xs`}>{data?.type || '-'}</Badge>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Status</span>
                <Badge className={`${getStatusColor(data?.status)} text-xs flex items-center gap-1`}>
                  {getStatusIcon(data?.status)}
                  {data?.status}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-sm font-medium">Pengajuan Dibuat</p>
                    <p className="text-xs text-gray-500">{formatDateTime(data?.created_date || '-')}</p>
                  </div>
                </div>
                {data?.approve_date && data.status !== 'PENDING' && (
                  <div className="flex gap-3">
                    <div
                      className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${data?.status === 'APPROVE' ? 'bg-green-500' : 'bg-red-500'}`}
                    ></div>
                    <div>
                      <p className="text-sm font-medium">{data?.status === 'APPROVE' ? 'Disetujui' : 'Ditolak'}</p>
                      <p className="text-xs text-gray-500">{formatDateTime(data?.approve_date || '-')}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageContainer>
  );
}
