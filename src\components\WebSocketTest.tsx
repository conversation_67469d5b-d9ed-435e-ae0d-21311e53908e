import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useWebSocket } from '@/hooks/useWebSocket';
import type { IResSignIn } from '@/types/response/IResSignIn';
import { Wifi, WifiOff, AlertCircle, CheckCircle, Send } from 'lucide-react';

export default function WebSocketTest() {
  const [testQrCode, setTestQrCode] = useState('test-qr-12345');
  const [receivedMessages, setReceivedMessages] = useState<IResSignIn[]>([]);
  const [testMessage, setTestMessage] = useState('');

  const webSocket = useWebSocket({
    onConnect: () => {
      console.log('WebSocket Test: Connected');
    },
    onDisconnect: () => {
      console.log('WebSocket Test: Disconnected');
    },
    onError: (error) => {
      console.error('WebSocket Test Error:', error);
    },
    onLoginReceived: (loginData: IResSignIn) => {
      console.log('WebSocket Test: Received login data:', loginData);
      setReceivedMessages(prev => [...prev, loginData]);
    },
  });

  const handleConnect = async () => {
    try {
      await webSocket.connect();
    } catch (error) {
      console.error('Failed to connect:', error);
    }
  };

  const handleSubscribe = () => {
    if (testQrCode.trim()) {
      webSocket.subscribeToQRCode(testQrCode.trim());
    }
  };

  const handleDisconnect = () => {
    webSocket.disconnect();
  };

  const clearMessages = () => {
    setReceivedMessages([]);
  };

  const getStatusColor = () => {
    if (webSocket.isConnecting) return 'yellow';
    if (webSocket.isConnected) return 'green';
    if (webSocket.error) return 'red';
    return 'gray';
  };

  const getStatusIcon = () => {
    if (webSocket.isConnecting) return <Wifi className="h-4 w-4 animate-pulse" />;
    if (webSocket.isConnected) return <CheckCircle className="h-4 w-4" />;
    if (webSocket.error) return <AlertCircle className="h-4 w-4" />;
    return <WifiOff className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (webSocket.isConnecting) return 'Connecting...';
    if (webSocket.isConnected) return 'Connected';
    if (webSocket.error) return 'Error';
    return 'Disconnected';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon()}
            WebSocket Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Connection Status */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full bg-${getStatusColor()}-500`}></div>
              <span className="font-medium">Status: {getStatusText()}</span>
            </div>
            <Badge variant={webSocket.isConnected ? 'default' : 'secondary'}>
              {webSocket.isConnected ? 'Online' : 'Offline'}
            </Badge>
          </div>

          {/* Error Display */}
          {webSocket.error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="h-4 w-4" />
                <span className="font-medium">Connection Error</span>
              </div>
              <p className="text-red-700 text-sm mt-1">{webSocket.error}</p>
            </div>
          )}

          {/* Connection Controls */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              onClick={handleConnect}
              disabled={webSocket.isConnected || webSocket.isConnecting}
              className="w-full"
            >
              {webSocket.isConnecting ? 'Connecting...' : 'Connect WebSocket'}
            </Button>
            <Button
              onClick={handleDisconnect}
              disabled={!webSocket.isConnected}
              variant="outline"
              className="w-full"
            >
              Disconnect
            </Button>
            <Button
              onClick={clearMessages}
              variant="secondary"
              className="w-full"
            >
              Clear Messages
            </Button>
          </div>

          {/* QR Code Subscription */}
          <div className="space-y-3">
            <h3 className="font-semibold">QR Code Topic Subscription</h3>
            <div className="flex gap-3">
              <Input
                value={testQrCode}
                onChange={(e) => setTestQrCode(e.target.value)}
                placeholder="Enter QR code to subscribe"
                className="flex-1"
              />
              <Button
                onClick={handleSubscribe}
                disabled={!webSocket.isConnected || !testQrCode.trim()}
              >
                <Send className="h-4 w-4 mr-2" />
                Subscribe
              </Button>
            </div>
            <p className="text-sm text-gray-600">
              Topic: <code className="bg-gray-100 px-2 py-1 rounded">/topic/{testQrCode}</code>
            </p>
          </div>

          {/* Connection Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Connection Status:</span>
              <p className={`mt-1 ${
                webSocket.isConnected ? 'text-green-600' : 
                webSocket.isConnecting ? 'text-yellow-600' : 
                'text-gray-600'
              }`}>
                {getStatusText()}
              </p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Reconnect Attempts:</span>
              <p className="mt-1 text-gray-600">
                {webSocket.connectionStatus.reconnectAttempts} / {webSocket.connectionStatus.maxReconnectAttempts}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Received Messages */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Received Login Messages</span>
            <Badge variant="outline">{receivedMessages.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {receivedMessages.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Send className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No messages received yet</p>
              <p className="text-sm mt-1">Connect and subscribe to a topic to receive messages</p>
            </div>
          ) : (
            <div className="space-y-3">
              {receivedMessages.map((message, index) => (
                <div key={index} className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-green-800">Login Message #{index + 1}</span>
                    <Badge variant="outline" className="text-green-700 border-green-300">
                      {new Date().toLocaleTimeString()}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Access Token:</span>
                      <code className="block bg-white p-2 rounded mt-1 text-xs break-all">
                        {message.access_token.substring(0, 50)}...
                      </code>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Account Data:</span>
                      <code className="block bg-white p-2 rounded mt-1 text-xs">
                        {JSON.stringify(message.account_data, null, 2).substring(0, 100)}...
                      </code>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>Click "Connect WebSocket" to establish connection</li>
            <li>Enter a QR code value and click "Subscribe" to listen for messages</li>
            <li>From server/mobile app, send a message to the topic: <code>/topic/{'{qr_code}'}</code></li>
            <li>Message should appear in the "Received Login Messages" section</li>
            <li>Test different QR codes by changing the input and subscribing again</li>
          </ol>
          
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> Make sure your WebSocket server is running at the configured URL 
              and supports STOMP protocol for this test to work properly.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
