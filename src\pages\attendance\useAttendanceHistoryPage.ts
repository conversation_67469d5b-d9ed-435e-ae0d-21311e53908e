import { AttendanceRepository } from '@/repositories/attendance-repository.ts';
import { useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import type { IFilterList } from '@/types/type/IFilterList.ts';
import DateHelper from '@/lib/date-helper.ts';
import { addDays, subDays } from 'date-fns';
import { buildSearchParams } from '@/utils/search-params.utils.ts';
import { useQuery } from '@tanstack/react-query';
import { ENV } from '@/constants/env.ts';

const today = new Date();
const defaultStartDate = subDays(today, 7);
const defaultEndDate = today;

export function useAttendanceHistoryPage() {
  const attendanceRepository = new AttendanceRepository();
  const [searchParams, setSearchParams] = useSearchParams();
  const [loadingDownloadFile, setLoadingDownloadFile] = useState(false);
  const initialStartDate = searchParams.get('start_date')
    ? new Date(searchParams.get('start_date')!)
    : defaultStartDate;

  const initialEndDate = searchParams.get('end_date') ? new Date(searchParams.get('end_date')!) : defaultEndDate;

  const [selectedDate, setSelectedDate] = useState<Date[]>([initialStartDate, initialEndDate]);

  const [filterData, setFilterData] = useState<IFilterList>(() => ({
    agency_id: searchParams.get('agency_id') || '',
    page: parseInt(searchParams.get('page') || '0'),
    size: parseInt(searchParams.get('size') || '10'),
    start_date: searchParams.get('start_date') || DateHelper.toFormatDate(initialStartDate, 'yyyy-MM-dd'),
    end_date: searchParams.get('end_date') || DateHelper.toFormatDate(addDays(initialEndDate, 2), 'yyyy-MM-dd'),
  }));

  useEffect(() => {
    const params = buildSearchParams(filterData);
    setSearchParams(params);
  }, [filterData, setSearchParams]);

  const queryList = useQuery({
    queryKey: ['attendance_list', filterData],
    queryFn: () => attendanceRepository.getAttendance(filterData),
  });

  function handlePaginationChange(params: { page?: number; size?: number }) {
    setFilterData((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }

  function handleDateChange(date: Date[]) {
    setSelectedDate(date);
    setFilterData((prev) => ({
      page: 0,
      size: prev.size,
      start_date: DateHelper.toFormatDate(date[0], 'yyyy-MM-dd'),
      end_date: DateHelper.toFormatDate(date[1], 'yyyy-MM-dd'),
    }));
  }

  function onDownloadCSV() {
    setLoadingDownloadFile(true);
    const startDate = filterData.start_date;
    const endDate = filterData.end_date;
    const url = `${ENV.BASE_URL}/attendance/v1/history/csv?start_date=${startDate}&end_date=${endDate}`;

    fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'text/csv',
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error('Failed to download file');
        }
        return response.blob();
      })
      .then((blob) => {
        const fileUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = `attendance-${startDate}_to_${endDate}.csv`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(fileUrl);
        setLoadingDownloadFile(false);
      })
      .catch((error) => {
        setLoadingDownloadFile(false);
        console.error('Download error:', error);
      });
  }

  const dataList = queryList.data?.response_data || [];
  return {
    queryList,
    dataList,
    handlePaginationChange,
    selectedDate,
    setSelectedDate,
    handleDateChange,
    onDownloadCSV,
    loadingDownloadFile,
  };
}
