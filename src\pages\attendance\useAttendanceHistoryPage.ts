import { ENV } from '@/constants/env.ts';
import { useAuth } from '@/hooks/use-auth';
import { useUi } from '@/hooks/useUi';
import DateHelper from '@/lib/date-helper.ts';
import { AttendanceRepository } from '@/repositories/attendance-repository.ts';
import { MasterDataRepository } from '@/repositories/master-data-repository';
import type { IFilterList } from '@/types/type/IFilterList.ts';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { addDays, subDays } from 'date-fns';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const today = new Date();
const defaultStartDate = subDays(today, 7);
const defaultEndDate = today;

export function useAttendanceHistoryPage() {
  const [searchParams] = useSearchParams();

  const [searchValue, setSearchValue] = useState<string>('');
  const attendanceRepository = new AttendanceRepository();
  const masterDataRepository = new MasterDataRepository();
  const initialStartDate = defaultStartDate;
  const initialEndDate = defaultEndDate;
  const auth = useAuth();
  const accountId = searchParams.get('account_id');


  const defaultAgencyId = auth?.user?.role !== 'SUPER_ADMIN' ? auth?.user?.agency_id : undefined;
  const { toast } = useUi();
  const initFilter: IFilterList = {
    agency_id: defaultAgencyId,
    work_unit_id: undefined,
    account_id: accountId || undefined,
    page: 0,
    size: 10,
    role: auth.user?.role === 'MAYOR' ? 'PD_HEAD' : undefined,
    start_date: DateHelper.toFormatDate(initialStartDate, 'yyyy-MM-dd'),
    end_date: DateHelper.toFormatDate(addDays(initialEndDate, 2), 'yyyy-MM-dd'),
  };
  const [selectedDate, setSelectedDate] = useState<Date[]>([initialStartDate, initialEndDate]);
  const [loadingDownloadFile, setLoadingDownloadFile] = useState(false);
  const [openFilter, setOpenFilter] = useState<boolean>(false);

  const [filterData, setFilterData] = useState<IFilterList>(initFilter);

  const queryRole = useQuery({
    queryKey: ['list_all_role'],
    queryFn: async () => await masterDataRepository.listAllRole(),
  });

  const queryAgency = useQuery({
    queryKey: ['list_agency_master_data'],
    queryFn: async () => await masterDataRepository.getAgency(),
  });

  const queryWorkUnit = useQuery({
    queryKey: ['list_work_unit_by_agency', filterData.agency_id],
    queryFn: async () => await masterDataRepository.getWorkUnitByAgency(filterData.agency_id || ''),
    enabled: !!filterData.agency_id,
  });

  const queryList = useQuery({
    queryKey: ['attendance_history_list', filterData],
    queryFn: () => attendanceRepository.getAttendanceRecapHistory(filterData),
  });

  function handlePaginationChange(params: { page?: number; size?: number }) {
    setFilterData((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }

  function handleDateChange(date: Date[]) {
    setSelectedDate(date);
    setFilterData((prev) => ({
      ...prev,
      page: 0,
      size: prev.size,
      start_date: DateHelper.toFormatDate(date[0], 'yyyy-MM-dd'),
      end_date: DateHelper.toFormatDate(date[1], 'yyyy-MM-dd'),
    }));
  }

  function onDownloadExcel() {
    setLoadingDownloadFile(true);
    const startDate = filterData.start_date;
    const endDate = filterData.end_date;
    const params = buildSearchParams(filterData);
    const url = `${ENV.BASE_URL}/attendance/v1/history/excel${buildQueryString(params)}`;

    axios({
      method: 'GET',
      url: url,
      headers: {
        Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        Authorization: `Bearer ${auth?.token}`,
      },
      responseType: 'blob',
    })
      .then((response: any) => {
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        // Create download link
        const fileUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = `attendance-${startDate}_to_${endDate}.xlsx`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(fileUrl);

        setLoadingDownloadFile(false);
      })
      .catch((error: any) => {
        setLoadingDownloadFile(false);
        console.error('Error downloading Excel file:', error);
        toast.error('Terjadi kesalahan saat mengunduh, silahkan coba lagi');
        throw new Error(error);
      });
  }

  function handleResetFilter() {
    setSelectedDate([initialStartDate, initialEndDate]);
    setFilterData(initFilter);
  }

  function submitFilter() {
    setOpenFilter(false);
    queryList.refetch();
  }

  const dataAgency: ILabelValue<string>[] = (queryAgency.data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });

  const dataWorkUnit: ILabelValue<string>[] = (queryWorkUnit.data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });

  function onChangeAgency(e: string) {
    setFilterData((prev) => ({
      ...prev,
      work_unit_id: undefined,
      agency_id: e,
    }));
  }

  function onChangeWorkUnit(e: string) {
    setFilterData((prev) => ({
      ...prev,
      work_unit_id: e,
    }));
  }

  const dataList = queryList.data?.response_data || [];

  const dataRole: ILabelValue<string | undefined>[] = [
    {
      label: 'Semua',
      value: 'all',
    },
    ...((Array.isArray(queryRole?.data) ? queryRole.data : []) as Array<{ name: string; role_enum: string }>).map(
      (e) => ({
        label: e.name,
        value: e.role_enum,
      }),
    ),
  ];

  function onChangeFilterRole(e?: string) {
    if (e !== 'all') {
      setFilterData({ ...filterData, page: 0, role: e });
    } else {
      setFilterData({ ...filterData, role: undefined });
    }
  }

  function onSubmitSearch() {
    setFilterData({ ...filterData, q: searchValue, page: 0 });
  }

  function onResetSearch() {
    setSearchValue('');
    setFilterData({ ...filterData, page: 0, q: undefined });
  }

  return {
    queryList,
    dataList,
    handlePaginationChange,
    selectedDate,
    setSelectedDate,
    handleDateChange,
    onDownloadExcel,
    loadingDownloadFile,
    submitFilter,
    handleResetFilter,
    openFilter,
    dataAgency,
    setOpenFilter,
    onChangeAgency,
    queryAgency,
    filterData,
    setFilterData,
    dataWorkUnit,
    auth,
    onChangeWorkUnit,
    queryWorkUnit,
    accountId,
    dataRole,
    onChangeFilterRole,
    searchValue,
    setSearchValue,
    onSubmitSearch,
    onResetSearch,
  };
}
