import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import AppTable, { type ITableColumn } from '@/components/AppTable.tsx';
import type { IResListAttendance } from '@/types/response/IResListAttendance.ts';
import DateHelper from '@/lib/date-helper.ts';
import { formatTimeGapToInitials } from '@/lib/utils.ts';
import AppPagination from '@/components/AppPagination.tsx';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { DateRangePicker } from '@/components/ui/DateRangePicker.tsx';
import { useAttendanceHistoryPage } from '@/pages/attendance/useAttendanceHistoryPage.ts';
import { Button } from '@/components/ui/button.tsx';
import { FileDown } from 'lucide-react';

export default function AttendanceHistoryPage() {
  const page = useAttendanceHistoryPage();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Riwayat Absensi',
    },
  ];

  const tableColumn: ITableColumn<IResListAttendance>[] = [
    {
      headerTitle: 'Pegawai',
      component: (e) => <div>{e.account_name}</div>,
    },
    {
      headerTitle: 'Waktu Absen',
      component: (e) => (
        <div>
          {DateHelper.toFormatDate(e.date_time, 'dd LLLL, yyyy - HH:mm')} ({formatTimeGapToInitials(e.time_gap)})
        </div>
      ),
    },
    {
      headerTitle: 'Nama Absensi',
      component: (e) => (
        <div>
          <div className={'font-semibold'}>{e.time_table_name}</div>
          <div>
            {e.record_start_time} - {e.record_end_time}
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Status',
      component: (e) => <div>{e.status}</div>,
    },
    {
      headerTitle: 'Unit Kerja',
      component: (e) => (
        <div>
          <p className={'font-semibold'}>{e.work_unit_name}</p>
          <p className={'text-xs text-muted-foreground'}>{e.agency_name}</p>
        </div>
      ),
    },
  ];
  return (
    <PageContainer>
      <div className={'flex justify-between items-center'}>
        <PageTitle title={'Riwayat Absensi'} breadcrumb={breadcrumb} />
        <div className={'flex items-center gap-2'}>
          <DateRangePicker
            initialDateFrom={page.selectedDate[0]}
            initialDateTo={page.selectedDate[1]}
            onUpdate={(e) => page.handleDateChange([e.range.from, e.range.to || new Date()])}
            showCompare={false}
          />
          <Button onClick={page.onDownloadCSV} loading={page.loadingDownloadFile}>
            <FileDown /> Download Sebagai CSV
          </Button>
        </div>
      </div>
      <AppTable data={page.dataList} column={tableColumn} loading={page.queryList.isFetching} />
      {page.queryList.data?.paginated_data && (
        <AppPagination
          onPaginationChange={page.handlePaginationChange}
          dataPagination={page.queryList.data?.paginated_data}
        />
      )}
    </PageContainer>
  );
}
