import AppPagination from '@/components/AppPagination.tsx';
import AppTable, { type ITableColumn } from '@/components/AppTable.tsx';
import FilterList from '@/components/FilterList';
import InputSearch from '@/components/InputSearch';
import InputSelect from '@/components/InputSelect';
import IsAttendingText from '@/components/IsAttendingText';
import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import { DateRangePicker } from '@/components/ui/DateRangePicker.tsx';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button.tsx';
import DateHelper from '@/lib/date-helper.ts';
import { useAttendanceHistoryPage } from '@/pages/attendance/useAttendanceHistoryPage.ts';
import type { IResRecapHistoryAttendance } from '@/types/response/IResRecapHistory';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { FileDown } from 'lucide-react';

export default function AttendanceHistoryPage() {
  const page = useAttendanceHistoryPage();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Riwayat Absensi',
    },
  ];

  const tableColumn: ITableColumn<IResRecapHistoryAttendance>[] = [
    {
      headerTitle: 'Pegawai',
      component: (e) => (
        <div className="flex items-center gap-2">
          <img
            className="h-8 w-8 rounded-full border border-black"
            src={e.account_profile_picture}
            alt={e.account_name}
          />
          <div>
            <div className="font-semibold">{e.account_name}</div>
            <div className="text-gray-500 text-xs">{e.account_nip}</div>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Kehadiran',
      component: (e) => <IsAttendingText isAttending={e.is_attending} />,
    },
    {
      headerTitle: 'Tanggal',
      component: (e) => (
        <div>
          <div>{e.date_time && DateHelper.toFormatDate(new Date(e.date_time), 'dd MMMM yyyy')} </div>
        </div>
      ),
    },
    {
      headerTitle: 'Waktu',
      component: (e) => (
        <div>
          <div>{e.is_attending ? e.date_time && DateHelper.toFormatDate(new Date(e.date_time), 'HH:mm:ss') : ''} </div>
        </div>
      ),
    },
    {
      headerTitle: 'Nama Absensi',
      component: (e) => (
        <div>
          <div className={'font-semibold'}>
            {e?.time_table_name || 'ABSENSI KHUSUS'}{' '}
            <p className="font-normal">{e?.reason_type ? `(${e.reason_type_string})` : ''}</p>
          </div>
          {e.time_table_name && e.record_start_time && e.record_end_time && (
            <div>
              {e.record_start_time} - {e.record_end_time}
            </div>
          )}
        </div>
      ),
    },

    {
      headerTitle: 'Unit Kerja',
      component: (e) => (
        <div>
          <p className={'font-semibold'}>{e.work_unit_name}</p>
          <p className={'text-xs text-muted-foreground'}>{e.agency_name}</p>
        </div>
      ),
    },
  ];
  return (
    <PageContainer>
      <div className={'flex justify-between items-center'}>
        <PageTitle title={'Riwayat Absensi'} breadcrumb={breadcrumb} />
        <div className={'flex items-center gap-2'}>
          <DateRangePicker
            initialDateFrom={page.selectedDate[0]}
            initialDateTo={page.selectedDate[1]}
            onUpdate={(e) => page.handleDateChange([e.range.from, e.range.to || new Date()])}
            showCompare={false}
          />

          <Button onClick={page.onDownloadExcel} loading={page.loadingDownloadFile}>
            <FileDown /> Export Ke Excel
          </Button>
        </div>
      </div>
      <Alert>
        <InfoCircledIcon />
        <AlertTitle className="font-semibold">Rekap Absensi Otomatis</AlertTitle>
        <AlertDescription>
          Riwayat absensi akan diproses otomatis setiap hari di jam 00:00 WIB untuk data hari sebelumnya.
        </AlertDescription>
      </Alert>
      <div className="flex gap-3">
        <div className="flex-1">
          <InputSearch
            active={!!page?.filterData?.q}
            placeholder="Cari Nama pegawai"
            searchValue={page.searchValue}
            setSearchValue={page.setSearchValue}
            handleReset={page.onResetSearch}
            handleSearch={() => page.onSubmitSearch()}
          />
        </div>
        {!page?.accountId && (
          <FilterList
            open={page.openFilter}
            onOpenChange={page.setOpenFilter}
            onSubmit={() => page.submitFilter()}
            onReset={page.handleResetFilter}
          >
            <div className="grid gap-3">
              <InputSelect
                disabled={page?.auth?.user?.role !== 'SUPER_ADMIN'}
                label="Perangkat daerah"
                placeholder="Pilih Perangkat daerah"
                disableFormik
                options={page.dataAgency}
                onValueChange={page.onChangeAgency}
                name="filter_agency"
                id="filter_agency"
                value={page.filterData.agency_id}
              />

              <InputSelect
                disabled={!page.filterData.agency_id}
                label="Unit Kerja"
                placeholder="Pilih Unit Kerja"
                disableFormik
                options={page.dataWorkUnit}
                onValueChange={page.onChangeWorkUnit}
                name="filter_work_unit"
                id="filter_work_unit"
                value={page.filterData.work_unit_id}
              />

              <InputSelect
                label="Role"
                placeholder="Pilih Role User"
                disableFormik
                options={page.dataRole}
                onValueChange={(e) => page.onChangeFilterRole(e)}
                name="filter_role"
                id="filter_role"
                value={page?.filterData?.role || 'all'}
              />
            </div>
          </FilterList>
        )}
      </div>
      <AppTable data={page.dataList} column={tableColumn} loading={page.queryList.isFetching} />
      {page.queryList.data?.paginated_data && (
        <AppPagination
          onPaginationChange={page.handlePaginationChange}
          dataPagination={page.queryList.data?.paginated_data}
        />
      )}
    </PageContainer>
  );
}
