import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailSubmission } from '@/types/response/IResDetailSubmission';
import type { IResListSubmission } from '@/types/response/IResListSubmission';
import type { BaseResponse, BaseResponsePaginated } from '@/types/response/IResModel';
import type { IFilterList } from '@/types/type/IFilterList.ts';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils.ts';

export class SubmissionRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async getListSubmission(filter: IFilterList) {
    const params = buildSearchParams(filter);
    return await this.httpService
      .GET(ENDPOINT.LIST_SUBMISSION() + buildQueryString(params))
      .then((res: BaseResponsePaginated<IResListSubmission[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
      });
  }

  async approveSubmission(id: string) {
    return await this.httpService
      .PATCH(ENDPOINT.APPROVE_SUBMISSION(id))
      .then((res) => res.data)
      .catch((e) => this.errorService.fetchApiError(e));
  }
  async rejectSubmission(id: string) {
    return await this.httpService
      .PATCH(ENDPOINT.REJECT_SUBMISSION(id))
      .then((res) => res.data)
      .catch((e) => this.errorService.fetchApiError(e));
  }

  async detailSubmission(id: string) {
    return await this.httpService
      .GET(ENDPOINT.DETAIL_SUBMISSION(id))
      .then((res: BaseResponse<IResDetailSubmission>) => {
        return res.data?.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }
}
