import InputAutocomplete from '@/components/InputAutocomplete';
import InputDate from '@/components/InputDate.tsx';
import InputRadioGroup from '@/components/InputRadioGroup';
import InputSelect from '@/components/InputSelect';
import InputText from '@/components/InputText';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { useAppSelector } from '@/redux/store';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { FormikProvider } from 'formik';
import { AlertCircle, ArrowLeft, Building2, CheckCircle, Edit3, Save, Users } from 'lucide-react';
import { Link } from 'react-router-dom';
import EmployeeFormSkeleton from './EmployeeFormSkeleton.tsx';
import { useEmployeeFormPage } from './useEmployeeFormPage';
import AddressModule from '@/components/AddressModule.tsx';
import { useAuth } from '@/hooks/use-auth.ts';

export default function EmployeeFormPage() {
  const page = useEmployeeFormPage();
  const auth = useAuth();
  const isEdit = !!page?.id;

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Pegawai',
      path: ROUTES.EMPLOYEE_LIST(),
    },
    {
      label: isEdit ? 'Edit Pegawai' : 'Tambah Pegawai',
    },
  ];

  const dataWorkUnit = page.queryWorkUnit.data || [];
  const listReligion = useAppSelector((state) => state.MasterData.listReligion);
  const listMaritalStatus = useAppSelector((state) => state.MasterData.listMaritalStatus);

  const genderOptions = [
    { label: 'Laki-laki', value: 'MALE' },
    { label: 'Perempuan', value: 'FEMALE' },
  ];

  if (isEdit && !page.formik.values.name) {
    return (
      <PageContainer className="max-w-4xl mx-auto">
        <PageTitle title={isEdit ? 'Edit Pegawai' : 'Tambah Pegawai'} breadcrumb={breadcrumb} />
        <EmployeeFormSkeleton />
      </PageContainer>
    );
  }

  return (
    <PageContainer className="max-w-4xl mx-auto">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <PageTitle title={isEdit ? 'Edit Pegawai' : 'Tambah Pegawai'} breadcrumb={breadcrumb} />
        <div className="flex items-center gap-3">
          <Link to={ROUTES.EMPLOYEE_LIST()}>
            <Button variant="outline" className="w-full sm:w-auto">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali
            </Button>
          </Link>
        </div>
      </div>

      <FormikProvider value={page.formik}>
        <div className="space-y-6">
          {/* Form Progress Indicator */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      page.formik.values.name ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    <Users className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Data Pribadi</span>
                </div>
                <div className="flex-1 h-px bg-gray-200"></div>
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      page.formik.values.agency_id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    <Building2 className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Penempatan</span>
                </div>
                <div className="flex-1 h-px bg-gray-200"></div>
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      page.formik.values.checked ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    <CheckCircle className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Konfirmasi</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Data Pribadi Pegawai</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Masukkan informasi pribadi dan kontak pegawai</p>
                </div>
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="grid gap-8">
                <InputText
                  alignment="horizontal"
                  name="name"
                  id="name"
                  label="Nama Lengkap"
                  required
                  placeholder="Contoh: Ahmad Budi Santoso"
                />
                <InputText
                  alignment="horizontal"
                  name="front_degree"
                  id="front_degree"
                  label="Gelar Depan"
                  placeholder="Contoh: Dr., Ir."
                />

                <InputText
                  alignment="horizontal"
                  name="back_degree"
                  id="back_degree"
                  label="Gelar Belakang"
                  placeholder="Contoh: S.T., M.Kom., Amd."
                />
                <InputText
                  alignment="horizontal"
                  name="nip"
                  id="nip"
                  label="NIP (Nomor Induk Pegawai)"
                  required
                  placeholder="Contoh: 198501012010011001"
                />

                <InputDate
                  alignment="horizontal"
                  name="date_of_birth"
                  id="date_of_birth"
                  label="Tanggal Lahir"
                  required
                  placeholder="Masukan tanggal lahir"
                />
                <InputSelect
                  alignment="horizontal"
                  id="religion"
                  name="religion"
                  label="Agama"
                  required
                  options={listReligion}
                  placeholder="Pilih agama"
                />
                <InputSelect
                  alignment="horizontal"
                  id="marital_status"
                  name="marital_status"
                  label="Status Pernikahan"
                  required
                  options={listMaritalStatus}
                  placeholder="Pilih status pernikahan"
                />
                <InputText
                  alignment="horizontal"
                  name="email"
                  id="email"
                  label="Email"
                  required
                  placeholder="Contoh: <EMAIL>"
                />
                <InputText
                  alignment="horizontal"
                  name="phone"
                  id="phone"
                  label="Nomor WhatsApp"
                  required
                  startIcon={'+62'}
                  placeholder="81234567890"
                />
                {auth.checkingPrivilege('EDIT_EMPLOYEE_ROLE') && (
                  <InputSelect
                    alignment="horizontal"
                    id="role"
                    name="role"
                    label="Role"
                    required
                    options={page.dataRole}
                    placeholder="Pilih "
                  />
                )}
                <InputRadioGroup
                  alignment="horizontal"
                  id="gender"
                  name="gender"
                  label="Jenis Kelamin"
                  required
                  options={genderOptions}
                />
              </div>
            </CardContent>
          </Card>

          <AddressModule formik={page.formik} />

          {auth.checkingPrivilege('EDIT_WORK_UNIT') && (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Building2 className="w-5 h-5 text-orange-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">Penempatan Kerja</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">Tentukan Perangkat daerah dan unit kerja untuk pegawai</p>
                  </div>
                </div>
              </CardHeader>
              <Separator />
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <InputAutocomplete
                    label="Perangkat daerah"
                    placeholder="Pilih Perangkat daerah tempat pegawai bekerja"
                    name="agency_id"
                    id="agency_id"
                    required
                    options={page.listAgency}
                  />

                  {page.formik.values.agency_id && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-blue-700">
                        <CheckCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">Perangkat daerah telah dipilih</span>
                      </div>
                      <div className="text-sm text-blue-600 mt-1">Unit kerja yang tersedia akan dimuat otomatis</div>
                    </div>
                  )}

                  <InputAutocomplete
                    label="Unit Kerja"
                    placeholder={
                      !page.formik.values.agency_id
                        ? 'Pilih Perangkat daerah terlebih dahulu'
                        : 'Pilih unit kerja dalam Perangkat daerah'
                    }
                    name="work_unit_id"
                    id="work_unit_id"
                    required
                    disabled={!page.formik.values.work_unit_id}
                    options={dataWorkUnit}
                  />

                  {!page.formik.values.agency_id && (
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-amber-700">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">Pilih Perangkat daerah terlebih dahulu</span>
                      </div>
                      <div className="text-sm text-amber-600 mt-1">
                        Unit kerja akan tersedia setelah memilih Perangkat daerah
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Confirmation Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Konfirmasi</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Pastikan semua data sudah benar sebelum menyimpan</p>
                </div>
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="space-y-6">
                {/* Form Validation Summary */}
                {!page.formik.isValid && page.formik.touched.name && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-red-700">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Mohon lengkapi data berikut:</span>
                    </div>
                    <ul className="text-sm text-red-600 mt-2 space-y-1">
                      {page.formik.errors.name && <li>• Nama lengkap wajib diisi</li>}
                      {page.formik.errors.nip && <li>• NIP wajib diisi</li>}
                      {page.formik.errors.email && <li>• Email wajib diisi</li>}
                      {page.formik.errors.phone && <li>• Nomor WhatsApp wajib diisi dengan format yang benar</li>}
                      {page.formik.errors.agency_id && <li>• Perangkat daerah wajib dipilih</li>}
                    </ul>
                  </div>
                )}

                {page.formik.values.name && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">Ringkasan Data Pegawai:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Nama:</span>
                          <span className="font-medium">{page.formik.values.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">NIP:</span>
                          <span className="font-medium">{page.formik.values.nip || '-'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Email:</span>
                          <span className="font-medium">{page.formik.values.email || '-'}</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">WhatsApp:</span>
                          <span className="font-medium">
                            {page.formik.values.phone ? `+62${page.formik.values.phone}` : '-'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Perangkat daerah:</span>
                          <span className="font-medium">
                            {page.listAgency.find((a) => a.value === page.formik.values.agency_id)?.label || '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg">
                  <Checkbox
                    checked={page.formik.values.checked}
                    onCheckedChange={() => page.formik.setFieldValue('checked', !page.formik.values.checked)}
                    id="confirmation"
                    className="mt-0.5"
                  />
                  <div className="flex-1">
                    <label htmlFor="confirmation" className="text-sm font-medium text-gray-900 cursor-pointer">
                      {isEdit ? 'Konfirmasi Perubahan Data Pegawai' : 'Konfirmasi Penambahan Pegawai Baru'}
                    </label>
                    <p className="text-xs text-gray-600 mt-1">
                      {isEdit
                        ? 'Saya yakin untuk memperbarui data pegawai dengan informasi yang telah dimasukkan'
                        : 'Saya yakin untuk menambahkan pegawai baru dengan informasi yang telah dimasukkan'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <Separator />
            <CardFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between">
              <Link to={ROUTES.EMPLOYEE_LIST()} className="w-full sm:w-auto">
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Batal
                </Button>
              </Link>
              <Button
                onClick={() => page.formik.handleSubmit()}
                disabled={!page.formik.isValid || !page.formik.values.checked}
                loading={page.mutationCreate.isPending || page.mutationEdit.isPending}
                className="w-full sm:w-auto"
              >
                {isEdit ? (
                  <>
                    <Edit3 className="w-4 h-4 mr-2" />
                    Perbarui Pegawai
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Simpan Pegawai
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </FormikProvider>
    </PageContainer>
  );
}
