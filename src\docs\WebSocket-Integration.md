# WebSocket Integration for QR Code Login System

## Overview
Implementasi WebSocket menggunakan STOMP.js untuk sistem QR code login yang memungkinkan web application menerima notifikasi login real-time dari mobile app.

## Architecture

### WebSocket Flow
1. **User clicks "Mulai QR Code Login"** → WebSocket connects to `{{ws-url}}/ws`
2. **QR Code generated** → Subscribe to topic `/topic/{qr_code}`
3. **Admin scans QR on mobile** → Server sends `IResSignIn` data to topic
4. **Web app receives data** → Process authentication and redirect
5. **QR refreshes every minute** → Update subscription to new topic

### Components Structure
```
src/
├── services/
│   └── websocket.service.ts     # Core WebSocket service with STOMP.js
├── hooks/
│   └── useWebSocket.ts          # React hook for WebSocket management
├── pages/auth/
│   └── useSignInPage.ts         # Enhanced with WebSocket integration
└── components/
    ├── QRCodeWithTimer.tsx      # Enhanced with WebSocket status
    └── QRCodeExample.tsx        # Demo component
```

## Implementation Details

### 1. WebSocket Service (`websocket.service.ts`)

```typescript
export class WebSocketService {
  private client: Client | null = null;
  private currentSubscription: any = null;
  
  // Core methods
  public connect(): Promise<void>
  public subscribeToQRCode(qrCode: string): void
  public unsubscribeFromCurrentTopic(): void
  public disconnect(): void
}
```

**Key Features:**
- STOMP.js client management
- Auto-reconnection with exponential backoff
- Topic subscription management
- Error handling and callbacks
- Connection status monitoring

### 2. React Hook (`useWebSocket.ts`)

```typescript
export function useWebSocket(options: UseWebSocketOptions): UseWebSocketReturn {
  // State management
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Methods
  return {
    isConnected, isConnecting, error,
    connect, disconnect, subscribeToQRCode, unsubscribeFromCurrentTopic
  };
}
```

**Features:**
- React state integration
- Callback management
- Auto cleanup on unmount
- Connection status tracking

### 3. Enhanced QR Login Hook (`useSignInPage.ts`)

```typescript
// WebSocket integration
const handleWebSocketLogin = useCallback((loginData: IResSignIn) => {
  // Store tokens and user data
  localStorage.setItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, loginData.access_token);
  localStorage.setItem(LOCAL_STORAGE_KEY.USER, JSON.stringify(loginData.account_data));
  
  // Navigate and cleanup
  navigate(ROUTES.HOME());
  stopQrTimer();
  window.location.reload();
}, [navigate]);

const webSocket = useWebSocket({
  onLoginReceived: handleWebSocketLogin,
  // ... other callbacks
});
```

## Configuration

### Environment Variables
Add to `.env` file:
```env
VITE_APP_WS_URL=ws://localhost:8080/ws
```

### Dependencies
```bash
npm install @stomp/stompjs
```

## API Integration

### Server-side Requirements

#### WebSocket Endpoint
```
{{ws-url}}/ws
```

#### Topic Pattern
```
/topic/{qr_code}
```

#### Message Format
Server sends `IResSignIn` data:
```typescript
interface IResSignIn {
  access_token: string;
  account_data: IResGetMe;
}
```

#### Server Implementation Example
```java
// When mobile app scans QR and authenticates
@Autowired
private SimpMessagingTemplate simpMessagingTemplate;

public void sendLoginNotification(String qrCode, IResSignIn loginData) {
    simpMessagingTemplate.convertAndSend("/topic/" + qrCode, loginData);
}
```

## Usage Examples

### Basic Integration
```typescript
import useSignInPage from '@/pages/auth/useSignInPage';

function LoginPage() {
  const {
    qrValue,
    count,
    isQrActive,
    onClickQr,
    webSocketConnected,
    webSocketConnecting,
    webSocketError,
  } = useSignInPage();

  return (
    <div>
      {!isQrActive ? (
        <button onClick={onClickQr}>Start QR Login</button>
      ) : (
        <QRCodeWithTimer
          qrValue={qrValue}
          count={count}
          webSocketConnected={webSocketConnected}
          webSocketConnecting={webSocketConnecting}
          webSocketError={webSocketError}
        />
      )}
    </div>
  );
}
```

### Advanced Usage with Custom Callbacks
```typescript
const webSocket = useWebSocket({
  onConnect: () => console.log('Connected to WebSocket'),
  onDisconnect: () => console.log('Disconnected from WebSocket'),
  onError: (error) => console.error('WebSocket error:', error),
  onLoginReceived: (loginData) => {
    // Custom login processing
    processLogin(loginData);
  },
});
```

## Features

### ✅ Real-time Communication
- **STOMP.js integration**: Professional WebSocket communication
- **Topic-based messaging**: Subscribe to specific QR code topics
- **Auto-reconnection**: Handles connection drops gracefully
- **Error handling**: Comprehensive error management

### ✅ QR Code Integration
- **Dynamic subscription**: New QR = new topic subscription
- **Auto-cleanup**: Unsubscribe when QR expires or stops
- **Status monitoring**: Real-time connection status display
- **Visual feedback**: Connection indicators and error messages

### ✅ Authentication Flow
- **Seamless login**: Automatic authentication on message receipt
- **Token management**: Proper storage of access tokens and user data
- **Navigation**: Auto-redirect after successful login
- **State cleanup**: Proper cleanup of timers and connections

### ✅ User Experience
- **Connection status**: Visual indicators for WebSocket state
- **Error display**: User-friendly error messages
- **Loading states**: Connection progress indicators
- **Responsive design**: Works on desktop and mobile

## Security Considerations

### 1. Topic Security
- QR codes use unique identifiers
- Topics are temporary (1-minute expiry)
- No sensitive data in topic names

### 2. Message Validation
- Validate incoming message format
- Verify token authenticity
- Handle malformed messages gracefully

### 3. Connection Security
- Use WSS in production
- Implement proper authentication headers
- Monitor connection attempts

## Performance Optimization

### 1. Connection Management
- Single WebSocket connection per session
- Efficient subscription management
- Proper cleanup on unmount

### 2. Memory Management
- Auto-cleanup of timers and subscriptions
- Prevent memory leaks
- Efficient state updates

### 3. Network Efficiency
- Heartbeat configuration
- Reconnection with backoff
- Minimal message overhead

## Troubleshooting

### Common Issues

#### WebSocket Connection Failed
```typescript
// Check environment configuration
console.log('WS URL:', ENV.WS_URL);

// Verify server is running
// Check network connectivity
// Validate CORS settings
```

#### Subscription Not Working
```typescript
// Verify QR code format
console.log('Subscribing to:', `/topic/${qrCode}`);

// Check server topic configuration
// Validate message format
```

#### Login Data Not Received
```typescript
// Check message parsing
onLoginReceived: (loginData) => {
  console.log('Received:', loginData);
  // Validate data structure
}
```

### Debug Mode
Enable debug logging:
```typescript
// In websocket.service.ts
debug: (str) => {
  if (ENV.NODE_ENV === 'DEV') {
    console.log('STOMP Debug:', str);
  }
}
```

## Testing

### Manual Testing
1. Start WebSocket server
2. Open web application
3. Click "Mulai QR Code Login"
4. Verify WebSocket connection status
5. Simulate mobile app scan (send test message)
6. Verify login processing

### Integration Testing
```typescript
// Test WebSocket service
const service = new WebSocketService({
  onLoginReceived: (data) => {
    expect(data).toHaveProperty('access_token');
    expect(data).toHaveProperty('account_data');
  }
});

await service.connect();
service.subscribeToQRCode('test-qr-code');
```

## Future Enhancements

1. **Message Encryption**: Encrypt WebSocket messages
2. **Rate Limiting**: Implement connection rate limits
3. **Analytics**: Track WebSocket usage metrics
4. **Clustering**: Support for multiple WebSocket servers
5. **Push Notifications**: Browser notifications for login events
