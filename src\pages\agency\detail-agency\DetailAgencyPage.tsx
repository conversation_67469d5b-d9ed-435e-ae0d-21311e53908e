import type { ITableColumn } from '@/components/AppTable';
import AppTable from '@/components/AppTable';
import MapPreview from '@/components/MapPreview';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ROUTES } from '@/routes/routes';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import {
  Briefcase,
  Building2,
  Calendar,
  Edit,
  Eye,
  FileText,
  Mail,
  MapPin,
  Phone,
  Plus,
  Settings,
  Users,
} from 'lucide-react';
import { Link } from 'react-router-dom';
import DetailAgencySkeleton from './DetailAgencySkeleton';
import { useDetailAgency } from './useDetailAgency';

// Helper function to get initials from name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// Helper function to format date
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export default function DetailAgencyPage() {
  const page = useDetailAgency();
  const data = page?.dataDetail;
  const workUnits = page?.dataWorkUnit || [];
  const employees = page?.queryEmployee?.data || [];
  const totalEmloyess = page.totalEmployee;

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: data?.name || '-',
    },
  ];

  const tableColumn: ITableColumn<IResWorkUnit>[] = [
    {
      headerTitle: 'Nama Unit Kerja',
      component: (e) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Briefcase className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{e.name}</div>
            <div className="text-sm text-gray-500">{e.description || 'Tidak ada deskripsi'}</div>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Tipe',
      component: (e) => (
        <Badge variant={e.type === 'SHIFT' ? 'default' : 'secondary'} className="font-medium">
          {e.type === 'SHIFT' ? 'Shift' : 'Regular'}
        </Badge>
      ),
    },
    {
      headerTitle: 'Statistik',
      component: (e) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="w-3 h-3 text-gray-400" />
            <span className="text-gray-600">{e.total_day} hari kerja</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="w-3 h-3 text-gray-400" />
            <span className="text-gray-600">{e.count_location || '0'} lokasi</span>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Aksi',
      component: (e) => (
        <div className="flex gap-2">
          <Link to={ROUTES.DETAIL_WORK_UNIT(e.agency_id, e.id)}>
            <Button size="sm" variant="outline" className="h-8 px-3">
              <Eye className="w-3 h-3 mr-1" />
              Detail
            </Button>
          </Link>
          <Link to={ROUTES.SETTING_SHIFT_TABLE(e.id)}>
            <Button size="sm" variant="outline" className="h-8 px-3">
              <Settings className="w-3 h-3 mr-1" />
              Jadwal
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  if (page.queryDetail?.isPending) {
    return (
      <PageContainer>
        <PageTitle title="Detail Instansi" breadcrumb={breadcrumb} />
        <DetailAgencySkeleton />
      </PageContainer>
    );
  }

  return (
    <PageContainer className="max-w-7xl mx-auto">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <PageTitle title="Detail Instansi" breadcrumb={breadcrumb} />
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Link to={ROUTES.MASTER_DATA.EDIT_AGENCY(data?.id || '')}>
            <Button variant="outline" className="w-full sm:w-auto">
              <Edit className="w-4 h-4 mr-2" />
              Edit Instansi
            </Button>
          </Link>
          <Link to={ROUTES.CREATE_WORK_UNIT(data?.id || '')}>
            <Button className="w-full sm:w-auto">
              <Plus className="w-4 h-4 mr-2" />
              Tambah Unit Kerja
            </Button>
          </Link>
        </div>
      </div>

      {/* Agency Profile Card */}
      <Card className="mb-6">
        <CardContent className="">
          <div className=" gap-6 ">
            <div className="mb-5">
              <div className="text-center lg:text-left space-y-3 w-full">
                <div className="flex justify-between">
                  <h1 className="text-xl font-bold text-gray-900 mb-2">{data?.name || 'Nama Instansi'}</h1>
                  <Badge variant="outline" className="text-xs">
                    <Building2 className="w-3 h-3 mr-1" />
                    Instansi Pemerintah
                  </Badge>
                </div>

                {data?.description && (
                  <div className="bg-gray-50 rounded-lg p-3 ">
                    <div className="flex items-start gap-2">
                      <FileText className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
                      <p className="text-gray-700 text-sm leading-relaxed">{data?.description || '---'}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Statistics Grid */}
            <div className="flex-1">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Briefcase className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-xl font-bold text-gray-900">{workUnits.length}</div>
                      <div className="text-sm text-gray-600">Unit Kerja</div>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <Users className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <div className="text-xl font-bold text-gray-900">{totalEmloyess}</div>
                      <div className="text-sm text-gray-600">Pegawai</div>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <div className="text-sm font-bold text-gray-900">{formatDate(data?.created_date || '')}</div>
                      <div className="text-sm text-gray-600">Tanggal Dibuat</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Location Info */}
              {data?.lat && data?.lng && (
                <div className="bg-orange-50  flex justify-between rounded-lg p-4 border border-orange-100">
                  <div>
                    {' '}
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <MapPin className="w-4 h-4 text-orange-600" />
                      </div>
                      <h3 className="font-medium text-gray-900">Lokasi Instansi</h3>
                    </div>
                    <div className="text-sm text-gray-600">
                      Koordinat: {data.lat.toFixed(6)}, {data.lng.toFixed(6)}
                    </div>
                  </div>
                  <Button
                    variant="link"
                    size="sm"
                    className="w-full sm:w-auto text-orange-800"
                    onClick={() => {
                      const googleMapsUrl = `https://www.google.com/maps?q=${data.lat},${data.lng}`;
                      window.open(googleMapsUrl, '_blank');
                    }}
                  >
                    <MapPin className="w-4 h-4 mr-2" />
                    Buka di Google Maps
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Tabs */}
      <Card>
        <Tabs defaultValue="work_unit" className="w-full">
          <CardHeader className="pb-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="work_unit" className="flex items-center gap-2">
                <Briefcase className="w-4 h-4" />
                Unit Kerja
              </TabsTrigger>
              <TabsTrigger value="employee" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Pegawai
              </TabsTrigger>
              <TabsTrigger value="location" className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Lokasi
              </TabsTrigger>
            </TabsList>
          </CardHeader>

          <Separator />

          {/* Work Units Tab */}
          <TabsContent value="work_unit" className="mt-0">
            <CardContent className="pt-4">
              {workUnits.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Daftar Unit Kerja</h3>
                      <p className="text-sm text-gray-600">Kelola unit kerja dalam instansi ini</p>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <AppTable data={workUnits} column={tableColumn} />
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Briefcase className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada unit kerja</h3>
                  <p className="text-gray-600 mb-6">Mulai dengan menambahkan unit kerja pertama untuk instansi ini</p>
                  <Link to={ROUTES.CREATE_WORK_UNIT(data?.id || '')}>
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Tambah Unit Kerja
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </TabsContent>

          {/* Employees Tab */}
          <TabsContent value="employee" className="mt-0">
            <CardContent className="pt-4">
              {employees.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Daftar Pegawai</h3>
                      <p className="text-sm text-gray-600">Pegawai yang terdaftar dalam instansi ini</p>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 px-4 font-medium text-gray-900">Pegawai</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-900">Unit Kerja</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-900">Kontak</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                          <th className="text-center py-3 px-4 font-medium text-gray-900">Aksi</th>
                        </tr>
                      </thead>
                      <tbody>
                        {employees.map((employee) => (
                          <tr key={employee.account_id} className="border-b border-gray-100 hover:bg-gray-50">
                            <td className="py-3 px-4">
                              <div className="flex items-center gap-3">
                                <Avatar className="w-10 h-10">
                                  <AvatarImage src={employee.profile_picture} alt={employee.name} />
                                  <AvatarFallback className="bg-blue-100 text-blue-700 text-sm">
                                    {getInitials(employee.name)}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium text-gray-900">{employee.name}</div>
                                  <div className="text-sm text-gray-500">NIP: {employee.nip || '-'}</div>
                                </div>
                              </div>
                            </td>
                            <td className="py-3 px-4">
                              <div className="text-sm text-gray-900">
                                {employee.work_unit_name || 'Belum ada unit kerja'}
                              </div>
                            </td>
                            <td className="py-3 px-4">
                              <div className="space-y-1">
                                <div className="flex items-center gap-1 text-sm text-gray-600">
                                  <Phone className="w-3 h-3 text-gray-400" />
                                  <span>{employee.phone}</span>
                                </div>
                                <div className="flex items-center gap-1 text-sm text-gray-600">
                                  <Mail className="w-3 h-3 text-gray-400" />
                                  <span className="truncate max-w-[200px]">{employee.email}</span>
                                </div>
                              </div>
                            </td>
                            <td className="py-3 px-4">
                              <Badge
                                variant={employee.status === 'ACTIVE' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {employee.status === 'ACTIVE' ? 'Aktif' : 'Tidak Aktif'}
                              </Badge>
                            </td>
                            <td className="py-3 px-4 text-center">
                              <Link to={ROUTES.DETAIL_EMPLOYEE(employee.account_id)}>
                                <Button size="sm" variant="outline" className="h-8 px-3">
                                  <Eye className="w-3 h-3 mr-1" />
                                  Detail
                                </Button>
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada pegawai</h3>
                  <p className="text-gray-600">Pegawai akan muncul di sini setelah didaftarkan ke instansi ini</p>
                </div>
              )}
            </CardContent>
          </TabsContent>

          {/* Location Tab */}
          <TabsContent value="location" className="mt-0">
            <CardContent className="pt-4 h-fit">
              <div className="space-y-4 h-fit">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Lokasi Instansi</h3>
                  <p className="text-sm text-gray-600">Peta lokasi geografis instansi</p>
                </div>
                {data?.lat && data?.lng ? (
                  <div className="bg-gray-50 h-fit rounded-lg p-4">
                    <div className="w-full">
                      <MapPreview lat={data.lat} lng={data.lng} />
                    </div>
                    <div className="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex flex-col sm:flex-row gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span>Latitude: {data.lat.toFixed(6)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span>Longitude: {data.lng.toFixed(6)}</span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full sm:w-auto"
                        onClick={() => {
                          const googleMapsUrl = `https://www.google.com/maps?q=${data.lat},${data.lng}`;
                          window.open(googleMapsUrl, '_blank');
                        }}
                      >
                        <MapPin className="w-4 h-4 mr-2" />
                        Buka di Google Maps
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Lokasi belum diatur</h3>
                    <p className="text-gray-600">Koordinat lokasi instansi belum tersedia</p>
                  </div>
                )}
              </div>
            </CardContent>
          </TabsContent>
        </Tabs>
      </Card>
    </PageContainer>
  );
}
