import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import DateHelper from '@/lib/date-helper';
import { useAgencyRepository } from '@/pages/agency/page/useAgencyRepository';
import { EmployeeRepository } from '@/repositories/employee-repostiory';
import { MasterDataRepository } from '@/repositories/master-data-repository';
import { ROUTES } from '@/routes/routes';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqCreateEmployee } from '@/types/request/IReqCreateEmployee';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import * as yup from 'yup';

export function useEmployeeFormPage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const agencyRepository = useAgencyRepository();
  const employeeRepository = new EmployeeRepository();
  const masterDataRepository = new MasterDataRepository();

  const navigate = useNavigate();
  const { toast } = useUi();
  const [listAgency, setListAgency] = useState<ILabelValue<string>[]>([]);

  const { id } = useParams();

  const queryAllRole = useQuery({
    queryKey: ['all_role'],
    queryFn: async () => await masterDataRepository.listAllRole(),
  });

  useEffect(() => {
    if (agencyRepository.listAgency.data) {
      setListAgency(
        agencyRepository.listAgency.data.map((e) => {
          return {
            label: e.name,
            value: e.id,
          };
        }),
      );
    }
  }, [agencyRepository.listAgency.data]);

  const queryDetail = useQuery({
    queryKey: ['detailEmployee', id],
    enabled: !!id,
    queryFn: () => employeeRepository.getDetailEmployee(id),
  });

  const initState: IReqCreateEmployee = {
    city_id: '',
    district_id: '',
    province_id: '',
    sub_district_id: '',
    work_unit_id: '',
    name: '',
    phone: '',
    email: '',
    nip: '',
    role: 'USER',
    agency_id: '',
    date_of_birth: '',
    gender: '',
    religion: '',
    marital_status: '',
    address: '',
    front_degree: '',
    back_degree: '',
  };

  const validationSchema = yup.object().shape({
    name: yup.string().required(),
    phone: yup
      .string()
      .required('Nomor telepon wajib diisi')
      .matches(/^8[0-9]{7,14}$/, 'Nomor harus dimulai dari angka 8 dan terdiri dari 8-12 digit'),
  });

  const mutationCreate = useMutation({
    mutationKey: ['create_employee'],
    mutationFn: async (e: IReqCreateEmployee) => {
      const data = {
        ...e,
        checked: undefined,
        phone: '62' + e.phone,
      };
      await httpService
        .POST(ENDPOINT.CREATE_EMPLOYEE(), data)
        .then(() => {
          toast.success('Data pegawai baru berhasil ditambahkan');
          navigate(ROUTES.EMPLOYEE_LIST());
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        });
    },
  });

  const mutationEdit = useMutation({
    mutationKey: ['edit_employee'],
    mutationFn: async (e: IReqCreateEmployee) => {
      const data = {
        phone: e.phone,
        name: e.name,
        email: e.email,
        nip: e.nip,
        work_unit_id: e.work_unit_id,
        agency_id: e.agency_id,
        front_degree: e.front_degree,
        back_degree: e.back_degree,
        date_of_birth: DateHelper.toFormatDate(new Date(e.date_of_birth), 'yyyy-MM-dd'),
        religion: e.religion,
        marital_status: e.marital_status,
        gender: e.gender,
        district_id: e.district_id,
        city_id: e.city_id,
        province_id: e.province_id,
        sub_district_id: e.sub_district_id,
        address: e.address,
        role: e.role,
      };
      await httpService
        .PUT(ENDPOINT.EDIT_EMPLOYEE(id || ''), data)
        .then(() => {
          toast.success('Data pegawai baru berhasil diperbaharui');
          navigate(ROUTES.DETAIL_EMPLOYEE(id || ''));
        })
        .catch((e) => {
          throw new Error(e);
        });
    },
  });

  const formik = useFormik({
    initialValues: initState,
    validationSchema: validationSchema,
    onSubmit: (e) => (id ? mutationEdit.mutate(e) : mutationCreate.mutate(e)),
  });

  function checkPhoneSet(e?: string): string | undefined {
    if (e) {
      return e.startsWith('62') ? e.replace(/^62/, '') : e;
    } else {
      return;
    }
  }

  useEffect(() => {
    if (id && queryDetail?.data) {
      const detail: any = queryDetail.data;
      formik.setValues({
        ...initState,
        ...detail,
        nip: detail?.nip,
        phone: checkPhoneSet(detail?.phone),
        date_of_birth: detail?.date_of_birth || '',
        gender: detail?.gender || '',
        religion: detail?.religion || '',
        marital_status: detail?.marital_status || '',
        address: detail?.address || '',
        tax_id_number: detail?.tax_id_number || '',
        front_degree: detail?.front_degree || '',
        back_degree: detail?.back_degree || '',
        profile_picture: detail?.profile_picture || '',
        role: detail?.role || '',
      });
    }
  }, [queryDetail?.data, id]);

  const queryWorkUnit = useQuery({
    queryKey: ['work_unit_by_agency', formik?.values?.agency_id || ''],
    enabled: !!formik.values.agency_id,
    queryFn: async () => {
      try {
        const res = await httpService.GET(ENDPOINT.LIST_WORK_UNIT_AGENCY(formik?.values?.agency_id || ''));
        const data = res.data.response_data || [];
        return data.map((e: IResWorkUnit) => ({
          label: e.name,
          value: e.id,
        }));
      } catch (e) {
        errorService.fetchApiError(e);
        return []; // pastikan selalu return array (bukan undefined)
      }
    },
  });

  const dataRole: ILabelValue<string>[] = (queryAllRole.data || []).map((e) => {
    return {
      label: e.name,
      value: e.role_enum,
    };
  });

  return { formik, listAgency, id, mutationCreate, queryWorkUnit, queryDetail, dataRole, mutationEdit };
}
