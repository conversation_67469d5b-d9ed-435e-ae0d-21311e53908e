import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import { useAgencyRepository } from '@/pages/agency/page/useAgencyRepository';
import { EmployeeRepository } from '@/repositories/employee-repostiory';
import { ROUTES } from '@/routes/routes';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqCreateEmployee } from '@/types/request/IReqCreateEmployee';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { BaseResponse } from '@/types/response/IResModel';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import * as yup from 'yup';

export function useEmployeeFormPage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const agencyRepository = useAgencyRepository();
  const employeeRepository = new EmployeeRepository();
  const navigate = useNavigate();
  const { toast } = useUi();
  const [listAgency, setListAgency] = useState<ILabelValue<string>[]>([]);

  const { id } = useParams();
  useEffect(() => {
    if (agencyRepository.listAgency.data) {
      setListAgency(
        agencyRepository.listAgency.data.map((e) => {
          return {
            label: e.name,
            value: e.id,
          };
        }),
      );
    }
  }, [agencyRepository.listAgency.data]);

  const queryDetail = useQuery({
    queryKey: ['detailEmployee', id],
    enabled: !!id,
    queryFn: () => employeeRepository.getDetailEmployee(id),
  });

  const initState: IReqCreateEmployee = {
    name: '',
    phone: '',
    email: '',
    nip: '',
    role: 'USER',
    agency_id: '',
  };

  const validationSchema = yup.object().shape({
    name: yup.string().required(),
    phone: yup
      .string()
      .required('Nomor telepon wajib diisi')
      .matches(/^8[0-9]{7,14}$/, 'Nomor harus dimulai dari angka 8 dan terdiri dari 8-12 digit'),
  });

  const mutationCreate = useMutation({
    mutationKey: ['create_employee'],
    mutationFn: (e: IReqCreateEmployee) => {
      const data = {
        ...e,
        checked: undefined,
        phone: '62' + e.phone,
      };
      return httpService
        .POST(ENDPOINT.CREATE_EMPLOYEE(), data)
        .then(() => {
          toast.success('Data pegawai baru berhasil ditambahkan');
          navigate(ROUTES.EMPLOYEE_LIST());
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        });
    },
  });

  const formik = useFormik({
    initialValues: initState,
    validationSchema: validationSchema,
    onSubmit: (e) => mutationCreate.mutate(e),
  });

  useEffect(() => {
    if (id && queryDetail?.data) {
      formik.setValues({
        ...queryDetail.data,
        phone: queryDetail.data.phone.replace('62', ''),
      });
    }
  }, [queryDetail?.data, id]);

  const queryWorkUnit = useQuery({
    initialData: [],
    queryKey: ['work_unit_agency', formik?.values?.agency_id],
    enabled: !!formik.values.agency_id,
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.LIST_WORK_UNIT_AGENCY(formik?.values?.agency_id || ''))
        .then((res: BaseResponse<IResWorkUnit[]>) => {
          const data = res.data.response_data || [];
          const r: ILabelValue<string>[] = data.map((e) => {
            return {
              label: e.name,
              value: e.id,
            };
          });
          return r;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
  });

  return { formik, listAgency, id, mutationCreate, queryWorkUnit, queryDetail };
}
