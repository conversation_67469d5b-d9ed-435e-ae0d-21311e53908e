# QR Code Timer Implementation

## Overview
Implementasi sistem QR Code yang berubah otomatis setiap 1 menit dengan countdown timer real-time untuk meningkatkan keamanan login.

## Features
- ✅ QR Code berubah otomatis setiap 1 menit (60 detik)
- ✅ Countdown timer real-time yang update setiap detik
- ✅ Progress bar visual untuk menunjukkan waktu tersisa
- ✅ Manual refresh QR Code
- ✅ Auto cleanup timer saat component unmount
- ✅ Format waktu yang user-friendly (mm:ss)
- ✅ Status indicator dan visual feedback
- ✅ Responsive design untuk mobile dan desktop

## Technical Implementation

### 1. Hook: useSignInPage.ts

```typescript
// State management
const [count, setCount] = useState<number>(60); // Countdown dalam detik
const [isQrActive, setIsQrActive] = useState<boolean>(false);
const intervalRef = useRef<NodeJS.Timeout | null>(null);
const countdownRef = useRef<NodeJS.Timeout | null>(null);

// Generate QR code baru
const generateNewQrCode = useCallback(() => {
  const code = generateUniqueCode();
  setQrValue(code);
  setCount(60); // Reset countdown ke 60 detik
  console.log('QR Code baru dibuat:', code);
}, []);

// Countdown timer (update setiap 1 detik)
const startCountdown = useCallback(() => {
  if (countdownRef.current) {
    clearInterval(countdownRef.current);
  }

  countdownRef.current = setInterval(() => {
    setCount((prevCount) => {
      if (prevCount <= 1) {
        generateNewQrCode(); // Generate QR baru saat countdown habis
        return 60;
      }
      return prevCount - 1;
    });
  }, 1000);
}, [generateNewQrCode]);

// QR timer (generate QR baru setiap 1 menit)
const startQrTimer = useCallback(() => {
  if (intervalRef.current) {
    clearInterval(intervalRef.current);
  }

  intervalRef.current = setInterval(() => {
    generateNewQrCode();
  }, 60000); // 60000ms = 1 menit
}, [generateNewQrCode]);
```

### 2. Component: QRCodeWithTimer.tsx

```typescript
interface QRCodeWithTimerProps {
  qrValue?: string;
  count: number;
  isQrActive: boolean;
  formatCountdown: (seconds: number) => string;
  onRefresh: () => void;
  onStop: () => void;
}

// Progress bar berdasarkan countdown
<div
  className={`h-2 rounded-full transition-all duration-1000 ${
    count <= 10 ? 'bg-red-500' : count <= 30 ? 'bg-yellow-500' : 'bg-green-500'
  }`}
  style={{ width: `${(count / 60) * 100}%` }}
></div>
```

## Usage Examples

### Basic Usage
```typescript
import useSignInPage from '@/pages/auth/useSignInPage';
import QRCodeWithTimer from '@/components/QRCodeWithTimer';

function LoginPage() {
  const {
    qrValue,
    count,
    isQrActive,
    formatCountdown,
    onClickQr,
    stopQrTimer,
    generateNewQrCode,
  } = useSignInPage();

  return (
    <div>
      {!isQrActive ? (
        <button onClick={onClickQr}>
          Mulai QR Code Login
        </button>
      ) : (
        <QRCodeWithTimer
          qrValue={qrValue}
          count={count}
          isQrActive={isQrActive}
          formatCountdown={formatCountdown}
          onRefresh={generateNewQrCode}
          onStop={stopQrTimer}
        />
      )}
    </div>
  );
}
```

### Advanced Usage dengan Custom Styling
```typescript
<QRCodeWithTimer
  qrValue={qrValue}
  count={count}
  isQrActive={isQrActive}
  formatCountdown={formatCountdown}
  onRefresh={() => {
    console.log('Manual refresh triggered');
    generateNewQrCode();
  }}
  onStop={() => {
    console.log('QR timer stopped');
    stopQrTimer();
  }}
/>
```

## API Reference

### useSignInPage Hook Returns

| Property | Type | Description |
|----------|------|-------------|
| `qrValue` | `string \| undefined` | QR code value yang di-generate |
| `count` | `number` | Countdown timer dalam detik (0-60) |
| `isQrActive` | `boolean` | Status apakah QR code sedang aktif |
| `formatCountdown` | `(seconds: number) => string` | Format countdown ke mm:ss |
| `onClickQr` | `() => void` | Function untuk memulai QR code |
| `stopQrTimer` | `() => void` | Function untuk stop semua timer |
| `generateNewQrCode` | `() => void` | Function untuk generate QR baru |

### QRCodeWithTimer Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `qrValue` | `string \| undefined` | No | QR code value untuk ditampilkan |
| `count` | `number` | Yes | Countdown timer dalam detik |
| `isQrActive` | `boolean` | Yes | Status QR code aktif/tidak |
| `formatCountdown` | `(seconds: number) => string` | Yes | Function format countdown |
| `onRefresh` | `() => void` | Yes | Callback saat refresh button diklik |
| `onStop` | `() => void` | Yes | Callback saat stop button diklik |

## Timer Configuration

### Interval Settings
```typescript
// QR Code refresh interval (1 menit)
const QR_REFRESH_INTERVAL = 60000; // 60000ms = 1 menit

// Countdown update interval (1 detik)
const COUNTDOWN_INTERVAL = 1000; // 1000ms = 1 detik

// Initial countdown value (60 detik)
const INITIAL_COUNTDOWN = 60;
```

### Customization
Untuk mengubah interval timer, edit nilai di `useSignInPage.ts`:

```typescript
// Ubah interval QR refresh (contoh: 2 menit)
intervalRef.current = setInterval(() => {
  generateNewQrCode();
}, 120000); // 120000ms = 2 menit

// Ubah initial countdown
setCount(120); // 120 detik = 2 menit
```

## Security Considerations

1. **Unique Code Generation**: Setiap QR code menggunakan `generateUniqueCode()` untuk memastikan uniqueness
2. **Time-based Expiry**: QR code otomatis expire setiap 1 menit
3. **Visual Feedback**: User dapat melihat countdown untuk mengetahui kapan QR akan expire
4. **Manual Refresh**: User dapat manual refresh jika diperlukan

## Performance Optimization

1. **useCallback**: Semua functions menggunakan useCallback untuk mencegah re-render
2. **useRef**: Timer references menggunakan useRef untuk performa optimal
3. **Cleanup**: Auto cleanup timer saat component unmount
4. **Conditional Rendering**: QR code hanya render saat diperlukan

## Browser Compatibility

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Troubleshooting

### Timer tidak berjalan
- Pastikan `onClickQr()` dipanggil untuk memulai timer
- Check console untuk error messages
- Pastikan component tidak unmount sebelum timer selesai

### QR Code tidak berubah
- Verify `generateUniqueCode()` function bekerja dengan benar
- Check network connectivity jika QR code dari server
- Pastikan `setQrValue()` dipanggil dengan value baru

### Memory Leaks
- Timer otomatis cleanup saat component unmount
- Manual call `stopQrTimer()` jika perlu stop timer sebelum unmount
- Check browser dev tools untuk memory usage

## Future Enhancements

1. **WebSocket Integration**: Real-time QR code sync dengan server
2. **Push Notifications**: Notifikasi saat QR code akan expire
3. **Analytics**: Track QR code usage dan success rate
4. **Customizable Intervals**: UI untuk mengatur interval timer
5. **QR Code Encryption**: Encrypt QR code content untuk security tambahan
