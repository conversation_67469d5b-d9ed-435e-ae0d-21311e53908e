import { Card, CardContent } from './ui/card';
import { LoadingSpinner } from './ui/CircularLoading';

export default function CardLoading() {
  return (
    <Card className="shadow-none">
      <CardContent>
        <div className="flex items-center flex-col min-h-[200px] justify-center">
          <LoadingSpinner className="h-16 w-16 opacity-40" />
          <p className="mt-4 text-gray-400">Loading...</p>
        </div>
      </CardContent>
    </Card>
  );
}
