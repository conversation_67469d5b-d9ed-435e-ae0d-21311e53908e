# QR Code Timer Implementation

## Overview
Implementasi QR Code yang berubah otomatis setiap 1 menit dengan countdown timer dan keterangan "Scan QR di Mobile App untuk Login".

## Fitur Utama

### ✅ QR Code Auto-Refresh
- QR Code berubah otomatis setiap 60 detik (1 menit)
- Generate unique code baru setiap refresh
- Timer berjalan di background

### ✅ Countdown Timer
- Countdown real-time yang update setiap 1 detik
- Format waktu mm:ss (contoh: 01:30)
- Visual progress bar dengan color coding:
  - 🟢 Hijau: > 30 detik tersisa
  - 🟡 Kuning: 10-30 detik tersisa
  - 🔴 Merah: < 10 detik tersisa (dengan animasi pulse)

### ✅ User Interface
- **QR Code Display**: QR code dengan countdown badge di pojok
- **Instruksi Jelas**: "Scan QR di Mobile App" dengan langkah-langkah
- **Progress Bar**: Visual representation waktu tersisa
- **Action Buttons**: Manual refresh dan stop timer
- **Status Info**: Informasi countdown dan progress

## File yang Dimodifikasi/Dibuat

### 1. `useSignInPage.ts` - Hook Utama
```typescript
// State management
const [count, setCount] = useState<number>(60);
const [isQrActive, setIsQrActive] = useState<boolean>(false);

// Timer functions
const generateNewQrCode = useCallback(() => { ... });
const startCountdown = useCallback(() => { ... });
const startQrTimer = useCallback(() => { ... });
const stopQrTimer = useCallback(() => { ... });
const formatCountdown = useCallback((seconds: number) => string);
```

### 2. `SignInPage.tsx` - UI Implementation
- Enhanced QR code display dengan countdown
- Instruksi "Scan QR di Mobile App untuk Login"
- Progress bar dan visual feedback
- Action buttons untuk control

### 3. `QRCodeWithTimer.tsx` - Reusable Component
- Komponen terpisah untuk QR code dengan timer
- Props interface untuk customization
- Responsive design

### 4. `QRCodeExample.tsx` - Demo Component
- Contoh penggunaan lengkap
- Technical status display
- Feature showcase

## Cara Penggunaan

### Basic Usage
```typescript
import useSignInPage from '@/pages/auth/useSignInPage';

function LoginPage() {
  const {
    qrValue,
    count,
    isQrActive,
    formatCountdown,
    onClickQr,
    stopQrTimer,
    generateNewQrCode,
  } = useSignInPage();

  return (
    <div>
      {!isQrActive ? (
        <button onClick={onClickQr}>
          Mulai QR Code Login
        </button>
      ) : (
        <QRCodeWithTimer
          qrValue={qrValue}
          count={count}
          formatCountdown={formatCountdown}
          onRefresh={generateNewQrCode}
          onStop={stopQrTimer}
        />
      )}
    </div>
  );
}
```

## Timer Configuration

### Interval Settings
```typescript
// QR refresh interval (1 menit)
const QR_REFRESH_INTERVAL = 60000; // 60000ms = 1 menit

// Countdown update interval (1 detik)  
const COUNTDOWN_INTERVAL = 1000; // 1000ms = 1 detik

// Initial countdown value (60 detik)
const INITIAL_COUNTDOWN = 60;
```

## Visual Features

### QR Code Container
- Gradient background (blue to indigo)
- White QR code background dengan shadow
- Countdown badge di pojok kanan atas
- Responsive sizing

### Instructions Section
- Icon smartphone dengan judul "Scan QR di Mobile App"
- Deskripsi jelas tentang cara penggunaan
- 3-step visual guide (Buka App → Scan QR → Login)

### Countdown Display
- Large countdown timer dengan format mm:ss
- Progress bar dengan smooth animation
- Color coding berdasarkan waktu tersisa
- Pulse animation saat < 10 detik

### Action Controls
- "Generate QR Baru" button untuk manual refresh
- "Stop Timer" button untuk menghentikan
- Responsive button layout

## Security & Performance

### Security
- Unique QR code generation setiap refresh
- Time-based expiry (1 menit)
- Visual countdown untuk user awareness

### Performance
- useCallback untuk optimize re-renders
- useRef untuk timer references
- Auto cleanup saat component unmount
- Efficient state management

## Browser Support
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers

## Customization

### Mengubah Interval Timer
```typescript
// Di useSignInPage.ts, ubah nilai:
intervalRef.current = setInterval(() => {
  generateNewQrCode();
}, 120000); // 2 menit

setCount(120); // 2 menit countdown
```

### Mengubah Styling
```typescript
// Di QRCodeWithTimer.tsx, sesuaikan className:
className={`px-3 py-1 rounded-full text-xs font-bold shadow-lg ${
  count <= 10 ? 'bg-red-500 text-white animate-pulse' : 
  count <= 30 ? 'bg-yellow-500 text-white' : 
  'bg-green-500 text-white'
}`}
```

## Testing

### Manual Testing
1. Klik "Mulai QR Code Login"
2. Verify QR code muncul dengan countdown
3. Wait dan verify QR berubah setiap 1 menit
4. Test manual refresh button
5. Test stop timer button

### Console Logging
```typescript
// Di generateNewQrCode function:
console.log('QR Code baru dibuat:', code);
```

## Troubleshooting

### Timer tidak berjalan
- Check console untuk error messages
- Verify `onClickQr()` dipanggil
- Pastikan component tidak unmount

### QR Code tidak berubah
- Check `generateUniqueCode()` function
- Verify `setQrValue()` dipanggil
- Check network connectivity

### Memory leaks
- Timer auto cleanup saat unmount
- Manual call `stopQrTimer()` jika perlu
- Monitor browser dev tools

## Future Enhancements
1. WebSocket integration untuk real-time sync
2. Push notifications saat QR akan expire
3. Analytics untuk track usage
4. Customizable timer intervals via UI
5. QR code encryption untuk security tambahan
