import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailAgency } from '@/types/response/IResDetailAgency';
import type { IResListAgency } from '@/types/response/IResListAgency';
import type { BaseResponse } from '@/types/response/IResModel';

export class AgencyRepository {
  httpService = new HttpService();
  errorService = new ErrorService();

  async getDetailAgency(agencyId: string): Promise<IResDetailAgency> {
    try {
      const res: BaseResponse<IResDetailAgency> = await this.httpService.GET(ENDPOINT.DETAIL_AGENCY(agencyId));
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async listAgency() {
    return this.httpService
      .GET(ENDPOINT.LIST_AGENCY())
      .then((res: BaseResponse<IResListAgency[]>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }
}
