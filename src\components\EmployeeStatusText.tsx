import type { EmployeeStatusType } from '@/types/type/EmployeeStatusType';

interface IProps {
  status: EmployeeStatusType;
  string?: string;
}

export default function EmployeeStatusText({ status, string }: IProps) {
  const style = getStatusStyle(status);

  return (
    <div className={` w-fit px-2 py-1 text-wrap rounded-full text-sm font-medium ${style} w-fit `}>{string || '-'}</div>
  );
}

function getStatusStyle(status: EmployeeStatusType): string {
  switch (status) {
    case 'WAITING_PHONE_VERIFICATION':
    case 'WAITING_FACE_REGISTRATION':
      return ' text-yellow-800';
    case 'PENDING':
      return ' text-blue-800';
    case 'ACTIVE':
      return ' text-green-800';
    case 'INACTIVE':
      return ' text-red-800';
    default:
      return ' text-gray-800';
  }
}
