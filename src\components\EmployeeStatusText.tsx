import type { EmployeeStatusType } from '@/types/type/EmployeeStatusType';

interface IProps {
  status: EmployeeStatusType;
}

export default function EmployeeStatusText({ status }: IProps) {
  const label = getStatusLabel(status);
  const style = getStatusStyle(status);

  return <div className={` w-fit px-2 py-1 text-wrap rounded-full text-sm font-medium ${style} w-fit `}>{label}</div>;
}

function getStatusLabel(status: EmployeeStatusType): string {
  switch (status) {
    case 'WAITING_PHONE_VERIFICATION':
      return 'Menunggu Verifikasi Telepon';
    case 'WAITING_FACE_REGISTRATION':
      return 'Menunggu Registrasi Wajah';
    case 'PENDING':
      return 'Menunggu Persetujuan';
    case 'ACTIVE':
      return 'Aktif';
    case 'INACTIVE':
      return 'Tidak Aktif';
    default:
      return status;
  }
}

function getStatusStyle(status: EmployeeStatusType): string {
  switch (status) {
    case 'WAITING_PHONE_VERIFICATION':
    case 'WAITING_FACE_REGISTRATION':
      return ' text-yellow-800';
    case 'PENDING':
      return ' text-blue-800';
    case 'ACTIVE':
      return ' text-green-800';
    case 'INACTIVE':
      return ' text-red-800';
    default:
      return ' text-gray-800';
  }
}
