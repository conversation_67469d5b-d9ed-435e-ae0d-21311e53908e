import { WorkUnitRepository } from '@/repositories/work-unit-repositories';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildSearchParams } from '@/utils/search-params.utils';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export function useWorkUnitPage() {
  const workUnitRepositori = new WorkUnitRepository();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterData, setFilterData] = useState<IFilterList>(getInitialFilter());
  const [searchValue, setSearchValue] = useState(filterData.q);

  useEffect(() => {
    const params = buildSearchParams(filterData);
    setSearchParams(params);
  }, [filterData, setSearchParams]);

  function getInitialFilter(): IFilterList {
    return {
      page: parseInt(searchParams.get('page') || '0'),
      size: parseInt(searchParams.get('size') || '10'),
    };
  }

  const queryWorkUnit = useQuery({
    queryKey: ['list_work_unit', filterData],
    queryFn: () => workUnitRepositori.listWorkUnit(filterData),
  });

  function handlePaginationChange(params: { page?: number; size?: number }) {
    setFilterData((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }

  function handleSearch() {
    const searchText = searchValue;
    if (searchText) {
      setFilterData((prev) => ({
        ...prev,
        q: searchText,
        page: 0, // Reset to first page on new search
      }));
    }
  }

  function handlePageChange(newPage: number) {
    setFilterData((prev) => ({
      ...prev,
      page: newPage,
    }));
  }

  function handleResetSearch() {
    setSearchValue('');
    setFilterData((prev) => ({
      ...prev,
      q: '',
      page: 0, // Reset to first page on new search
    }));
  }

  function isActiveSearch() {
    return !!filterData?.q?.length;
  }

  return {
    queryWorkUnit,
    handleSearch,
    handlePageChange,
    isActiveSearch,
    handlePaginationChange,
    handleResetSearch,
    searchValue,
    setSearchValue,
  };
}
