/* Detail Agency Page Styles */

.agencyProfile {
  background: linear-gradient(135deg, #eff6ff 0%, #f8fafc 50%, #faf5ff 100%);
  position: relative;
  overflow: hidden;
}

.agencyProfile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59, 130, 246, 0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.avatarContainer {
  position: relative;
  z-index: 10;
}

.statisticsCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.statisticsCard:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
}

.tabContent {
  min-height: 400px;
}

.workUnitCard {
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.workUnitCard:hover {
  border-color: rgba(59, 130, 246, 0.3);
  background-color: rgba(59, 130, 246, 0.02);
}

.employeeCard {
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.employeeCard:hover {
  border-color: rgba(16, 185, 129, 0.3);
  background-color: rgba(16, 185, 129, 0.02);
}

.emptyState {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
}

.actionButton {
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.actionButton:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
}

.headerActions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

@media (max-width: 640px) {
  .headerActions {
    flex-direction: column;
    width: 100%;
  }
  
  .headerActions button {
    width: 100%;
  }
}

/* Professional color scheme */
.blueAccent {
  color: #2563eb;
  background-color: #eff6ff;
  border-color: #bfdbfe;
}

.greenAccent {
  color: #059669;
  background-color: #ecfdf5;
  border-color: #a7f3d0;
}

.purpleAccent {
  color: #7c3aed;
  background-color: #f3e8ff;
  border-color: #c4b5fd;
}

.orangeAccent {
  color: #ea580c;
  background-color: #fff7ed;
  border-color: #fed7aa;
}

/* Typography enhancements */
.agencyTitle {
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.sectionTitle {
  font-weight: 600;
  letter-spacing: -0.025em;
  color: #111827;
}

.sectionDescription {
  color: #6b7280;
  line-height: 1.5;
}

/* Card enhancements */
.profileCard {
  border: 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.contentCard {
  border: 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Badge styling */
.agencyBadge {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: #1d4ed8;
  font-weight: 500;
}

.typeBadge {
  font-weight: 500;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Table enhancements */
.tableRow {
  transition: all 0.2s ease;
}

.tableRow:hover {
  background-color: rgba(59, 130, 246, 0.02);
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .agencyProfile {
    padding: 1.5rem;
  }
  
  .statisticsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .agencyProfile {
    padding: 1rem;
  }
  
  .statisticsGrid {
    grid-template-columns: 1fr;
  }
  
  .tabsList {
    grid-template-columns: 1fr;
  }
  
  .employeeGrid {
    grid-template-columns: 1fr;
  }
}

/* Accessibility improvements */
.focusVisible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .headerActions,
  .actionButton {
    display: none;
  }
  
  .profileCard,
  .contentCard {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .agencyProfile {
    background: white;
  }
}

/* Animation classes */
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideIn {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Professional spacing */
.sectionSpacing {
  margin-bottom: 2rem;
}

.cardSpacing {
  margin-bottom: 1.5rem;
}

.elementSpacing {
  margin-bottom: 1rem;
}

/* Text overflow handling */
.textTruncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.textWrap {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Icon styling */
.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.iconSmall {
  width: 1rem;
  height: 1rem;
}

.iconMedium {
  width: 1.25rem;
  height: 1.25rem;
}

.iconLarge {
  width: 1.5rem;
  height: 1.5rem;
}
