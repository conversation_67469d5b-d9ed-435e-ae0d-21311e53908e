import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FocusEvent<PERSON>andler, ReactNode } from 'react';
import { useState, useRef } from 'react';
import { cn } from '@/lib/utils.ts';
import Label from '@/components/ui/Label.tsx';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Textarea } from '@/components/ui/textarea.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Bold, Italic, Strikethrough, Code, Type, RotateCcw } from 'lucide-react';

interface IProps {
  id: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLTextAreaElement>;
  onChange?: ChangeEventHandler<HTMLTextAreaElement>;
  autoComplete?: string;
  dataTestId?: string;
  alignment?: 'horizontal' | 'vertical';
  disableFormik?: boolean;
  showPreview?: boolean; // Optional preview toggle
}

type FormatType = 'bold' | 'italic' | 'strikethrough' | 'monospace';

interface FormatButton {
  type: FormatType;
  icon: React.ComponentType<any>;
  label: string;
  symbol: string;
}

export default function WhatsAppMessageEditor(props: IProps) {
  const formik = !props.disableFormik ? useFormikContext<any>() : undefined;
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showPreview, setShowPreview] = useState(props.showPreview ?? false);

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  const formatButtons: FormatButton[] = [
    { type: 'bold', icon: Bold, label: 'Bold', symbol: '*' },
    { type: 'italic', icon: Italic, label: 'Italic', symbol: '_' },
    { type: 'strikethrough', icon: Strikethrough, label: 'Strikethrough', symbol: '~' },
    { type: 'monospace', icon: Code, label: 'Monospace', symbol: '```' },
  ];

  const applyFormat = (formatType: FormatType) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = currentValue.substring(start, end);

    const format = formatButtons.find((f) => f.type === formatType);
    if (!format) return;

    let newText: string;
    let newCursorPos: number;

    if (selectedText) {
      // Text is selected, wrap it with format symbols
      const formattedText = `${format.symbol}${selectedText}${format.symbol}`;
      newText = currentValue.substring(0, start) + formattedText + currentValue.substring(end);
      newCursorPos = start + formattedText.length;
    } else {
      // No text selected, insert format symbols and place cursor between them
      const formatText = `${format.symbol}${format.symbol}`;
      newText = currentValue.substring(0, start) + formatText + currentValue.substring(start);
      newCursorPos = start + format.symbol.length;
    }

    // Update the value
    const event = {
      target: {
        name: props.name,
        value: newText,
      },
    } as React.ChangeEvent<HTMLTextAreaElement>;

    if (props.onChange) {
      props.onChange(event);
    } else if (formik?.handleChange) {
      formik.handleChange(event);
    }

    // Set cursor position after state update
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  const clearFormatting = () => {
    const cleanText = currentValue
      .replace(/\*([^*]+)\*/g, '$1') // Remove bold
      .replace(/_([^_]+)_/g, '$1') // Remove italic
      .replace(/~([^~]+)~/g, '$1') // Remove strikethrough
      .replace(/```([^`]+)```/g, '$1'); // Remove monospace

    const event = {
      target: {
        name: props.name,
        value: cleanText,
      },
    } as React.ChangeEvent<HTMLTextAreaElement>;

    if (props.onChange) {
      props.onChange(event);
    } else if (formik?.handleChange) {
      formik.handleChange(event);
    }
  };

  const renderPreview = (text: string) => {
    if (!text) return <span className="text-gray-400 italic">Preview will appear here...</span>;

    return text.split('\n').map((line, lineIndex) => (
      <div key={lineIndex}>
        {line.split(/(\*[^*]+\*|_[^_]+_|~[^~]+~|```[^`]+```)/g).map((part, partIndex) => {
          if (part.startsWith('*') && part.endsWith('*')) {
            return <strong key={partIndex}>{part.slice(1, -1)}</strong>;
          }
          if (part.startsWith('_') && part.endsWith('_')) {
            return <em key={partIndex}>{part.slice(1, -1)}</em>;
          }
          if (part.startsWith('~') && part.endsWith('~')) {
            return <del key={partIndex}>{part.slice(1, -1)}</del>;
          }
          if (part.startsWith('```') && part.endsWith('```')) {
            return (
              <code key={partIndex} className="bg-gray-100 dark:bg-gray-800 px-1 rounded">
                {part.slice(3, -3)}
              </code>
            );
          }
          return <span key={partIndex}>{part}</span>;
        })}
      </div>
    ));
  };

  return (
    <div className={cn('grid', props.alignment === 'horizontal' ? 'grid-cols-2' : '')}>
      {props.label && <Label label={props.label} required={props.required} />}
      <div>
        {/* Formatting Toolbar */}
        <div className="flex items-center gap-1 p-2 border border-b-0 rounded-t-md bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          {formatButtons.map((format) => {
            const IconComponent = format.icon;
            return (
              <Button
                key={format.type}
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => applyFormat(format.type)}
                className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
                title={`${format.label} (${format.symbol}text${format.symbol})`}
              >
                <IconComponent size={16} />
              </Button>
            );
          })}

          <div className="h-4 w-px bg-gray-300 dark:bg-gray-600 mx-1" />

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={clearFormatting}
            className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
            title="Clear all formatting"
          >
            <RotateCcw size={16} />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setShowPreview(!showPreview)}
            className="h-8 px-2 hover:bg-gray-200 dark:hover:bg-gray-700 ml-auto"
            title="Toggle preview"
          >
            <Type size={16} />
            <span className="ml-1 text-xs">Preview</span>
          </Button>
        </div>

        {/* Input Area */}
        <div className={cn('relative flex items-center dark:bg-card bg-white')}>
          {props.startIcon && (
            <span className="absolute text-gray-500 left-3 flex items-center pr-3 z-10">{props.startIcon}</span>
          )}
          <Textarea
            ref={textareaRef}
            data-testid={props.dataTestId}
            autoComplete={props.autoComplete}
            onBlur={props.onBlur ?? formik?.handleBlur}
            onChange={props.onChange ?? formik?.handleChange}
            value={currentValue}
            name={props.name}
            onKeyDown={(e) => {
              // Handle Enter key
              if (e.key === 'Enter') {
                if (e.shiftKey) {
                  // Shift+Enter: new line (default behavior)
                  return;
                } else if (props.onEnter) {
                  // Enter: trigger onEnter if provided
                  e.preventDefault();
                  props.onEnter();
                  return;
                }
                // Default: new line
              }

              // Quick keyboard shortcuts
              if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                  case 'b':
                    e.preventDefault();
                    applyFormat('bold');
                    break;
                  case 'i':
                    e.preventDefault();
                    applyFormat('italic');
                    break;
                  case 'u':
                    e.preventDefault();
                    applyFormat('strikethrough');
                    break;
                  case 'k':
                    e.preventDefault();
                    applyFormat('monospace');
                    break;
                }
              }
            }}
            placeholder={props.placeholder || 'Type your WhatsApp message here...'}
            className={cn(
              props.startIcon ? 'pl-12' : '',
              props.endIcon ? 'pr-9' : '',
              errorMessage ? ' outline-red-500 border-red-500 bg-red-100' : '',
              'rounded-t-none border-t-0 min-h-[100px] resize-none',
              showPreview ? 'rounded-b-none border-b-0' : '',
            )}
            id={props.id}
          />
          {props.endIcon && (
            <span className="absolute text-gray-500 right-3 flex items-center pl-3 z-10">{props.endIcon}</span>
          )}
        </div>

        {/* Preview Area */}
        {showPreview && (
          <div className="border border-t-0 rounded-b-md p-3 bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 mb-2 font-medium">Preview:</div>
            <div className="text-sm whitespace-pre-wrap break-words min-h-[50px]">{renderPreview(currentValue)}</div>
          </div>
        )}

        {/* Helper text and shortcuts */}
        <div className="mt-2 space-y-1">
          {(errorMessage || props.helperText) && (
            <p className={cn('text-xs', errorMessage ? 'text-red-500' : 'text-gray-500')}>
              {errorMessage || props.helperText}
            </p>
          )}
          <div className="text-xs text-gray-400 space-y-1">
            <div>Shortcuts: Ctrl+B (Bold), Ctrl+I (Italic), Ctrl+U (Strikethrough), Ctrl+K (Monospace)</div>
            <div>Enter: {props.onEnter ? 'Send message' : 'New line'} • Shift+Enter: New line</div>
          </div>
        </div>
      </div>
    </div>
  );
}
