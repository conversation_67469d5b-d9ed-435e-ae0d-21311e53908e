import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Building, CalendarDays, CheckCircle, FileText, MapPin, User, XCircle } from 'lucide-react';
import type { IResListSubmission } from '@/types/response/IResListSubmission.ts';
import DateHelper from '@/lib/date-helper.ts';
import SubmissionStatusText from '@/components/SubmissionStatusText.tsx';

interface DialogDetailProps {
  page: {
    dataDetail: IResListSubmission | undefined;
    onCloseSubmission: () => void;
  };
  onApprove?: (id: string) => void;
  onReject?: (id: string) => void;
  loading?: boolean;
}

function DialogDetail({ page, onApprove, onReject, loading = false }: DialogDetailProps) {
  const data = page.dataDetail;

  if (!data) return null;

  const isPending = data.status === 'PENDING';

  const handleApprove = () => {
    if (onApprove) {
      onApprove(data.id);
    }
  };

  const handleReject = () => {
    if (onReject) {
      onReject(data.id);
    }
  };

  return (
    <Dialog open={!!page.dataDetail} onOpenChange={page.onCloseSubmission}>
      <DialogHeader>
        <DialogTitle className="text-xl font-semibold text-gray-900">Detail Pengajuan</DialogTitle>
      </DialogHeader>

      <Separator />
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className={'grid gap-2'}>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={data.account_profile_picture} alt={data.account_name} />
                <AvatarFallback>
                  {data.account_name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold text-lg text-gray-900">{data.account_name}</h3>
                <p className="text-sm text-gray-600">{data.work_unit_name}</p>
                <p className="text-xs text-gray-500">{data.agency_name}</p>
              </div>
            </div>
          </div>

          {/* Header Info */}
          <div className={'flex items-center justify-between gap-3'}>
            <div className={'font-semibold'}>{data.type_string}</div>
            <SubmissionStatusText enum={data.status} string={data.status_string} />
          </div>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <CalendarDays className="w-4 h-4 mr-2" />
                Periode
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="text-sm">
                  <span className="font-medium">Mulai:</span>{' '}
                  {DateHelper.toFormatDate(data.start_date, 'dd LLLL, yyyy - HH:mm')}
                </div>
                <div className="text-sm">
                  <span className="font-medium">Selesai:</span>{' '}
                  {DateHelper.toFormatDate(data.end_date, 'dd LLLL, yyyy - HH:mm')}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                  <Building className="w-4 h-4 mr-2" />
                  Informasi Unit Kerja
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="font-medium">Unit Kerja:</span>
                    <span className="ml-2">{data.work_unit_name}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Building className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="font-medium">Perangkat daerah:</span>
                    <span className="ml-2">{data.agency_name}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reason */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Alasan Pengajuan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-800 leading-relaxed">{data.reason}</p>
              </CardContent>
            </Card>
          </div>

          {/* Submission Date */}
          <div className="text-xs text-gray-500 flex items-center">
            <User className="w-3 h-3 mr-1" />
            Diajukan pada: {DateHelper.toFormatDate(data.created_date, 'dd LLLL, yyyy - HH:mm')}
          </div>

          {/* Action Buttons */}
          {isPending && (
            <>
              <Separator />
              <div className="flex space-x-3 pt-2">
                <Button loading={loading} onClick={handleReject} variant="destructive" className="flex-1">
                  <XCircle className="w-4 h-4 mr-2" />
                  Tolak
                </Button>
                <Button
                  loading={loading}
                  onClick={handleApprove}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Setujui
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default DialogDetail;
