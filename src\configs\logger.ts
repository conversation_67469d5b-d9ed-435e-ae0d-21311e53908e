import { ENV } from '@/constants/env';
import { isProd } from '@/lib/utils';
import * as Sentry from '@sentry/react';
const lg = Sentry.logger;

function formatMessage(message: string, context?: Record<string, any>): string {
  try {
    return `${message} | ${JSON.stringify(context)}`;
  } catch {
    return message;
  }
}

const logger = {
  trace: (message: string, context?: Record<string, any>) => {
    if (ENV.NODE_ENV === 'PRODUCTION') {
      Sentry.addBreadcrumb({
        level: 'debug',
        category: 'trace',
        message: formatMessage(message, context),
      });
    }
    console.debug(message, context);
    lg.info(`[DEBUG] - ${message}`, context);
  },

  debug: (message: string, context?: Record<string, any>) => {
    if (ENV.NODE_ENV === 'PRODUCTION') {
      Sentry.addBreadcrumb({
        level: 'debug',
        category: 'debug',
        message: formatMessage(message, context),
      });
    }
    console.debug(message, context);
    lg.info(`[DEBUG] - ${message}`, context);
  },

  info: (message: string, context?: Record<string, any>) => {
    console.info(message, context);
    lg.info(`[INFO] - ${message}`, context);
  },

  warn: (message: string, context?: Record<string, any>) => {
    console.warn(message, context);
    lg.info(`[WARNING] - ${message}`, context);
  },

  error: (message: string, context?: Record<string, any>) => {
    const error = new Error(message);
    if(isProd){

    Sentry.captureException(error, {
      extra: context,
    });
    }

    console.error(message, context);
    lg.info(`[ERROR] - ${message}`, context);
  },

  fatal: (message: string, context?: Record<string, any>) => {
    const error = new Error(message);
    Sentry.captureException(error, {
      level: 'fatal',
      extra: context,
    });
    console.error(`[FATAL] - ${message}`, context);
    lg.info(`[FATAL] - ${message}`, context);
  },
};

export default logger;
