/* Detail Employee Page Styles */

.profileHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.profileHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.avatarContainer {
  position: relative;
  z-index: 10;
}

.statusBadge {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.infoCard {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.infoCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.actionButton {
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.actionButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gradientText {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dataRow {
  transition: background-color 0.2s ease;
}

.dataRow:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .profileHeader {
    height: 120px;
  }

  .avatarContainer {
    margin-top: -60px;
  }
}

/* Prevent overflow and ensure proper text wrapping */
.textContainer {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.flexContainer {
  min-width: 0;
  flex: 1;
}

.iconContainer {
  flex-shrink: 0;
}

/* Ensure proper spacing on mobile */
@media (max-width: 640px) {
  .mobileStack {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .mobileFullWidth {
    width: 100%;
  }
}

/* Animation for loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Status indicator animations */
.statusActive {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* WhatsApp button hover effect */
.whatsappButton:hover {
  background-color: #25d366;
  color: white;
  transform: scale(1.1);
}

/* Card entrance animation */
.cardEnter {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
