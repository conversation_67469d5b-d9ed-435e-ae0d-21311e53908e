import type { ITableColumn } from '@/components/AppTable';
import AppTable from '@/components/AppTable';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { ROUTES } from '@/routes/routes';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { useWorkUnitPage } from './useWorkUnitPage';
import InputSearch from '@/components/InputSearch';
import AppPagination from '@/components/AppPagination';
import { Button } from '@/components/ui/button';
import { Info } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function WorkUnitPage() {
  const page = useWorkUnitPage();
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Unit Kerja',
      path: ROUTES.LIST_WORK_UNIT(),
    },
  ];

  const tableData: ITableColumn<IResWorkUnit>[] = [
    {
      headerTitle: 'Nama',
      component: (e) => <div>{e?.name}</div>,
    },
    {
      headerTitle: 'Instansi',
      component: (e) => <div>{e?.agency_name}</div>,
    },
    {
      headerTitle: 'Tipe Unit Kerja',
      component: (e) => <div>{e?.type}</div>,
    },
    {
      headerTitle: '',
      component: (e) => (
        <Link to={ROUTES.DETAIL_WORK_UNIT(e.id)}>
          <Button variant={'outline'} size={'icon'}>
            <Info />
          </Button>
        </Link>
      ),
    },
  ];
  return (
    <PageContainer>
      <PageTitle title="Unit Kerja" breadcrumb={breadcrumbs} />
      <div className="flex-1">
        <InputSearch
          placeholder="Cari nama unit kerja"
          active={page.isActiveSearch()}
          handleReset={page.handleResetSearch}
          searchValue={page.searchValue}
          setSearchValue={page.setSearchValue}
          handleSearch={() => page.handleSearch()}
        />
      </div>
      <AppTable
        loading={page.queryWorkUnit.isPending}
        column={tableData}
        data={page.queryWorkUnit.data?.response_data || []}
      />
      <AppPagination
        onPaginationChange={page.handlePaginationChange}
        dataPagination={page.queryWorkUnit.data?.paginated_data}
      />
    </PageContainer>
  );
}
