name: Deploy to VPS

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Clean install dependencies
        run: |
          rm -rf node_modules package-lock.json
          npm install

      - name: Install Rollup dependencies
        run: npm install @rollup/rollup-linux-x64-gnu

      - name: Build application
        run: npm run build:prod

      - name: Deploy to VPS
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SECRET_KEY }}
          source: 'dist/'
          target: '/var/www/production-nucalale-admin'
          strip_components: 0

      - name: Execute remote commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SECRET_KEY }}
          script: |
            # Backup current deployment if it exists
            if [ -d "/var/www/production-nucalale-admin/dist" ]; then
              timestamp=$(date +%Y%m%d_%H%M%S)
              mkdir -p /var/www/production-nucalale-admin/backups
              cp -r /var/www/production-nucalale-admin/dist /var/www/production-nucalale-admin/backups/dist_$timestamp
            fi

            # Ensure proper permissions
            chmod -R 755 /var/www/production-nucalale-admin/dist

            # Restart Nginx if needed
            sudo systemctl restart nginx

            echo "Deployment completed successfully!"
