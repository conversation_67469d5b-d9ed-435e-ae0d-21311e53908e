import { ASSETS } from '@/constants/assets';
import { Clock, Shield, Smartphone, Users } from 'lucide-react';

export default function SignInInformation() {
  // CSS animation for continuous scroll
  const scrollAnimation = `
    @keyframes scroll {
      0% {
        transform: translate3d(0, 0, 0);
      }
      100% {
        transform: translate3d(-${280 * 4}px, 0, 0);
      }
    }
    .animate-scroll {
      animation: scroll 20s linear infinite;
    }
  `;

  const features = [
    {
      icon: Shield,
      title: 'Keamanan Terjamin',
      description: 'Sistem keamanan berlapis dengan enkripsi data',
    },
    {
      icon: Smartphone,
      title: 'Teknologi Modern',
      description: 'Validasi wajah dengan AI dan GPS tracking',
    },
    {
      icon: Clock,
      title: 'Real-time Monitoring',
      description: 'Pantau kehadiran secara langsung dan akurat',
    },
    {
      icon: Users,
      title: 'Multi-Perangkat daerah',
      description: 'Mendukung berbagai Perangkat daerah dan unit kerja',
    },
  ];

  const duplicatedFeatures = [...features, ...features, ...features, ...features];
  return (
    <div
      className="relative bg-gradient-to- bg-cover lg:flex hidden flex-col justify-between p-8 text-white overflow-hidden"
      style={{ backgroundImage: `url(${ASSETS.IMG_LOGIN_1})` }}
    >
      <style dangerouslySetInnerHTML={{ __html: scrollAnimation }} />
      <div className="absolute inset-0 bg-gradient-to-b from-primary/10 to-black">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="relative z-10">
        <div className="flex items-center justify-center gap-3 mb-8">
          <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
            <img className="h-8" src={ASSETS.LG_BRAND} alt="Kabupaten Manggarai" />
          </div>
          <div>
            <h1 className="text-xl font-bold">Nuca Lale</h1>
            <p className="text-green-100 text-sm">Sistem Absensi Digital</p>
          </div>
        </div>

        <div className="mt-12 mb-8">
          <h2 className="font-bold text-2xl xl:text-3xl italic text-center leading-tight tracking-wide">
            Manggarai Maju Lebih Cepat
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mt-4 rounded-full"></div>
        </div>
      </div>

      <div className="relative z-10 space-y-8">
        {/* Infinite Horizontal Carousel Features Section */}
        <div className="mt-8 overflow-hidden relative">
          <div
            className="flex animate-scroll"
            style={{
              width: `${duplicatedFeatures.length * 280}px`,
            }}
            role="region"
            aria-label="Features running banner"
            aria-live="polite"
          >
            {duplicatedFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={`${feature.title}-${index}`}
                  className="flex items-start w-64 gap-4 group px-4 flex-shrink-0"
                  role="group"
                  aria-label={`Feature: ${feature.title}`}
                >
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm group-hover:bg-white/30 transition-all duration-300 flex-shrink-0">
                    <IconComponent className="w-6 h-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-sm mb-2 leading-tight text-white">{feature.title}</h3>
                    <p className="text-green-100 text-xs leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Gradient fade effects for smooth infinite appearance */}
          <div className="absolute left-0 top-0 w-12 h-full bg-gradient-to-r from-black/40 to-transparent pointer-events-none z-10"></div>
          <div className="absolute right-0 top-0 w-12 h-full bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-10"></div>
        </div>
        {/* Enhanced Local Quote Section */}
        <div className="text-center max-w-2xl mx-auto">
          <div className="relative">
            <blockquote className="text-xl xl:text-2xl text-green-100 italic font-medium leading-relaxed px-4">
              "Cama Lewang Ngger Peang, Cama Po'e Ngger One"
            </blockquote>
          </div>
          <div className="w-20 h-0.5 bg-gradient-to-r from-transparent via-green-200 to-transparent mx-auto mt-6 mb-4"></div>
          <p className="text-sm xl:text-base text-green-200 leading-relaxed font-light max-w-lg mx-auto">
            Semangat gotong royong dalam membangun disiplin kerja yang lebih baik
          </p>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
      <img
        src={ASSETS.DECORATION}
        alt="decoration"
        className="h-full w-[10%] absolute top-0 right-0 "
        style={{ zIndex: 100 }}
      />
    </div>
  );
}
