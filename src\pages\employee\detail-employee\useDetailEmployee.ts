import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import { EmployeeRepository } from '@/repositories/employee-repostiory';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailEmployee } from '@/types/response/IResDetailEmployee';
import type { BaseResponse } from '@/types/response/IResModel';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';

export function useDetailEmployee() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const employeeRepository = new EmployeeRepository();
  const { id } = useParams();
  const { toast } = useUi();
  const queryDetail = useQuery({
    queryKey: ['detailEmployee', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const response: BaseResponse<IResDetailEmployee> = await httpService.GET(ENDPOINT.DETAIL_EMPLOYEE(id!));
        return response.data.response_data;
      } catch (error) {
        errorService.fetchApiError(error);
      }
    },
  });

  const mutationApprove = useMutation({
    mutationFn: (id: string) => employeeRepository.setActiveEmployee(id),
    onSuccess: () => {
      toast.success('Pegawai berhasil disetujui');
      queryDetail.refetch();
    },
  });

  function onSubmitApproveEmployee() {
    console.log(id);
    if (id) {
      mutationApprove.mutate(id);
    }
  }

  return { queryDetail, onSubmitApproveEmployee, mutationApprove };
}
