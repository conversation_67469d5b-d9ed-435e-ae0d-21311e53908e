import BroadcastStatusText from '@/components/BroadcastStatusText.tsx';
import InputTextArea from '@/components/InputTextArea';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog.tsx';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useAuth } from '@/hooks/use-auth.ts';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { CalendarDays, CheckCircle, MessageSquare, Send, User, Users, XCircle } from 'lucide-react';
import { useDetailBroadcastPage } from './useDetailBroadcastPage';

export function DetailBroadcastPage() {
  const page = useDetailBroadcastPage();
  const data = page?.dataDetail;
  const auth = useAuth();

  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Broadcast WA',
      path: ROUTES.BROADCAST_LIST(),
    },
    {
      label: data?.title || '',
    },
  ];

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const UserInfo = ({
    user,
    title,
    date,
    reason,
  }: {
    user?: any;
    title: string;
    date?: string;
    reason?: string | null;
  }) => {
    if (!user) return null;

    return (
      <div className="space-y-2">
        <h4 className="font-medium text-sm text-gray-600">{title}</h4>
        <div className="flex items-start space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user.profile_picture} alt={user.name} />
            <AvatarFallback className="bg-blue-100 text-blue-600">
              {user.name?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900">{user.name || '-'}</p>
            <p className="text-xs text-gray-500">{user.agency_name || '-'}</p>
            <p className="text-xs text-gray-500">{user.work_unit_name || '-'}</p>
            {date && (
              <div className="flex items-center mt-1 text-xs text-gray-500">
                <CalendarDays className="h-3 w-3 mr-1" />
                {formatDate(date)}
              </div>
            )}
            {reason && (
              <p className="text-xs text-red-600 mt-1">
                <span className="font-medium">Alasan: </span>
                {reason}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  };

  function actionButtonPending() {
    if (auth.checkingPrivilege('APPROVE_REJECT_BROADCAST')) {
      return (
        <div className={'flex gap-2'}>
          <Button onClick={() => page.setShowModalOpen('REJECT')} variant={'destructive'}>
            Tolak Broadcast
          </Button>
          <Button onClick={() => page.setShowModalOpen('APPROVE')}>Setujui Broadcast</Button>
        </div>
      );
    } else {
      return <></>;
    }
  }

  function actionButtonSend() {
    if (auth.checkingPrivilege('SEND_BROADCAST')) {
      return (
        <div className={'flex gap-2'}>
          <Button onClick={() => page.setShowModalOpen('SEND')}>
            <Send />
            Kirim Broadcast
          </Button>
        </div>
      );
    } else {
      return <></>;
    }
  }

  function checkingActionButton() {
    switch (data?.status_enum) {
      case 'PENDING':
        return actionButtonPending();
      case 'READY_TO_SEND':
        return actionButtonSend();
      default:
        return <></>;
    }
  }

  function modalApprove() {
    return (
      <AlertDialog open={page?.showModalOpen === 'APPROVE'} onOpenChange={() => page.setShowModalOpen(undefined)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Yakin untuk menyetujui pesan broadcast ini ?</AlertDialogTitle>
            <AlertDialogDescription>
              Pesan broadcast yang disetujui akan dikirim ke seluruh pegawai yang terdaftar dalam cakupan broadcast ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <Button
              loading={page?.mutationApprove?.isPending}
              disabled={!!page?.mutationApprove?.isPending}
              onClick={() => page.mutationApprove.mutate()}
            >
              Continue
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  function modalSend() {
    return (
      <AlertDialog open={page?.showModalOpen === 'SEND'} onOpenChange={() => page.setShowModalOpen(undefined)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Yakin untuk mengirim pesan broadcast ini ?</AlertDialogTitle>
            <AlertDialogDescription>
              Pesan broadcast yang dikirim akan masuk ke WA seluruh pegawai yang terdaftar dalam cakupan broadcast ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <Button
              loading={page?.mutationSend?.isPending}
              disabled={!!page?.mutationSend?.isPending}
              onClick={() => page.mutationSend.mutate()}
            >
              Continue
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  function modalReject() {
    return (
      <Dialog open={page?.showModalOpen === 'REJECT'} onOpenChange={page.onCloseModalReject}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Yakin untuk menolak pesan broadcast ini ?</DialogTitle>
            <DialogDescription>
              pesan broadcast yang ditolak tidak akan dikirim, tetapi tetap tersimpan sebagai riwayat
            </DialogDescription>
          </DialogHeader>
          <div>
            <InputTextArea
              onChange={(e) => page.setReason(e?.target?.value)}
              value={page?.reason}
              disableFormik
              placeholder="Masukan alasan penolakan"
              name="reason"
              label="Alasan Penolakan"
              required
              id="reason"
            />
          </div>
          <div className="grid grid-cols-2 gap-2">
            <Button onClick={page.onCloseModalReject} variant={'outline'}>
              Batalkan
            </Button>
            <Button
              loading={page.mutationReject.isPending}
              disabled={page.mutationReject.isPending || !page.reason}
              onClick={() => page.mutationReject.mutate()}
              variant={'destructive'}
            >
              Ya, Tolak
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      {modalApprove()}
      {modalSend()}
      {modalReject()}
      <PageContainer loading={page?.queryDetail?.isPending}>
        <div className={'flex items-center justify-between'}>
          <PageTitle title="Detail broadcast WA" breadcrumb={breadcrumbs} />
          {checkingActionButton()}
        </div>

        <div className="space-y-6">
          {/* Header Information */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-xl font-semibold">{data?.title || '-'}</CardTitle>
                  <div className="flex items-center space-x-4 mt-2">
                    <Badge variant="outline">{data?.coverage_type_string || '-'}</Badge>
                  </div>
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div className="flex items-center">
                    <CalendarDays className="h-4 w-4 mr-1" />
                    {formatDate(data?.created_date || '')}
                  </div>
                  <div className={'mt-2'}>
                    <BroadcastStatusText label={data?.status_string || ''} status={data?.status_enum || 'PENDING'} />
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Message Content */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Pesan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm leading-relaxed whitespace-pre-wrap">{data?.body || '-'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Coverage Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Cakupan ({data?.coverages?.length || 0})
                </CardTitle>
              </CardHeader>
              <CardContent className="h-full">
                <div className="flex items-center justify-center h-full">
                  {data?.coverages && data.coverages.length > 0 ? (
                    <div className="space-y-2 max-h-48 overflow-y-auto w-full">
                      {data.coverages.map((coverage, index) => (
                        <div key={coverage.id || index} className="flex items-center p-2 w-full bg-gray-50 rounded-md">
                          <div className="h-2 w-2  rounded-full mr-3" />
                          <span className="text-sm">{coverage.name || '-'}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex h-full  items-center justify-center gap-4 flex-col">
                      <p className="text-gray-500 text-sm">Semua cakupan pegawai</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Creator Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Pembuat
                </CardTitle>
              </CardHeader>
              <CardContent>
                <UserInfo user={data?.created_by} title="" date={data?.created_date} />
              </CardContent>
            </Card>
          </div>

          {/* Action Information */}
          {(data?.approve_and_reject_data || data?.send_by) && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Approval/Rejection Data */}
              {data?.approve_and_reject_data && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      {data?.status_enum === 'REJECT' ? (
                        <XCircle className="h-5 w-5 mr-2 text-red-500" />
                      ) : (
                        <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
                      )}
                      {data?.status_enum === 'REJECT' ? 'Ditolak' : 'Disetujui'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <UserInfo
                      user={data.approve_and_reject_data}
                      title=""
                      date={data.approve_and_reject_data.date}
                      reason={data.approve_and_reject_data.reason}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Send By Information */}
              {data?.send_by && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Send className="h-5 w-5 mr-2 text-blue-500" />
                      Dikirim Oleh
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <UserInfo user={data.send_by} title="" date={data.send_by.date} />
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </PageContainer>
    </>
  );
}
