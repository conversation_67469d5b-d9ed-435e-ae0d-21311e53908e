import { BroadcastRepository } from '@/repositories/broadcast-repository';
import type { IPaginatedParams } from '@/types/response/IResModel';
import type { IFilterList } from '@/types/type/IFilterList';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

export function useBroadcastListPage() {
  const broadcastRepository = new BroadcastRepository();
  const initFilter: IFilterList = {
    page: 0,
    size: 10,
  };

  const [filter, setFilter] = useState<IFilterList>(initFilter);

  const queryList = useQuery({
    queryKey: ['list_broadcast', filter],
    queryFn: async () => await broadcastRepository.listBroadcast(filter),
  });

  const querySummaryCount = useQuery({
    queryKey: ['count_summary', filter],
    queryFn: async () => await broadcastRepository.getSummaryCount(filter),
  });

  function handlePaginationChange(params: IPaginatedParams) {
    setFilter((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }

  return { queryList, handlePaginationChange, querySummaryCount };
}
