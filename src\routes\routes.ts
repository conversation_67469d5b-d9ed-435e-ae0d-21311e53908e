export const ROUTES = {
  HOME: () => `/`,
  SIGN_IN: () => `/auth/sign-in`,
  EMPLOYEE_LIST: () => `/employee`,
  CREATE_EMPLOYEE: () => `/employee/new`,
  DETAIL_AGENCY: (id: string) => `/agency/detail/${id}`,
  CREATE_WORK_UNIT: (agencyId: string) => `/agency/work-unit/new/${agencyId}`,
  DETAIL_WORK_UNIT: (workUnitId: string) => `/agency/work-unit/detail/${workUnitId}`,
  DETAIL_EMPLOYEE: (id: string) => `/employee/detail/${id}`,
  EDIT_WORK_UNIT: (id: string) => `/work-unit/edit/${id}`,
  REGISTER_EMPLOYEE_FACE: (id: string) => `/employee/register-face/${id}`,
  SETTING_SHIFT_TABLE: (id: string) => `/agency/work-unit/${id}/setting-shift`,
  SETTING_WORK_UNIT_LOCATION: (id: string) => `/work-unit/setting/location/${id}`,
  SETTING_SCHEDULE_REGULAR: (id: string) => `/work-unit/setting/schedule/regular/${id}`,
  LIST_WORK_UNIT: () => `/work-unit`,
  EDIT_EMPLOYEE: (id: string) => `/employee/edit/${id}`,
  SETTING_SHIFT_WORK_UNIT: (id: string) => `/work-unit/setting/shift/${id}`,
  SETTING_SHIFT_ATTENDANCE_TIME: (id: string) => `/work-unit/setting/shift/attendance-time/${id}`,
  MASTER_DATA: {
    AGENCY: () => '/agency',
    NEW_AGENCY: () => `/agency/new`,
    EDIT_AGENCY: (id: string) => `/agency/edit/${id}`,
  },
  ATTENDANCE_HISTORY: () => `/history`,
  ATTENDANCE_LIST: () => `/attendance`,
  ADD_WORK_UNIT_MANGER : (id : string) => `/work-unit/add-manager/${id}`
};
