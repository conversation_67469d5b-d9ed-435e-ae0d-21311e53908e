import EmployeeStatusText from '@/components/EmployeeStatusText';
import IconContainer from '@/components/IconContainer';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/hooks/use-auth';
import { getInitials } from '@/lib/utils';
import { ROUTES } from '@/routes/routes';
import type { EmployeeStatusType } from '@/types/type/EmployeeStatusType';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import {
  Briefcase,
  Building,
  Calendar,
  Check,
  Clock,
  Edit,
  Eye,
  History,
  IdCard,
  Mail,
  MapPin,
  Phone,
  Shield,
  User,
  UserCheck,
} from 'lucide-react';
import { MdWhatsapp } from 'react-icons/md';
import { Link, useNavigate, useParams } from 'react-router-dom';
import DetailEmployeeSkeleton from './DetailEmployeeSkeleton';
import { useDetailEmployee } from './useDetailEmployee';

const getStatusInfo = (status: EmployeeStatusType) => {
  switch (status) {
    case 'ACTIVE':
      return { variant: 'success' as const, label: 'Aktif', icon: UserCheck };
    case 'INACTIVE':
      return { variant: 'destructive' as const, label: 'Tidak Aktif', icon: User };
    case 'PENDING':
      return { variant: 'warning' as const, label: 'Menunggu', icon: Clock };
    case 'WAITING_PHONE_VERIFICATION':
      return { variant: 'info' as const, label: 'Verifikasi Telepon', icon: Phone };
    case 'WAITING_FACE_REGISTRATION':
      return { variant: 'info' as const, label: 'Registrasi Wajah', icon: Eye };
    default:
      return { variant: 'secondary' as const, label: 'Unknown', icon: User };
  }
};

// Helper function to format date
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export default function DetailEmployeePage() {
  const navigate = useNavigate();
  const page = useDetailEmployee();
  const data = page?.queryDetail?.data;
  const { id } = useParams();
  const auth = useAuth();

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Pegawai',
      path: ROUTES.EMPLOYEE_LIST(),
    },
    {
      label: data?.name || '',
    },
  ];
  const statusInfo = data?.status ? getStatusInfo(data.status) : null;
  const StatusIcon = statusInfo?.icon || User;

  return (
    <PageContainer loading={page.mutationAssignAdmin.isPending}>
      <div className="flex justify-between items-start">
        <PageTitle title="Detail Pegawai" breadcrumb={breadcrumb} />
        <div className="flex gap-2">
          {id && (
            <Link to={ROUTES.ATTENDANCE_HISTORY() + `?account_id=${id}`}>
              <Button variant="outline">
                <History className="w-4 h-4 mr-2" />
                Riwayat Absensi
              </Button>
            </Link>
          )}
          {auth.checkingPrivilege('EDIT_EMPLOYEE') && (
            <Button onClick={() => navigate(ROUTES.EDIT_EMPLOYEE(id || ''))}>
              <Edit className="w-4 h-4 mr-2" />
              Edit Pegawai
            </Button>
          )}
        </div>
      </div>

      {page.queryDetail?.isPending || page?.mutationApprove?.isPending ? (
        <DetailEmployeeSkeleton />
      ) : (
        <div className="space-y-6 animate-in fade-in-50 duration-500 ">
          {data?.status === 'PENDING' && (
            <Card>
              <CardContent className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <IconContainer icon="Info" />
                  <div>
                    <div className="font-medium">Menunggu Persetujuan Admin</div>
                    <p className="text-muted-foreground text-xs">
                      Pegawai akan mendapatkan akses penuh ke semua fitur setelah disetujui oleh admin.
                    </p>
                  </div>
                </div>
                <div>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant={'link'}>
                        <Check className="mr-1" size={16} /> Setujui Sekarang
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Konfirmasi Persetujuan</AlertDialogTitle>
                        <AlertDialogDescription>
                          Tindakan ini akan memberikan akses penuh kepada pegawai. Apakah Anda yakin ingin melanjutkan?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Batal</AlertDialogCancel>
                        <AlertDialogAction onClick={page.onSubmitApproveEmployee}>Setujui</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          )}
          <Card className="overflow-hidden transition-all duration-300  py-0">
            <div className="bg-gradient-to-r from-primary to-blue-500 h-28"></div>
            <CardContent className="relative pt-0 pb-6">
              <div className="flex flex-col sm:flex-row gap-6 -mt-16">
                <div className="relative">
                  <Avatar className="w-32 h-32 border-4 border-white ">
                    <AvatarImage src={data?.profile_picture} alt={data?.name} className="object-cover" />
                    <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                      {data?.name ? getInitials(data.name) : 'N/A'}
                    </AvatarFallback>
                  </Avatar>
                </div>

                <div className="flex-1 pt-4 sm:pt-8 min-w-0">
                  <div className="flex flex-col pt-4 sm:flex-row sm:items-start sm:justify-between gap-4">
                    <div className="min-w-0 flex-1">
                      <h1 className="text-2xl font-bold text-gray-900 mb-2 break-words">
                        {data?.name || 'Nama tidak tersedia'}
                      </h1>
                      <div className="flex flex-wrap items-center gap-3 text-gray-600 mb-3">
                        <div className="flex items-center gap-2 min-w-0">
                          <IdCard className="w-4 h-4 flex-shrink-0" />
                          <span className="font-medium break-all">NIP: {data?.nip || '-'}</span>
                        </div>
                        <div className="flex items-center gap-2 min-w-0">
                          <Shield className="w-4 h-4 flex-shrink-0" />
                          <span className="capitalize break-words">{data?.role || '-'}</span>
                        </div>
                      </div>
                      <div className="flex  gap-2 min-w-0">
                        <Building className="w-4 h-4 mt-1 text-gray-500 flex-shrink-0" />
                        <span className="text-gray-700 font-medium break-words">{data?.agency_name || '-'}</span>
                      </div>
                    </div>

                    <div className="flex flex-col items-end justify-end gap-2 flex-shrink-0">
                      {data?.status && <EmployeeStatusText string={data.status_string} status={data.status} />}
                      <div className="text-sm text-gray-500 break-all">ID: {data?.account_id || '-'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="transition-all duration-300  ">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Informasi Kontak
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                  <div className="flex items-center gap-3 min-w-0">
                    <Mail className="w-4 h-4 text-gray-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-600">Email</span>
                  </div>
                  <span className="text-sm text-gray-900 break-all sm:text-right sm:max-w-[60%]">
                    {data?.email || '-'}
                  </span>
                </div>
                <Separator />
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                  <div className="flex items-center gap-3 min-w-0">
                    <Phone className="w-4 h-4 text-gray-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-600">Telepon</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-900">{data?.phone || '-'}</span>
                    {data?.phone && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 flex-shrink-0"
                        onClick={() => window.open(`https://wa.me/${data.phone.replace(/\D/g, '')}`, '_blank')}
                      >
                        <MdWhatsapp className="w-4 h-4 text-green-600" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="transition-all duration-300  ">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="w-5 h-5" />
                  Informasi Pekerjaan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                  <div className="flex items-center gap-3 min-w-0">
                    <Building className="w-4 h-4 text-gray-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-600">Perangkat daerah</span>
                  </div>
                  <span className="text-sm text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                    {data?.agency_name || '-'}
                  </span>
                </div>
                <Separator />
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                  <div className="flex items-center gap-3 min-w-0">
                    <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-600">Unit Kerja</span>
                  </div>
                  <span className="text-sm text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                    {data?.work_unit_name || '-'}
                  </span>
                </div>
                <Separator />
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                  <div className="flex items-center gap-3 min-w-0">
                    <Shield className="w-4 h-4 text-gray-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-600">Role</span>
                  </div>
                  <Badge variant="outline" className="capitalize self-start sm:self-center">
                    {data?.role || '-'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* System Information */}
          <Card className="transition-all duration-300  ">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Informasi Sistem
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                    <div className="flex items-center gap-3 min-w-0">
                      <Calendar className="w-4 h-4 text-gray-500 flex-shrink-0" />
                      <span className="text-sm font-medium text-gray-600">Tanggal Dibuat</span>
                    </div>
                    <span className="text-sm text-gray-900 break-words sm:text-right">
                      {formatDate(data?.created_date || '')}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                    <div className="flex items-center gap-3 min-w-0">
                      <User className="w-4 h-4 text-gray-500 flex-shrink-0" />
                      <span className="text-sm font-medium text-gray-600">Dibuat Oleh</span>
                    </div>
                    <span className="text-sm text-gray-900 break-words sm:text-right">{data?.created_by || '-'}</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                    <div className="flex items-center gap-3 min-w-0">
                      <Clock className="w-4 h-4 text-gray-500 flex-shrink-0" />
                      <span className="text-sm font-medium text-gray-600">Terakhir Diupdate</span>
                    </div>
                    <span className="text-sm text-gray-900 break-words sm:text-right">
                      {formatDate(data?.updated_date || '')}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                    <div className="flex items-center gap-3 min-w-0">
                      <StatusIcon className="w-4 h-4 text-gray-500 flex-shrink-0" />
                      <span className="text-sm font-medium text-gray-600">Status</span>
                    </div>
                    {data?.status && <EmployeeStatusText string={data.status_string} status={data.status} />}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="transition-all duration-300  ">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Aksi Cepat
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200  .5 hover:border-blue-300 min-w-0"
                  onClick={() => navigate(ROUTES.REGISTER_EMPLOYEE_FACE(data?.account_id || ''))}
                >
                  <Eye className="w-5 h-5 flex-shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium break-words">Registrasi Wajah</div>
                    <div className="text-xs text-gray-500 break-words">Daftarkan wajah pegawai</div>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200  .5 hover:border-green-300 min-w-0"
                >
                  <Phone className="w-5 h-5 flex-shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium break-words">Kirim Notifikasi</div>
                    <div className="text-xs text-gray-500 break-words">Kirim pesan ke pegawai</div>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200  .5 hover:border-yellow-300 min-w-0"
                >
                  <UserCheck className="w-5 h-5 flex-shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium break-words">Reset Password</div>
                    <div className="text-xs text-gray-500 break-words">Reset kata sandi</div>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200  .5 hover:border-purple-300 min-w-0"
                  onClick={() => navigate(ROUTES.EMPLOYEE_LIST())}
                >
                  <Edit className="w-5 h-5 flex-shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium break-words">Edit Pegawai</div>
                    <div className="text-xs text-gray-500 break-words">Ubah data pegawai</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </PageContainer>
  );
}
