import type { ILabelValue } from '@/types/type/ILabelValue';
import type { TimeTableType } from '@/types/type/TimeTableType';

export const dayListString = ['Sen<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu', '<PERSON><PERSON>'];

export const dataTimeTableType: ILabelValue<TimeTableType>[] = [
  {
    label: 'Regular',
    value: 'REGULAR',
  },
  {
    label: 'Shift',
    value: 'SHIFT',
  },
];
