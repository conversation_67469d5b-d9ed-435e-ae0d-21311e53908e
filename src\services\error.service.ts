import { useAuth } from '@/hooks/use-auth';
import { useUi } from '@/hooks/useUi.ts';

export default class ErrorService {
  private ui = useUi();
  private auth = useAuth();

  private handleSnackbar(message: string) {
    this.ui.toast.error(message);
  }

  public fetchApiError(error: any) {
    if (error) {
      if (error.status === 401 || error?.response?.status === 401) {
        this.auth.logOut();
      } else if (error?.response?.data?.errors?.message) {
        const message = error?.response?.data?.errors?.message || 'Terjadi Kesalahan Pada Sistem';
        this.handleSnackbar(message);
      } else if (error?.code === 'ERR_NETWORK' || error?.message === 'Network Error') {
        this.handleSnackbar('Tidak dapat terhubung ke server. Periksa server backend dan koneksi jaringan Anda.');
      } else if (error?.request) {
        this.handleSnackbar('Tidak ada respon dari server. Periksa koneksi jaringan.');
      } else {
        this.handleSnackbar('Ke<PERSON>ahan dalam mengirim permintaan.');
      }
    } else {
      this.handleSnackbar('Terjadi Kesalahan Pada Sistem');
    }
    throw new Error(error);
  }
}
