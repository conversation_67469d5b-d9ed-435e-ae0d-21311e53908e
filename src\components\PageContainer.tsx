import { cn } from '@/lib/utils';
import type { ReactNode } from 'react';
import CardLoading from './CardLoading';

export default function PageContainer(props: IProps) {
  return (
    <div
      className={cn(
        ' h-full mx-auto grid gap-4',
        props.disablePaddingY ? '' : 'py-8',
        props.size === 'full' ? 'px-4 w-full' : 'w-5xl',
        props.className,
      )}
    >
      {props.loading ? <CardLoading /> : props.children}
    </div>
  );
}

interface IProps {
  children: ReactNode;
  disablePaddingY?: boolean;
  loading?: boolean;
  className?: string;
  size?: 'base' | 'full';
}
