import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, Upload, X } from 'lucide-react';
import { useCallback, useMemo, useRef, useState } from 'react';
import Cropper, { type Area, type Point } from 'react-easy-crop';
import { UploadService } from '@/services/upload-service';
import ErrorService from '@/services/error.service';
import getCroppedImg from '@/utils/cropper-utils';
import { cn } from '@/lib/utils';
import Label from '@/components/ui/Label';
import { Slider } from '@/components/ui/slider';
import { useUi } from '@/hooks/useUi';

interface IProps {
  ratio?: number;
  label?: string;
  onBlur?: any;
  required?: boolean;
  onChange?: (e?: string) => void;
  value?: string;
  name?: string;
  errorMessage?: string;
  fullHeight?: boolean;
  folderName: 'PERSONAL_LINK' | 'PROJECT' | 'DOCUMENT' | 'AGENCY_IMAGE';
  className?: string;
  placeholder?: string;
}

export default function UploadBox(props: IProps) {
  const { toast } = useUi();
  const [zoom, setZoom] = useState<number>(1); // Changed from array to number
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [fileCrop, setFileCrop] = useState<string | null>(null);
  const [cropper, setCropper] = useState<Area | null>(null);
  const [loadingUpload, setLoadingUpload] = useState<boolean>(false);

  const inputRef = useRef<HTMLInputElement>(null);

  // Memoize aspect ratio to prevent unnecessary re-renders
  const aspectSet = useMemo(() => props.ratio || 16 / 9, [props.ratio]);

  // Memoize upload service instance
  const uploadService = useMemo(() => new UploadService(), []);

  const uploadProcess = useCallback(
    async (files: Blob) => {
      try {
        if (!files) {
          setLoadingUpload(false);
          return;
        }

        setLoadingUpload(true);

        try {
          const url = await uploadService.uploadBlob(files, props.folderName);
          // Check if component is still mounted before calling onChange
          if (props.onChange) {
            props.onChange(url);
          }
          toast.success('Image uploaded successfully');
        } catch (e) {
          new ErrorService().fetchApiError(e);
        } finally {
          setLoadingUpload(false);
        }
      } catch (error) {
        setLoadingUpload(false);
        toast.error('Failed to upload image');
      }
    },
    [uploadService, props.folderName, props.onChange, toast],
  );

  const showCropper = useCallback(async () => {
    if (!fileCrop || !cropper) return;

    try {
      const resultCropper: any = await getCroppedImg(fileCrop, cropper, 0);
      const file: File = resultCropper.file;
      await uploadProcess(file);
      setFileCrop(null);
    } catch (e) {
      toast.error('Failed to setup cropper');
      throw new Error(e as any);
    }
  }, [fileCrop, cropper, uploadProcess, toast]);

  const onCropComplete = useCallback((_: Area, croppedAreaPixels: Area) => {
    setCropper(croppedAreaPixels);
  }, []);

  const onClearImage = useCallback(() => {
    if (props.onChange) {
      props.onChange(undefined);
    }
  }, [props.onChange]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || !files.length) return;

    const reader = new FileReader();
    reader.onload = () => {
      setFileCrop(reader.result as string);
    };
    reader.readAsDataURL(files[0]);

    // Clear the input value to allow selecting the same file again
    e.target.value = '';
  }, []);

  const handleDialogClose = useCallback((open: boolean) => {
    if (!open) {
      setFileCrop(null);
      // Reset crop and zoom when closing
      setCrop({ x: 0, y: 0 });
      setZoom(1);
    }
  }, []);

  const handleUploadClick = useCallback(() => {
    if (!props.value && !loadingUpload && inputRef.current) {
      inputRef.current.click();
    }
  }, [props.value, loadingUpload]);

  const handleClearClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onClearImage();
    },
    [onClearImage],
  );

  // Handle zoom change properly
  const handleZoomChange = useCallback((newZoom: number) => {
    setZoom(newZoom);
  }, []);

  // Handle zoom slider change
  const handleZoomSliderChange = useCallback((value: number[]) => {
    if (value && value.length > 0) {
      setZoom(value[0]);
    }
  }, []);

  // Memoize the cropper component but avoid creating functions inside render
  const CropperComponent = useMemo(() => {
    if (!fileCrop) return null;

    return (
      <div className="relative w-full h-[400px]">
        <Cropper
          image={fileCrop}
          crop={crop}
          zoom={zoom}
          aspect={aspectSet}
          onCropChange={setCrop}
          onZoomChange={handleZoomChange}
          onCropComplete={onCropComplete}
        />
      </div>
    );
  }, [
    fileCrop,
    crop,
    zoom,
    aspectSet,
    onCropComplete,
    handleZoomChange,
    handleZoomSliderChange,
    showCropper,
    loadingUpload,
  ]);

  return (
    <>
      <Dialog open={!!fileCrop} onOpenChange={handleDialogClose}>
        <DialogContent className="max-w-2xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Crop Image</DialogTitle>
          </DialogHeader>
          {CropperComponent}
          <DialogFooter className="w-full">
            <div className="w-full space-y-4">
              <div className="space-y-2">
                <Label label="Zoom" />
                <Slider
                  min={1}
                  max={3}
                  step={0.1}
                  value={[zoom]}
                  onValueChange={handleZoomSliderChange}
                  className="w-full"
                />
              </div>

              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={() => setFileCrop(null)} className="flex-1">
                  Cancel
                </Button>

                <Button onClick={showCropper} disabled={loadingUpload} className="flex-1">
                  {loadingUpload && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Submit
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className={cn('space-y-2', props.className)}>
        {props.label && <Label label={props.label} required={props.required} />}

        <div
          className={cn(
            'relative border-2 border-dashed rounded-lg transition-colors duration-200',
            props.fullHeight ? 'h-full min-h-[200px]' : 'h-[200px]',
            props.errorMessage
              ? 'border-destructive bg-destructive/5'
              : 'border-muted-foreground/25 hover:border-muted-foreground/50',
            !props.value && 'cursor-pointer hover:bg-muted/50',
          )}
          onClick={handleUploadClick}
        >
          {props.value ? (
            <div className="relative h-full w-full p-4">
              <div className="relative h-full w-full bg-muted rounded-md overflow-hidden flex items-center justify-center">
                <img src={props.value} alt="Uploaded" className="max-h-full max-w-full object-contain rounded-md" />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 h-8 w-8 p-0"
                  onClick={handleClearClick}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full space-y-3 text-muted-foreground">
              {loadingUpload ? (
                <>
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="text-sm">Uploading...</span>
                </>
              ) : (
                <>
                  <Upload className="h-8 w-8" />
                  <div className="text-center">
                    <p className="text-sm font-medium">{props.placeholder || 'Click to upload image'}</p>
                    <p className="text-xs text-muted-foreground/70">PNG, JPG, GIF up to 10MB</p>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        {props.errorMessage && <p className="text-sm text-destructive">{props.errorMessage}</p>}

        <input
          ref={inputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          onBlur={props.onBlur}
          name={props.name}
          className="hidden"
        />
      </div>
    </>
  );
}
