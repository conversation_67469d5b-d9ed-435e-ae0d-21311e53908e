import * as faceapi from 'face-api.js';
import { useEffect, useRef, useState } from 'react';
import Webcam from 'react-webcam';

const videoConstraints = {
  facingMode: 'user',
  width: { ideal: 1280 },
  height: { ideal: 720 },
};

interface IProps {
  onCapture: (image: string) => void;
  showDetections?: boolean;
  onFaceAnalysis?: (analysis: FaceAnalysisResult) => void;
  webcamRef?: React.RefObject<Webcam>;
}

export interface FaceAnalysisResult {
  faceDetected: boolean;
  lookingLeft: boolean;
  lookingRight: boolean;
  lookingCenter: boolean;
  mouthOpen: boolean;
  showingTeeth: boolean;
}

export default function RegisterCamCard({
  showDetections = true,
  onFaceAnalysis,
  webcamRef: externalWebcamRef,
}: IProps) {
  const internalWebcamRef = useRef<Webcam | null>(null);
  const webcamRef = externalWebcamRef || internalWebcamRef;
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [modelsLoaded, setModelsLoaded] = useState(false);
  const [faceDetected, setFaceDetected] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 640, height: 480 });
  const [loadingProgress, setLoadingProgress] = useState(0);

  useEffect(() => {
    const loadModels = async () => {
      try {
        const MODEL_URL = '/models';

        await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);
        setLoadingProgress(33);
        console.info('TinyFaceDetector model loaded');

        await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
        setLoadingProgress(66);
        console.info('FaceLandmark68 model loaded');

        await faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL);
        setLoadingProgress(100);
        console.info('FaceExpression model loaded');

        setModelsLoaded(true);
        console.info('All required face detection models loaded successfully');
      } catch (error) {
        console.error('Error loading face detection models:', error);
      }
    };
    loadModels();
  }, []);

  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new ResizeObserver(() => {
      const width = containerRef.current!.offsetWidth;
      const height = (width * 9) / 16; // 16:9 ratio
      setDimensions({ width, height });
    });

    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!modelsLoaded) return;

    const interval = setInterval(async () => {
      const video = webcamRef.current?.video;
      const canvas = canvasRef.current;

      if (video && video.readyState === 4 && canvas) {
        try {
          // Detect face with landmarks and expressions
          const detections = await faceapi
            .detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
            .withFaceLandmarks()
            .withFaceExpressions();

          faceapi.matchDimensions(canvas, dimensions);
          const resized = faceapi.resizeResults(detections, dimensions);

          const context = canvas.getContext('2d');
          if (!context) return;

          context.clearRect(0, 0, canvas.width, canvas.height);

          if (showDetections) {
            faceapi.draw.drawDetections(canvas, resized);
            faceapi.draw.drawFaceLandmarks(canvas, resized);
          }

          // Update face detection status
          const faceFound = detections.length > 0;
          setFaceDetected(faceFound);

          if (faceFound && onFaceAnalysis) {
            const detection = detections[0];
            const landmarks = detection.landmarks;
            const expressions = detection.expressions;

            // Calculate face orientation based on landmarks
            const jawLeft = landmarks.getJawOutline()[0];
            const jawRight = landmarks.getJawOutline()[16];
            const nose = landmarks.getNose()[3];

            // Calculate horizontal position of nose relative to jaw
            const jawWidth = jawRight.x - jawLeft.x;
            const noseOffset = (nose.x - jawLeft.x) / jawWidth;

            // Determine if looking left, right, or center
            const lookingLeft = noseOffset < 0.43;
            const lookingRight = noseOffset > 0.57;
            const lookingCenter = !lookingLeft && !lookingRight;

            // Detect mouth open using mouth landmarks
            const upperLip = landmarks.getMouth()[13]; // Top of bottom lip
            const lowerLip = landmarks.getMouth()[19]; // Bottom of bottom lip
            const leftCorner = landmarks.getMouth()[0]; // Left corner of mouth
            const rightCorner = landmarks.getMouth()[6]; // Right corner of mouth

            // Calculate mouth width and height
            const mouthWidth = Math.sqrt(
              Math.pow(rightCorner.x - leftCorner.x, 2) + Math.pow(rightCorner.y - leftCorner.y, 2),
            );

            const mouthHeight = Math.sqrt(Math.pow(upperLip.x - lowerLip.x, 2) + Math.pow(upperLip.y - lowerLip.y, 2));

            // Mouth is open if height/width ratio exceeds threshold
            const mouthRatio = mouthHeight / mouthWidth;
            const mouthOpen = mouthRatio > 0.25;

            // For showing teeth, we'll use a combination of mouth open and happy expression
            // This is a simplification as actual teeth detection would require more complex models
            const showingTeeth = mouthOpen && expressions.happy > 0.3;

            // Send analysis to parent component
            onFaceAnalysis({
              faceDetected: true,
              lookingLeft,
              lookingRight,
              lookingCenter,
              mouthOpen,
              showingTeeth,
            });
          } else if (onFaceAnalysis) {
            // No face detected
            onFaceAnalysis({
              faceDetected: false,
              lookingLeft: false,
              lookingRight: false,
              lookingCenter: false,
              mouthOpen: false,
              showingTeeth: false,
            });
          }
        } catch (error) {
          console.error('Error during face detection:', error);
        }
      }
    }, 100); // Faster interval for more responsive detection

    return () => clearInterval(interval);
  }, [modelsLoaded, dimensions, showDetections, onFaceAnalysis]);

  return (
    <div className="relative w-full" ref={containerRef} style={{ aspectRatio: '16 / 9' }}>
      <Webcam
        ref={webcamRef}
        audio={false}
        screenshotFormat="image/jpeg"
        videoConstraints={videoConstraints}
        className="absolute top-0 left-0 w-full h-full object-cover z-0"
        mirrored={true}
      />
      <canvas
        ref={canvasRef}
        width={dimensions.width}
        height={dimensions.height}
        className="absolute top-0 left-0 z-10"
      />

      {/* Face detection indicator */}
      {faceDetected && (
        <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-md text-xs z-20">
          Wajah Terdeteksi
        </div>
      )}

      {/* Loading indicator */}
      {!modelsLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20">
          <div className="text-white text-center w-64">
            <div className="h-2 bg-gray-700 rounded-full mb-2">
              <div
                className="h-full bg-blue-500 rounded-full transition-all duration-300"
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>
            <p className="mt-2">Memuat model deteksi wajah... {loadingProgress.toFixed(0)}%</p>
          </div>
        </div>
      )}

      {/* Countdown indicator for auto capture */}
      {/* Ini akan ditangani oleh komponen induk */}
    </div>
  );
}
