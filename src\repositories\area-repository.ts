import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { INameId } from '@/types/type/INameId.ts';

export class AreaRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async getProvince() {
    return this.httpService
      .GET(ENDPOINT.LIST_PROVINCE())
      .then((res: BaseResponse<INameId[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async getCity(provinceId: string) {
    return this.httpService
      .GET(ENDPOINT.LIST_CITY(provinceId))
      .then((res: BaseResponse<INameId[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async getDistrict(cityId: string) {
    return this.httpService
      .GET(ENDPOINT.LIST_DISTRICT(cityId))
      .then((res: BaseResponse<INameId[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async getSubDistrict(districtId: string) {
    return this.httpService
      .GET(ENDPOINT.SUB_DISTRICT(districtId))
      .then((res: BaseResponse<INameId[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }
}
