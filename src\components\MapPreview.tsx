import type { LatLngExpression } from 'leaflet';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-leaflet';
import { markerIcon } from './MapsSearch';

export default function MapPreview(props: IProps) {
  const position: LatLngExpression = [props.lat, props.lng];
  return (
    <div>
      <MapContainer
        center={position || [0, 0]}
        zoom={18}
        style={{ height: '400px', width: '100%', zIndex: 0 }}
        className="z-0"
      >
        <TileLayer
          attribution="&copy; OpenStreetMap contributors"
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {position && <Marker position={position} icon={markerIcon} />}
      </MapContainer>
    </div>
  );
}

interface IProps {
  lat: number;
  lng: number;
}
