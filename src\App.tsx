import RoutesBuilder from './components/route-builder';
import { Toaster } from '@/components/ui/sonner.tsx';
import { useApp } from '@/hooks/useApp.ts';
import { useOnLoad } from './hooks/useOnLoad';
import { ENV } from './constants/env';

export default function App() {
  useApp();
  useOnLoad();
  return (
    <>
      {ENV.NODE_ENV !== 'PRODUCTION' && (
        <img
          alt={'Testing Banner'}
          className="fixed top-0 h-[64px] right-0"
          style={{ zIndex: 99999 }}
          src="/assets/svg/testing_banner.svg"
        />
      )}
      <Toaster />
      <RoutesBuilder />
    </>
  );
}
