import { useAuth } from '@/hooks/use-auth';
import { ROUTES } from '@/routes/routes.ts';
import {
  Building,
  Calendar,
  HistoryIcon,
  LayoutGrid,
  type LucideIcon,
  Paperclip,
  Settings,
  Users,
  Workflow,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { MdWhatsapp } from 'react-icons/md';
import { useLocation } from 'react-router-dom';

type Submenu = {
  href: string;
  label: string;
  active?: boolean;
};

type Menu = {
  href: string;
  label: string;
  active?: boolean;
  icon: LucideIcon;
  submenus?: Submenu[];
};

export type Group = {
  groupLabel?: string;
  menus: Menu[];
};
export function useMenuList() {
  const auth = useAuth();
  const location = useLocation();
  const [dataGroup, setDataGroup] = useState<Group[]>([]);
  useEffect(() => {
    const normalize = (path: string) => path.replace(/\/+$/, ''); // hapus trailing slash
    const currentPath = normalize(location.pathname || '/'); // gunakan useLocation dari react-router

    const data: Group[] = [
      {
        groupLabel: '',
        menus: [
          {
            href: '/',
            label: 'Dashboard',
            icon: LayoutGrid,
            submenus: [],
            active: currentPath === '',
          },
        ],
      },
    ];

    if (auth.checkingPrivilege(['LIST_AGENCY', 'LIST_EMPLOYEE', 'SUBMISSION_LIST', 'LIST_WORK_UNIT'])) {
      const menuList: Menu[] = [];

      if (auth.checkingPrivilege('LIST_EMPLOYEE')) {
        menuList.push({
          href: ROUTES.EMPLOYEE_LIST(),
          label: 'Pegawai',
          icon: Users,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.EMPLOYEE_LIST())),
        });
      }
      if (auth.checkingPrivilege('LIST_AGENCY')) {
        menuList.push({
          href: ROUTES.MASTER_DATA.AGENCY(),
          label: 'Perangkat Daerah',
          icon: Building,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.MASTER_DATA.AGENCY())),
        });
      }

      if (auth.checkingPrivilege('LIST_WORK_UNIT')) {
        menuList.push({
          href: ROUTES.LIST_WORK_UNIT(),
          label: 'Unit Kerja',
          icon: Workflow,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.LIST_WORK_UNIT())),
        });
      }

      if (auth.checkingPrivilege('SUBMISSION_LIST')) {
        menuList.push({
          href: ROUTES.SUBMISSION_LIST(),
          label: 'Pengajuan',
          icon: Paperclip,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.SUBMISSION_LIST())),
        });
      }

      if (auth.checkingPrivilege('BROADCAST_MENU')) {
        menuList.push({
          href: ROUTES.BROADCAST_LIST(),
          label: 'Broadcast WA',
          icon: MdWhatsapp as any,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.BROADCAST_LIST())),
        });
      }

      if (menuList.length > 0) {
        data.push({
          groupLabel: 'Administrasi',
          menus: menuList,
        });
      }
    }

    data.push({
      groupLabel: 'Absensi',
      menus: [
        {
          href: ROUTES.ATTENDANCE_LIST(),
          label: 'Absensi Hari ini',
          icon: Calendar,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.ATTENDANCE_LIST())),
        },
        {
          href: ROUTES.ATTENDANCE_HISTORY(),
          label: 'Riwayat Absensi',
          icon: HistoryIcon,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.ATTENDANCE_HISTORY())),
        },
      ],
    });

    if (auth.user?.role === 'SUPER_ADMIN') {
      data.push({
        groupLabel: 'Pengaturan',
        menus: [
          {
            href: ROUTES.SETTING_PRIVILEGE(),
            label: 'Setting Akses',
            icon: Settings,
            submenus: [],
            active: currentPath.startsWith(normalize(ROUTES.SETTING_PRIVILEGE())),
          },
        ],
      });
    }

    setDataGroup(data);
  }, [auth?.privileges, location.pathname]);

  return {
    dataGroup,
  };
}
