import { ROUTES } from '@/routes/routes.ts';
import { Building, Calendar, HistoryIcon, LayoutGrid, type LucideIcon, Users, Workflow } from 'lucide-react';

type Submenu = {
  href: string;
  label: string;
  active?: boolean;
};

type Menu = {
  href: string;
  label: string;
  active?: boolean;
  icon: LucideIcon;
  submenus?: Submenu[];
};

type Group = {
  groupLabel?: string;
  menus: Menu[];
};

export function getMenuList(pathName: string = ''): Group[] {
  const normalize = (path: string) => path.replace(/\/+$/, ''); // hapus trailing slash
  const currentPath = normalize(pathName || '/');

  return [
    {
      groupLabel: '',
      menus: [
        {
          href: '/',
          label: 'Dashboard',
          icon: LayoutGrid,
          submenus: [],
          active: currentPath === '/',
        },
      ],
    },
    {
      groupLabel: 'Administrasi',
      menus: [
        {
          href: ROUTES.MASTER_DATA.AGENCY(),
          label: 'Instansi',
          icon: Building,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.MASTER_DATA.AGENCY())),
        },
        {
          href: ROUTES.EMPLOYEE_LIST(),
          label: 'Pegawai',
          icon: Users,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.EMPLOYEE_LIST())),
        },
        {
          href: ROUTES.LIST_WORK_UNIT(),
          label: 'Unit Kerja',
          icon: Workflow,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.LIST_WORK_UNIT())),
        },
      ],
    },
    {
      groupLabel: 'Absensi',
      menus: [
        {
          href: ROUTES.ATTENDANCE_LIST(),
          label: 'Absensi Hari ini',
          icon: Calendar,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.ATTENDANCE_LIST())),
        },
        {
          href: ROUTES.ATTENDANCE_HISTORY(),
          label: 'Riwayat Absensi',
          icon: HistoryIcon,
          submenus: [],
          active: currentPath.startsWith(normalize(ROUTES.ATTENDANCE_HISTORY())),
        },
      ],
    },
    // {
    //   groupLabel: undefined,
    //   menus: [
    //     {
    //       href: '',
    //       label: 'Master Data',
    //       icon: LayoutList,
    //       submenus: [
    //         {
    //           href: ROUTES.MASTER_DATA.AGENCY(),
    //           label: 'Instansi',
    //           active: currentPath.startsWith(normalize(ROUTES.MASTER_DATA.AGENCY())),
    //         },
    //       ],
    //       active: currentPath.startsWith('/master-data'), // aktifkan jika anak aktif
    //     },
    //     {
    //       href: '/categories',
    //       label: 'Categories',
    //       icon: Bookmark,
    //       active: currentPath.startsWith('/categories'),
    //     },
    //     {
    //       href: '/tags',
    //       label: 'Tags',
    //       icon: Tag,
    //       active: currentPath.startsWith('/tags'),
    //     },
    //   ],
    // },
    // {
    //   groupLabel: 'Settings',
    //   menus: [
    //     {
    //       href: '/users',
    //       label: 'Users',
    //       icon: Users,
    //       active: currentPath.startsWith('/users'),
    //     },
    //     {
    //       href: '/account',
    //       label: 'Account',
    //       icon: Settings,
    //       active: currentPath.startsWith('/account'),
    //     },
    //   ],
    // },
  ];
}
