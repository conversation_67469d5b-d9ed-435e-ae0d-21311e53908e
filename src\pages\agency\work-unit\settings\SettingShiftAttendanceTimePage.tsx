import IconContainer from '@/components/IconContainer';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Card, CardContent } from '@/components/ui/card';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { useNavigate, useParams } from 'react-router-dom';
import { useSettingShiftAttendanceTimePage } from '@/pages/agency/work-unit/settings/useSettingShiftAttendanceTimePage.ts';
import { FieldArray, FormikProvider } from 'formik';
import { Separator } from '@/components/ui/separator.tsx';
import InputText from '@/components/InputText.tsx';
import InputTime from '@/components/InputTime.tsx';
import { Fragment } from 'react';
import { Button } from '@/components/ui/button.tsx';
import { CircleChevronLeft, Plus, Send, Trash } from 'lucide-react';
import ConfirmationCheckBox from '@/components/ConfirmationCheckbox.tsx';

export default function SettingShiftAttendanceTimePage() {
  const page = useSettingShiftAttendanceTimePage();
  const navigate = useNavigate();
  const { id } = useParams();
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Unit Kerja',
      path: ROUTES.LIST_WORK_UNIT(),
    },
    {
      label: 'Detail',
      path: ROUTES.DETAIL_WORK_UNIT(id || ''),
    },
    {
      label: 'Kelola Waktu Absensi',
    },
  ];
  return (
    <PageContainer loading={page.queryShift.isPending || page.queryLisTimeTable.isPending}>
      <PageTitle title="Kelola Waktu Absensi" breadcrumb={breadcrumbs} />
      <Card>
        <CardContent>
          <div className="flex gap-3 items-center">
            <IconContainer icon="IdCardLanyardIcon" variant="pink" />
            <div>
              <div className="font-bold">Kelola waktu absensi</div>
              <p className="text-muted-foreground text-sm">
                Atur waktu absensi untuk setiap shift mulai dari jam masuk sampai jam keluar atau yang lainnya jika
                diperlukan
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className={'grid gap-3'}>
        <FormikProvider value={page.formik}>
          {page.formik.values.data.map((item, i) => (
            <Card key={i}>
              <CardContent>
                <div className={'flex items-center gap-3'}>
                  {item.code && (
                    <div
                      className={'h-8 w-fit px-4 flex items-center justify-center text-white bg-primary rounded-full'}
                    >
                      {item.code}
                    </div>
                  )}
                  <div>
                    <div className={'font-semibold'}>{item.name}</div>
                    <p>{`${item.start_time} - ${item.end_time}`}</p>
                  </div>
                </div>
              </CardContent>
              <Separator />
              <CardContent>
                <div className={'grid gap-2'}>
                  <FieldArray name={`data[${i}].data`}>
                    {({ push }) => (
                      <Fragment>
                        {item.data.map((_, j) => (
                          <div key={j} className={'grid gap-3 bg-muted p-4 rounded-md'}>
                            <InputText
                              name={`data[${i}].data[${j}].name`}
                              label={'Nama Absensi'}
                              placeholder={'Masukan nama absensi (absen masuk) '}
                              required
                              endIcon={
                                item.data.length > 1 && (
                                  <Fragment>
                                    {page.loadingDelete === `${i.toString()}${j.toString()}` ? (
                                      <div>Loading...</div>
                                    ) : (
                                      <Button
                                        variant={'link'}
                                        className={'text-red-800'}
                                        onClick={() => page.onRemove(i, j)}
                                      >
                                        <Trash /> Hapus
                                      </Button>
                                    )}
                                  </Fragment>
                                )
                              }
                            />
                            <div className={'grid grid-cols-2 gap-3'}>
                              <InputTime
                                name={`data[${i}].data[${j}].start_time`}
                                label={'Jam masuk'}
                                placeholder={'Masukan jam masuk'}
                                required
                              />
                              <InputTime
                                name={`data[${i}].data[${j}].end_time`}
                                label={'Batas waktu toleransi'}
                                placeholder={'Masukan Batas waktu toleransi'}
                                required
                              />
                            </div>
                          </div>
                        ))}
                        <div className={'flex justify-end'}>
                          <Button
                            variant={'link'}
                            onClick={() => {
                              push({
                                name: '',
                                start_time: '',
                                end_time: '',
                              });
                            }}
                          >
                            <Plus />
                            Tambah
                          </Button>
                        </div>
                      </Fragment>
                    )}
                  </FieldArray>
                </div>
              </CardContent>
            </Card>
          ))}
        </FormikProvider>
      </div>
      <Card>
        <CardContent className={'flex justify-between gap-2 items-center'}>
          <ConfirmationCheckBox
            label={'Konfirmasi'}
            checked={page.formik.values.checked}
            description={'Konfirmasi untuk memastikan anda mengelolah waktu absensi'}
            onCheckedChange={(e) => page.formik.setFieldValue('checked', e)}
          />
          <div className={'flex items-center gap-2'}>
            <Button variant={'outline'} onClick={() => navigate(-1)}>
              <CircleChevronLeft /> Batal
            </Button>
            <Button
              loading={page.mutationSubmit.isPending}
              disabled={!page.formik.values.checked}
              onClick={() => page.formik.handleSubmit()}
            >
              <Send /> Kirim
            </Button>
          </div>
        </CardContent>
      </Card>
    </PageContainer>
  );
}
