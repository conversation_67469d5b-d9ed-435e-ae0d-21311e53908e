import { useUi } from '@/hooks/useUi';
import { MasterDataRepository } from '@/repositories/master-data-repository';
import type { IReqSettingRoleAccess } from '@/types/request/IReqSettingRoleAccess';
import type { IResListAllRole } from '@/types/response/IResListAllRole';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export function useSettingPrivilegePage() {
  const masterDataRepository = new MasterDataRepository();
  const { toast } = useUi();
  const [selectedRole, setSelectedRole] = useState<IResListAllRole | undefined>();
  const [selectedPrivileges, setSelectedPrivilege] = useState<string[]>([]);
  const [checked, setChecked] = useState(false);

  const queryAllRole = useQuery({
    queryKey: ['all_role'],
    queryFn: async () => await masterDataRepository.listAllRole(),
  });

  const queryAllPrivileges = useQuery({
    queryKey: ['all_privileges'],
    queryFn: async () => await masterDataRepository.listAllPrivileges(),
  });

  const queryRoleAccess = useQuery({
    queryKey: ['all_role_access'],
    queryFn: async () => await masterDataRepository.getRoleAccess(),
  });

  const mutationSubmit = useMutation({
    mutationFn: async () => {
      const data: IReqSettingRoleAccess = {
        role: selectedRole?.role_enum || '',
        privileges: selectedPrivileges,
      };
      await masterDataRepository.setRoleAccess(data);
      queryRoleAccess.refetch().then(() => {
        toast.success('Akses role berhasil di perbaharui');
        setChecked(false);
      });
    },
  });

  function onClickRole(e: IResListAllRole) {
    setChecked(false);
    setSelectedRole(e);
  }

  function onChangePrivilege(e: string) {
    setSelectedPrivilege((prev) => {
      if (prev.includes(e)) {
        return prev.filter((item) => item !== e);
      } else {
        return [...prev, e];
      }
    });
  }

  const dataRole = queryAllRole.data || [];
  const dataPrivileges = queryAllPrivileges.data || [];
  const dataRoleAccess = queryRoleAccess.data || [];

  useEffect(() => {
    if (dataRole?.[0]) {
      setSelectedRole(dataRole[0]);
    }
  }, [dataRole]);

  useEffect(() => {
    if (dataRoleAccess && selectedRole?.role_enum) {
      const matchedRole = dataRoleAccess.find((v) => v.role_enum === selectedRole.role_enum);

      if (matchedRole?.privileges_list && matchedRole.privileges_list.length > 0) {
        setSelectedPrivilege(matchedRole.privileges_list.map((e) => e.privilege_enum));
      } else {
        setSelectedPrivilege([]);
      }
    }
  }, [dataRoleAccess, selectedRole]);

  const loading = queryAllPrivileges.isPending || queryRoleAccess.isPending || queryAllRole.isPending;

  return {
    dataRole,
    dataPrivileges,
    selectedRole,
    onClickRole,
    dataRoleAccess,
    selectedPrivileges,
    checked,
    setChecked,
    onChangePrivilege,
    loading,
    mutationSubmit,
  };
}
