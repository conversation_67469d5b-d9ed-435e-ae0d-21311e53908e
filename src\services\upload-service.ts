import { ENV } from '@/constants/env.ts';
import { supabase } from '@/configs/supabse.config.ts';
import { useUi } from '@/hooks/useUi.ts';

export class UploadService {
  private ui = useUi();
  public async uploadMultiple(files: File[] | FileList, folder: string): Promise<string[]> {
    const uploadedUrls: string[] = [];

    for (const file of files) {
      try {
        const filePath = `${folder}/${Date.now()}_${file.name}`;

        const { error } = await supabase.storage.from(ENV.SUPABASE_BUCKET_NAME).upload(filePath, file, {
          contentType: file.type || 'application/octet-stream',
        });

        if (error) {
          this.ui.toast.error(`Upload failed: ${error.message}`);
          continue;
        }

        const url = `${ENV.SUPABASE_URL}/storage/v1/object/public/${ENV.SUPABASE_BUCKET_NAME}/${filePath}`;
        uploadedUrls.push(url);
      } catch (err) {
        this.ui.toast.error('Unexpected error during upload');
        console.error(err);
      }
    }

    return uploadedUrls;
  }
  public async upload(file: File, folder: string): Promise<string | undefined> {
    try {
      const filePath = `${folder}/${Date.now()}_${file.name}`;

      const { error } = await supabase.storage.from(ENV.SUPABASE_BUCKET_NAME).upload(filePath, file, {
        contentType: file.type || 'application/octet-stream',
      });

      if (error) {
        this.ui.toast.error(`Upload failed: ${error.message}`);
        return;
      }

      return `${ENV.SUPABASE_URL}/storage/v1/object/public/${ENV.SUPABASE_BUCKET_NAME}/${filePath}`;
    } catch (err) {
      this.ui.toast.error('Unexpected error during upload');
      console.error(err);
    }
  }

  public async uploadBlob(file: Blob, folder: string): Promise<string | undefined> {
    try {
      const fileName = file instanceof File && file.name ? file.name : 'upload.png';
      const filePath = `${folder}/${Date.now()}_${fileName}`;

      const { error } = await supabase.storage.from(ENV.SUPABASE_BUCKET_NAME).upload(filePath, file, {
        contentType: file.type || 'application/octet-stream',
      });

      if (error) {
        this.ui.toast.error(`Upload failed: ${error.message}`);
        return;
      }

      return `${ENV.SUPABASE_URL}/storage/v1/object/public/${ENV.SUPABASE_BUCKET_NAME}/${filePath}`;
    } catch (err) {
      this.ui.toast.error('Unexpected error during upload');
      console.error(err);
    }
  }
}
