import InputAutocomplete from '@/components/InputAutocomplete.tsx';
import { AreaRepository } from '@/repositories/area-repository.ts';
import type { ILabelValue } from '@/types/type/ILabelValue.ts';
import { useQuery } from '@tanstack/react-query';
import { Map } from 'lucide-react';
import { useEffect, useRef } from 'react';
import InputTextArea from './InputTextArea';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Separator } from './ui/separator';

export default function AddressModule(props: IProps) {
  const areaRepository = new AreaRepository();
  const isInitialMount = useRef(true);
  const prevProvinceId = useRef(props?.formik?.values?.province_id);
  const prevCityId = useRef(props?.formik?.values?.city_id);
  const prevDistrictId = useRef(props?.formik?.values?.district_id);
  const queryProvince = useQuery({
    queryKey: ['province_list'],
    queryFn: async () => await areaRepository.getProvince(),
  });

  const queryCity = useQuery({
    queryKey: ['city_list', props?.formik?.values?.province_id],
    queryFn: async () => await areaRepository.getCity(props?.formik?.values?.province_id || ''),
    enabled: !!props?.formik?.values?.province_id,
  });

  const queryDistrict = useQuery({
    queryKey: ['district_list', props?.formik?.values?.city_id],
    queryFn: async () => await areaRepository.getDistrict(props?.formik?.values?.city_id || ''),
    enabled: !!props?.formik?.values?.city_id,
  });

  const querySubDistrict = useQuery({
    queryKey: ['sub_district_list', props?.formik?.values?.district_id],
    queryFn: async () => await areaRepository.getSubDistrict(props?.formik?.values?.district_id || ''),
    enabled: !!props?.formik?.values?.district_id,
  });

  // Reset hierarchy when province changes
  useEffect(() => {
    if (!isInitialMount.current && props?.formik?.values?.province_id !== prevProvinceId.current) {
      // Reset city, district, and sub_district when province changes
      props.formik.setFieldValue('city_id', '');
      props.formik.setFieldValue('district_id', '');
      props.formik.setFieldValue('sub_district_id', '');
    }
    prevProvinceId.current = props?.formik?.values?.province_id;
  }, [props?.formik?.values?.province_id]);

  // Reset hierarchy when city changes
  useEffect(() => {
    if (!isInitialMount.current && props?.formik?.values?.city_id !== prevCityId.current) {
      // Reset district and sub_district when city changes
      props.formik.setFieldValue('district_id', '');
      props.formik.setFieldValue('sub_district_id', '');
    }
    prevCityId.current = props?.formik?.values?.city_id;
  }, [props?.formik?.values?.city_id]);

  // Reset hierarchy when district changes
  useEffect(() => {
    if (!isInitialMount.current && props?.formik?.values?.district_id !== prevDistrictId.current) {
      props.formik.setFieldValue('sub_district_id', '');
    }
    prevDistrictId.current = props?.formik?.values?.district_id;
  }, [props?.formik?.values?.district_id]);

  useEffect(() => {
    isInitialMount.current = false;
  }, []);

  const listProvince: ILabelValue<string>[] = (queryProvince?.data?.response_data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });

  const listCity: ILabelValue<string>[] = (queryCity?.data?.response_data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });

  const listDistrict: ILabelValue<string>[] = (queryDistrict?.data?.response_data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });

  const listSubDistrict: ILabelValue<string>[] = (querySubDistrict?.data?.response_data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <Map className="w-5 h-5 text-pink-600" />
          </div>
          <div>
            <CardTitle className="text-lg">Alamat</CardTitle>
            <p className="text-sm text-gray-600 mt-1">Masukan alamat domisili</p>
          </div>
        </div>
      </CardHeader>
      <Separator />
      <CardContent>
        <div className="grid gap-3">
          <InputAutocomplete
            required
            label="Provinsi"
            alignment={'horizontal'}
            name="province_id"
            id="province_id"
            placeholder="Pilih provinsi"
            options={listProvince}
          />
          <InputAutocomplete
            required
            label="Kabupaten/Kota"
            alignment={'horizontal'}
            name="city_id"
            id="city_id"
            placeholder="Pilih Kabupaten/kota"
            options={listCity}
            disabled={!props?.formik?.values?.province_id}
          />
          <InputAutocomplete
            required
            label="Kecamatan"
            alignment={'horizontal'}
            name="district_id"
            id="district_id"
            placeholder="Pilih kecamatan"
            options={listDistrict}
            disabled={!props?.formik?.values?.city_id}
          />
          <InputAutocomplete
            required
            label="Kelurahan/desa"
            alignment={'horizontal'}
            name="sub_district_id"
            id="sub_district_id"
            placeholder="Pilih Kelurahan/desa"
            options={listSubDistrict}
            disabled={!props?.formik?.values?.district_id}
          />
          <InputTextArea
            required
            label="Alamat Lengkap"
            name="address"
            id="address"
            placeholder="Masukan alamat lengkap"
            alignment={'horizontal'}
          />
        </div>
      </CardContent>
    </Card>
  );
}

interface IProps {
  formik?: any;
}
