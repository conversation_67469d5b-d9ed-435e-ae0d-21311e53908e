import type { BroadcastStatusTypeEnum } from '@/types/type/BroadcastStatusTypeEnum.ts';

export interface IResDetailBroadcast {
  id: string;
  title: string;
  body: string;
  status_enum: BroadcastStatusTypeEnum;
  status_string: string;
  coverage_type_enum: string;
  coverage_type_string: string;
  created_date: string;
  created_by: IBroadcastUser;
  coverages: {
    name: '';
    id: '';
  }[];
  approve_and_reject_data: IBroadcastActionData;
  send_by: IBroadcastActionData;
}

export interface IBroadcastUser {
  name: string;
  id: string;
  agency_name: string;
  agency_id: string;
  work_unit_name: string;
  work_unit_id: string;
  profile_picture: string;
}

export interface IBroadcastActionData extends IBroadcastUser {
  reason?: string | null;
  date: string;
}
