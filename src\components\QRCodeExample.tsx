import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QrCode, Smartphone } from 'lucide-react';
import useSignInPage from '@/pages/auth/useSignInPage';
import QRCodeWithTimer from './QRCodeWithTimer';

export default function QRCodeExample() {
  const {
    loginType,
    onClickQr,
    qrValue,
    count,
    isQrActive,
    formatCountdown,
    stopQrTimer,
    generateNewQrCode,
  } = useSignInPage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-gray-800">QR Code Login Demo</h1>
          <p className="text-gray-600">
            QR Code yang berubah otomatis setiap 1 menit dengan countdown timer
          </p>
        </div>

        {/* Main Content */}
        <Card className="shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              QR Code Login System
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {!isQrActive ? (
              <div className="text-center space-y-4">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                  <QrCode className="h-12 w-12 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Mulai QR Code Login
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Klik tombol di bawah untuk memulai QR code login dengan timer otomatis
                  </p>
                  <Button
                    onClick={onClickQr}
                    className="bg-blue-600 hover:bg-blue-700"
                    size="lg"
                  >
                    <Smartphone className="mr-2 h-5 w-5" />
                    Mulai QR Code Login
                  </Button>
                </div>
              </div>
            ) : (
              qrValue && (
                <QRCodeWithTimer
                  qrValue={qrValue}
                  count={count}
                  formatCountdown={formatCountdown}
                  onRefresh={generateNewQrCode}
                  onStop={stopQrTimer}
                />
              )
            )}

            {/* Features Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-3">Fitur QR Code Timer:</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  QR Code berubah otomatis setiap 1 menit
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Countdown timer real-time dengan progress bar
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  Visual feedback dengan color coding
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  Manual refresh dan stop timer tersedia
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Technical Info */}
        {isQrActive && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status Teknis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">QR Code ID:</span>
                  <code className="block bg-gray-100 p-2 rounded mt-1 text-xs">
                    {qrValue.substring(0, 20)}...
                  </code>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <span className="block text-green-600 font-medium mt-1">
                    {isQrActive ? 'Aktif' : 'Tidak Aktif'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Countdown:</span>
                  <span className="block font-mono font-bold mt-1">
                    {formatCountdown(count)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Progress:</span>
                  <span className="block mt-1">
                    {Math.round((count / 60) * 100)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
