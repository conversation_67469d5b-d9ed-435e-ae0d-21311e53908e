import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format, parse } from 'date-fns';
import { ENV } from '@/constants/env';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const isProd = ENV.NODE_ENV === 'PRODUCTION'

export const getInitials = (name?: string) => {
  if (!name || typeof name !== 'string') return '';

  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

export function normalizeTime(value: string): string {
  try {
    // Coba parse dengan format HH:mm:ss
    const parsed = parse(value, 'HH:mm:ss', new Date());
    return format(parsed, 'HH:mm');
  } catch {
    try {
      // Coba parse dengan format HH:mm
      const parsed = parse(value, 'HH:mm', new Date());
      return format(parsed, 'HH:mm');
    } catch {
      return ''; // fallback untuk invalid input
    }
  }
}

export function formatTimeGapToInitials(timeGapSeconds: number): string {
  if (timeGapSeconds === 0 || timeGapSeconds === null || timeGapSeconds === undefined) return '-';

  const isEarly = timeGapSeconds < 0;
  const absSeconds = Math.abs(timeGapSeconds);

  const minutes = Math.floor(absSeconds / 60);
  const hours = Math.floor(minutes / 60);
  const seconds = absSeconds % 60;

  let result: string;
  if (minutes >= 60) {
    result = `${hours} Jam`;
  } else if (minutes > 0) {
    result = `${minutes} Menit`;
  } else {
    result = `${seconds} Detik`;
  }

  return isEarly ? `lebih awal ${result}` : result;
}

export function generateUniqueCode(length: number = 10): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
}
