import { Card, CardContent } from '@/components/ui/card';
import { EmployeeRepository } from '@/repositories/employee-repostiory';
import type { IResEmplloyeSummary } from '@/types/response/IResEmployeeSummary';
import type { EmployeeStatusType } from '@/types/type/EmployeeStatusType';
import { useQuery } from '@tanstack/react-query';
import {
  AlertCircle,
  Building,
  Camera,
  CheckCircle,
  Clock,
  FileText,
  MapPin,
  Phone,
  Users,
  UserX,
  XCircle,
} from 'lucide-react';

// Status icon mapping
const getStatusIcon = (status: EmployeeStatusType) => {
  const iconMap = {
    WAITING_PHONE_VERIFICATION: Phone,
    WAITING_FACE_REGISTRATION: Camera,
    WAITING_OTHER_DATA: FileText,
    PENDING: Clock,
    ACTIVE: CheckCircle,
    INACTIVE: UserX,
    REJECT: XCircle,
    WAITING_REGISTRATION_DATA: FileText,
    WAITING_WORK_UNIT_DATA: Building,
    WAITING_ADDRESS_DATA: MapPin,
  };
  return iconMap[status] || Users;
};

// Status color mapping
const getStatusColor = (status: EmployeeStatusType) => {
  const colorMap = {
    WAITING_PHONE_VERIFICATION: 'bg-blue-50 border-blue-200 text-blue-700',
    WAITING_FACE_REGISTRATION: 'bg-purple-50 border-purple-200 text-purple-700',
    WAITING_OTHER_DATA: 'bg-amber-50 border-amber-200 text-amber-700',
    PENDING: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    ACTIVE: 'bg-green-50 border-green-200 text-green-700',
    INACTIVE: 'bg-gray-50 border-gray-200 text-gray-700',
    REJECT: 'bg-red-50 border-red-200 text-red-700',
    WAITING_REGISTRATION_DATA: 'bg-orange-50 border-orange-200 text-orange-700',
    WAITING_WORK_UNIT_DATA: 'bg-indigo-50 border-indigo-200 text-indigo-700',
    WAITING_ADDRESS_DATA: 'bg-teal-50 border-teal-200 text-teal-700',
  };
  return colorMap[status] || 'bg-gray-50 border-gray-200 text-gray-700';
};

const SummaryEmployeeStatusCard = () => {
  const employeeRepository = new EmployeeRepository();

  const querySummary = useQuery({
    queryKey: ['list_employee_summar'],
    queryFn: async () => await employeeRepository.getEmployeeSummary(),
  });

  const totalEmployees = querySummary?.data?.reduce((sum, item) => sum + item.amount, 0) || 0;
  const chunkArray = (arr: any, size: number) => {
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
      result.push(arr.slice(i, i + size));
    }
    return result;
  };
  return (
    <>
      <Card>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Total Pegawai</h2>
                <p className="text-gray-500">Status keseluruhan pegawai</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">{totalEmployees}</div>
              <div className="text-sm text-gray-500">Pewagai</div>
            </div>
          </div>

          {querySummary?.data && (
            <div className="flex flex-col gap-3">
              {chunkArray(querySummary.data, 3).map((row, rowIndex) => (
                <div
                  key={rowIndex}
                  className={`grid gap-3 grid-cols-${row.length}`} // grid-cols-1 / 2 / 3
                >
                  {row.map((item: IResEmplloyeSummary) => {
                    const IconComponent = getStatusIcon(item.status_enum);
                    const colorClass = getStatusColor(item.status_enum);

                    return (
                      <div
                        key={item.status_enum}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md cursor-pointer ${colorClass}`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="p-2 bg-white rounded-lg shadow-sm">
                            <IconComponent className="h-5 w-5" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold">{item.amount}</div>
                            <div className="text-xs opacity-75">Pegawai</div>
                          </div>
                        </div>
                        <div className="font-medium text-sm leading-tight">{item.status_string}</div>
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-600">Pegawai Aktif</div>
                <div className="text-2xl font-bold text-green-600">
                  {querySummary?.data?.find((item) => item.status_enum === 'ACTIVE')?.amount || 0}
                </div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-600">Menunggu Verifikasi</div>
                <div className="text-2xl font-bold text-yellow-600">
                  {querySummary?.data
                    ?.filter((item) => item.status_enum.startsWith('WAITING_') || item.status_enum === 'PENDING')
                    .reduce((sum, item) => sum + item.amount, 0) || 0}
                </div>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-600">Tidak Aktif/Ditolak</div>
                <div className="text-2xl font-bold text-red-600">
                  {querySummary?.data
                    ?.filter((item) => item.status_enum === 'INACTIVE' || item.status_enum === 'REJECT')
                    .reduce((sum, item) => sum + item.amount, 0) || 0}
                </div>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default SummaryEmployeeStatusCard;
