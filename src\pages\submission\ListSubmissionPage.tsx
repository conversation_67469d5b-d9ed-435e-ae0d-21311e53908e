import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import AppTable, { type ITableColumn } from '@/components/AppTable.tsx';
import type { IResListSubmission } from '@/types/response/IResListSubmission.ts';
import { useListSubmissionPage } from '@/pages/submission/useListSubmissionPage.ts';
import DateHelper from '@/lib/date-helper.ts';
import SubmissionStatusText from '@/components/SubmissionStatusText.tsx';
import { Building2, Info, Workflow } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ROUTES } from '@/routes/routes.ts';
import { Button } from '@/components/ui/button.tsx';
import AppPagination from '@/components/AppPagination.tsx';
import DialogDetail from '@/pages/submission/DialogDetailSubmission.tsx';
import InputSearch from '@/components/InputSearch';
import FilterList from '@/components/FilterList';
import InputSelect from '@/components/InputSelect';
import { useAuth } from '@/hooks/use-auth';

export default function ListSubmissionPage() {
  const page = useListSubmissionPage();
  const auth = useAuth();

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Pengajuan',
    },
  ];

  const tableColumn: ITableColumn<IResListSubmission>[] = [
    {
      headerTitle: 'Diajukan Oleh',
      component: (data) => (
        <Link to={ROUTES.DETAIL_EMPLOYEE(data.account_id)} className={'flex items-center gap-2'}>
          <img
            src={data.account_profile_picture}
            alt={data.account_name}
            className={'h-8 w-8 border border-black rounded-full'}
          />
          <div className={'font-semibold hover:underline whitespace-normal'}>{data.account_name}</div>
        </Link>
      ),
    },
    {
      headerTitle: 'Tipe Pengajuan',
      component: (data) => <div>{data.type_string}</div>,
    },
    {
      headerTitle: 'Unit Kerja',
      component: (data) => (
        <div>
          <Link to={ROUTES.DETAIL_WORK_UNIT(data.work_unit_id)} className={'flex gap-1 items-center hover:underline'}>
            <Workflow size={12} />
            {data?.work_unit_name}
          </Link>
          <Link to={ROUTES.DETAIL_AGENCY(data.agency_id)} className={'flex gap-1 items-center hover:underline'}>
            <Building2 size={12} />
            <div>{data?.agency_name}</div>
          </Link>
        </div>
      ),
    },
    {
      headerTitle: 'Tanggal Dibuat',
      component: (data) => <div>{DateHelper.toFormatDate(data.created_date, 'dd LLLL, yyyy - HH:mm')}</div>,
    },
    {
      headerTitle: 'Status',
      component: (data) => <SubmissionStatusText string={data.status_string} enum={data.status} />,
    },
    {
      headerTitle: '',
      component: (data) => (
        <Link to={ROUTES.DETAIL_SUBMISSION(data.id)}>
          <Button size={'icon'} variant={'outline'}>
            <Info />
          </Button>
        </Link>
      ),
    },
  ];

  return (
    <>
      <DialogDetail
        page={{
          dataDetail: page.dataDetail,
          onCloseSubmission: page.onCloseSubmission,
        }}
        onApprove={page.onApproveSubmission}
        onReject={page.onRejectSubmission}
        loading={page.loadingApproveReject}
      />
      <PageContainer>
        <PageTitle title="Pengajuan" breadcrumb={breadcrumb} />
        <div className="flex gap-2">
          <div className="flex-1">
            <InputSearch
              placeholder="Cari nama"
              searchValue={page.searchValue}
              setSearchValue={page.setSearchValue}
              handleSearch={page.handleSearch}
              handleReset={page.handleResetSearch}
              active={!!page?.filter?.q}
            />
          </div>
          <FilterList
            open={page.openFilter}
            onOpenChange={page.setOpenFilter}
            onSubmit={() => page.submitFilter()}
            onReset={page.handleResetFilter}
          >
            <div>
              <InputSelect
                disabled={auth?.user?.role !== 'SUPER_ADMIN'}
                label="Perangkat daerah"
                placeholder="Pilih Perangkat daerah"
                disableFormik
                options={page.dataAgency}
                onValueChange={page.onChangeAgency}
                name="filter_agency"
                id="filter_agency"
                value={page.filter.agency_id}
              />
              <InputSelect
                label="Role"
                placeholder="Pilih Role User"
                disableFormik
                options={page.dataRole}
                onValueChange={(e) => page.onChangeFilterRole(e)}
                name="filter_role"
                id="filter_role"
                value={page?.filter?.role || 'all'}
              />
            </div>
          </FilterList>
        </div>
        <div>
          <AppTable
            column={tableColumn}
            data={page?.queryList?.data?.response_data || []}
            loading={page.queryList?.isPending}
          />
          {page?.queryList?.data?.paginated_data && (
            <AppPagination
              onPaginationChange={(params) => page.onChangePagination(params)}
              dataPagination={page?.queryList?.data?.paginated_data}
            />
          )}
        </div>
      </PageContainer>
    </>
  );
}
