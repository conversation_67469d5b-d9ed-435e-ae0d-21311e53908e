import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import { EmployeeRepository } from '@/repositories/employee-repostiory.ts';
import { useEffect, useState } from 'react';
import type { IResListEmployee } from '@/types/response/IResListEmployee.ts';
import { useUi } from '@/hooks/useUi.ts';

export function useAddWorkUnitManager() {
  const { id } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const employeeRepository = new EmployeeRepository();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [selectedData, setSelectedData] = useState<string[]>([]);
  const { toast } = useUi();

  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', id],
    queryFn: () => workUnitRepository.getDetailWorkUnit(id!),
    enabled: !!id,
  });

  const queryEmployee = useQuery({
    queryKey: ['employee_list', id],
    queryFn: () => employeeRepository.getListAllAgencyId(queryDetail?.data?.agency_id || ''),
    enabled: !!queryDetail?.data?.agency_id,
  });

  const mutationSubmit = useMutation({
    mutationFn: (e: any) =>
      workUnitRepository.sendRequestAddManager(e).then(() => {
        toast.success('Atasan telah berhasil ditambahkan');
        navigate(-1);
      }),
  });

  useEffect(() => {
    if (queryDetail.data?.agency_id) {
      queryClient
        .invalidateQueries({
          queryKey: ['employee_list', queryDetail.data.agency_id],
        })
        .then();
    }
  }, [queryDetail.data?.agency_id, queryClient]);

  function onChangeDataSelected(e: IResListEmployee) {
    setSelectedData((prev) => {
      if (prev.includes(e.account_id)) {
        return prev.filter((item) => item !== e.account_id);
      } else {
        return [...prev, e.account_id];
      }
    });
  }

  function onCheckCheckbox(e: IResListEmployee): boolean {
    return selectedData.includes(e.account_id);
  }

  const detail = queryDetail?.data;
  const dataEmployee = queryEmployee?.data?.response_data || [];

  function onSubmitManager() {
    const data = {
      work_unit_id: detail?.id,
      account_ids: selectedData,
    }; 
    mutationSubmit.mutate(data);
  } 
 
  useEffect(() => {
    if(detail?.managers){
      setSelectedData(detail.managers.map(e => e.id))
    }
  },[detail?.managers])

  return {
    detail,
    dataEmployee,
    queryEmployee,
    queryDetail,
    onChangeDataSelected,
    onCheckCheckbox,
    selectedData,
    onSubmitManager,
    mutationSubmit,
  };
}
