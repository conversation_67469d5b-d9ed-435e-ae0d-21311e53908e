import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import { useState } from 'react';

export function useDetailWorkUnitPage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const [agencyId, setAgencyId] = useState<string>('');
  const { workUnitId } = useParams();
  const queryAgency = useQuery({
    queryKey: ['detailAgency', agencyId],
    queryFn: async () => {
      try {
        const res = await httpService.GET(ENDPOINT.DETAIL_AGENCY(agencyId!));
        return res.data.response_data;
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
    enabled: !!agencyId,
  });

  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', workUnitId],
    queryFn: async () => {
      try {
        const res = await httpService.GET(ENDPOINT.DETAIL_WORK_UNIT(workUnitId!));
        setAgencyId(res.data.response_data.agency_id);
        return res.data.response_data;
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
    enabled: !!workUnitId,
  });

  return { queryAgency, workUnitId, agencyId, queryDetail };
}
