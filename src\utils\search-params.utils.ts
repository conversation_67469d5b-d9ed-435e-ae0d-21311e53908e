import type { IFilterList } from '@/types/type/IFilterList';

export function buildSearchParams(filterData: IFilterList) {
  const params = new URLSearchParams();
  if (filterData.agency_id) params.set('agency_id', filterData.agency_id);
  if (filterData.q) params.set('q', filterData.q);
  if (filterData?.page) params.set('page', filterData.page.toString());
  if (filterData?.size) params.set('size', filterData.size.toString());
  if (filterData?.start_date) params.set('start_date', filterData.start_date.toString());
  if (filterData?.end_date) params.set('end_date', filterData.end_date.toString());
  return params;
}

export function buildQueryString(params: URLSearchParams) {
  return '?' + params.toString();
}
