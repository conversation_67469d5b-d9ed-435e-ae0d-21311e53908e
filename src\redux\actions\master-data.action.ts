import { MasterDataSlice } from '@/redux/reducers/master-data.reducers.ts';
import type { ILabelValue } from '@/types/type/ILabelValue.ts';
import type { Dispatch } from '@reduxjs/toolkit';

export class MasterDataAction {
  private action = MasterDataSlice.actions;

  listReligion(data: ILabelValue<string>[]) {
    return (dispatch: Dispatch) => {
      dispatch(this.action.listReligion(data));
    };
  }

  listMaritalStatus(data: ILabelValue<string>[]) {
    return (dispatch: Dispatch) => {
      dispatch(this.action.listMaritalStatus(data));
    };
  }
}
