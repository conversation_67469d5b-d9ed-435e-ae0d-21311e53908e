import { initializeApp } from 'firebase/app';

import { getMessaging, onMessage } from 'firebase/messaging';
const firebaseConfig = {
  apiKey: 'AIzaSyCo2CHk6A5BQj4ejQiGv3aGt0fx0T_6Hh4',
  authDomain: 'nuca-lale.firebaseapp.com',
  projectId: 'nuca-lale',
  storageBucket: 'nuca-lale.firebasestorage.app',
  messagingSenderId: '25176508765',
  appId: '1:25176508765:web:bfff8105d3c5175ddd00b4',
  measurementId: 'G-PB952T5BP5',
};
const app = initializeApp(firebaseConfig);

export const messaging = getMessaging(app);

export const onMessageListener = () =>
  new Promise((resolve, reject) => {
    try {
      onMessage(messaging, (payload) => {
        resolve(payload as any);
      });
    } catch (err) {
      reject(err as any);
    }
  });
