import { useUi } from '@/hooks/useUi.ts';
import { normalizeTime } from '@/lib/utils';
import { mockDataCreateWorkUnitRequest } from '@/mocks/mock-data-schedule.ts';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import type { IReqScheduleRegular } from '@/types/request/IReqScheduleRegular.ts';
import type {
  IReqSettingScheduleRegularWorkUnit,
  ITimeTable,
} from '@/types/request/IReqSettingScheduleRegularWorkUnit.ts';
import type { IResScheduleRegularWorkUnit } from '@/types/response/IResScheduleRegularWorkUnit.ts';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useNavigate, useParams } from 'react-router-dom';

export function useSettingScheduleRegularWorkUnitPage() {
  const { id } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const { toast } = useUi();
  const navigate = useNavigate();

  const initValue: IReqSettingScheduleRegularWorkUnit = mockDataCreateWorkUnitRequest;

  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', id],
    queryFn: () => workUnitRepository.getDetailWorkUnit(id || ''),
  });

  const querySchedule = useQuery({
    queryKey: ['work_unit_schedule_regular', id],
    queryFn: () =>
      workUnitRepository.getTimeTableSchedule(id || '').then((res) => {
        mappingDataResponse(res);
        return res;
      }),
  });

  const mutationSettingSchedule = useMutation({
    mutationFn: async (e: IReqScheduleRegular[]) => {
      await workUnitRepository.settingScheduleRegular(id, e).then(() => {
        queryDetail.refetch().then();
        toast.success('Jadwal kerja berhasil diubah');
        navigate(-1);
      });
    },
  });

  const formik = useFormik({
    initialValues: { ...initValue, checked: false },
    onSubmit: (values) => {
      const data: IReqScheduleRegular[] = [];

      for (const day of values.day) {
        for (const item of day.data) {
          data.push({
            ...(item.id && { id: item.id }),
            day: day.index,
            name: item.name,
            description: `${item.name} kerja hari ${day.label}`,
            start_time: normalizeTime(item.start_time),
            end_time: normalizeTime(item.end_time),
            is_holiday: day?.is_holiday || false,
          });
        }
      }

      mutationSettingSchedule.mutate(data);
    },
  });

  function mappingDataResponse(rawResponse: IResScheduleRegularWorkUnit[]) {
    const dayLabels: Record<number, string> = {
      1: 'Senin',
      2: 'Selasa',
      3: 'Rabu',
      4: 'Kamis',
      5: 'Jumat',
      6: 'Sabtu',
      7: 'Minggu',
    };

    const result = {
      day: [] as IReqSettingScheduleRegularWorkUnit['day'],
      timetable: [] as ITimeTable[],
    };

    const grouped = new Map<
      number,
      {
        index: number;
        label: string;
        is_holiday: boolean;
        data: {
          name: string;
          id: string;
          start_time: string;
          end_time: string;
        }[];
      }
    >();

    for (const item of rawResponse) {
      const index = item.day;

      const name = item.name.toLowerCase().includes('masuk') ? 'Jam Masuk' : 'Jam Keluar';

      if (!grouped.has(index)) {
        grouped.set(index, {
          index,
          label: dayLabels[index],
          is_holiday: item.holiday,
          data: [],
        });
      }

      grouped.get(index)?.data.push({
        name,
        id: item.id,
        start_time: item.start_time,
        end_time: item.end_time,
      });
    }

    result.day = Array.from(grouped.values());
    if (rawResponse.length > 0) {
      formik.setValues({ ...result, checked: false });
    }
  }

  return { queryDetail, formik, querySchedule, mutationSettingSchedule };
}
