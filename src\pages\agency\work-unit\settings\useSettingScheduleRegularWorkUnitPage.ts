import { dayListString } from '@/constants/data-constans';
import { useUi } from '@/hooks/useUi.ts';
import { normalizeTime } from '@/lib/utils';
import { mockDataCreateWorkUnitRequest } from '@/mocks/mock-data-schedule';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import type { IReqScheduleRegular } from '@/types/request/IReqScheduleRegular.ts';
import type {
  IReqSettingScheduleRegularWorkUnit,
  ITimeTable,
} from '@/types/request/IReqSettingScheduleRegularWorkUnit.ts';
import type { IResScheduleRegularWorkUnit } from '@/types/response/IResScheduleRegularWorkUnit.ts';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useNavigate, useParams } from 'react-router-dom';

export function useSettingScheduleRegularWorkUnitPage() {
  const { id } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const { toast } = useUi();
  const navigate = useNavigate();

  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', id],
    queryFn: () => workUnitRepository.getDetailWorkUnit(id || ''),
  });

  const initValue: IReqSettingScheduleRegularWorkUnit = mockDataCreateWorkUnitRequest;

  const mutationSettingSchedule = useMutation({
    mutationFn: async (e: IReqScheduleRegular[]) => {
      await workUnitRepository.settingScheduleRegular(id, e).then(() => {
        queryDetail.refetch().then();
        toast.success('Jadwal kerja berhasil diubah');
        navigate(-1);
      });
    },
  });

  const init: any = { day: [], checked: false };
  const formik = useFormik({
    initialValues: init,
    onSubmit: (values) => {
      const data: IReqScheduleRegular[] = [];

      if (values.day && values.day.length > 0) {
        for (const day of values.day) {
          for (const item of day.data as any) {
            data.push({
              ...(item.id && { id: item.id }),
              day: day.index,
              name: item.name,
              description: `${item.name} kerja hari ${day.label}`,
              start_time: normalizeTime(item.start_time),
              end_time: normalizeTime(item.end_time),
              is_holiday: day?.is_holiday || false,
            });
          }
        }
      }

      mutationSettingSchedule.mutate(data);
    },
  });

  // Function to copy Monday's schedule to other weekdays
  const copyFromMonday = () => {
    const mondayData = formik.values.day.find((day: any) => day.index === 1);
    if (!mondayData || mondayData.is_holiday) {
      toast.error('Tidak dapat menyalin dari Senin karena Senin adalah hari libur atau tidak memiliki jadwal');
      return;
    }

    const updatedDays = Array.from({ length: 7 }).map((_, i) => {
      const day = formik.values.day[i];
      if (day?.index !== 0) {
        if (day) {
          return {
            ...day,
            data: mondayData.data.map((item: any) => ({
              ...item,
              id: undefined, // Hapus ID
            })),
          };
        } else {
          return {
            is_holiday: false,
            index: i + 1,
            label: dayListString[i],
            data: mondayData.data.map((item: any) => ({
              ...item,
              id: undefined, // Hapus ID
            })),
          };
        }
      }
      return day;
    });

    formik.setFieldValue('day', updatedDays);
    toast.success('Jadwal Senin berhasil disalin ke semua hari kerja (termasuk Sabtu)');
  };

  // Function to move shift up in the schedule
  const moveShiftUp = (dayIndex: number, shiftIndex: number) => {
    if (shiftIndex === 0) return; // Already at the top

    const currentDay = formik.values.day[dayIndex];
    const newData = [...currentDay.data];

    // Swap with the item above
    [newData[shiftIndex - 1], newData[shiftIndex]] = [newData[shiftIndex], newData[shiftIndex - 1]];

    // Validate times don't overlap after reordering
    if (!validateShiftTimes(newData)) {
      toast.error('Perubahan urutan tidak dapat dilakukan karena akan menyebabkan jadwal tumpang tindih');
      return;
    }

    formik.setFieldValue(`day[${dayIndex}].data`, newData);
    toast.success('Urutan jadwal berhasil diubah');
  };

  // Function to move shift down in the schedule
  const moveShiftDown = (dayIndex: number, shiftIndex: number) => {
    const currentDay = formik.values.day[dayIndex];
    if (shiftIndex === currentDay.data.length - 1) return; // Already at the bottom

    const newData = [...currentDay.data];

    // Swap with the item below
    [newData[shiftIndex], newData[shiftIndex + 1]] = [newData[shiftIndex + 1], newData[shiftIndex]];

    // Validate times don't overlap after reordering
    if (!validateShiftTimes(newData)) {
      toast.error('Perubahan urutan tidak dapat dilakukan karena akan menyebabkan jadwal tumpang tindih');
      return;
    }

    formik.setFieldValue(`day[${dayIndex}].data`, newData);
    toast.success('Urutan jadwal berhasil diubah');
  };

  const querySchedule = useQuery({
    queryKey: ['work_unit_schedule_regular', id],
    queryFn: async () =>
      await workUnitRepository.getTimeTableSchedule(id || '').then((res) => {
        if (res && res.length > 0) {
          mappingDataResponse(res);
        } else {
          formik.setValues({ ...initValue, checked: false });
        }
        return res;
      }),
  });

  // Function to validate shift times don't overlap
  const validateShiftTimes = (dayData: any[]) => {
    for (let i = 0; i < dayData.length - 1; i++) {
      const currentEnd = dayData[i].end_time;
      const nextStart = dayData[i + 1].start_time;

      if (currentEnd && nextStart && currentEnd > nextStart) {
        return false;
      }
    }
    return true;
  };

  function mappingDataResponse(rawResponse: IResScheduleRegularWorkUnit[]) {
    const dayLabels: Record<number, string> = {
      1: 'Senin',
      2: 'Selasa',
      3: 'Rabu',
      4: 'Kamis',
      5: 'Jumat',
      6: 'Sabtu',
      7: 'Minggu',
    };

    const result = {
      day: [] as IReqSettingScheduleRegularWorkUnit['day'],
      timetable: [] as ITimeTable[],
    };

    const grouped = new Map<
      number,
      {
        index: number;
        label: string;
        is_holiday: boolean;
        data: {
          name: string;
          id: string;
          start_time: string;
          end_time: string;
        }[];
      }
    >();

    for (const item of rawResponse) {
      const index = item.day;

      if (!grouped.has(index)) {
        grouped.set(index, {
          index,
          label: dayLabels[index],
          is_holiday: item.holiday,
          data: [],
        });
      }

      grouped.get(index)?.data.push({
        name: item.name,
        id: item.id,
        start_time: item.start_time,
        end_time: item.end_time,
      });
    }

    result.day = Array.from(grouped.values());
    if (rawResponse.length > 0) {
      formik.setValues({ ...result, checked: false });
    }
  }

  return {
    queryDetail,
    formik,
    querySchedule,
    mutationSettingSchedule,
    copyFromMonday,
    moveShiftUp,
    moveShiftDown,
    validateShiftTimes,
  };
}
