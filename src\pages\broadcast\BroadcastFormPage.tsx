import InputAutocomplete from '@/components/InputAutocomplete';
import InputSearch from '@/components/InputSearch';
import InputSelect from '@/components/InputSelect.tsx';
import InputText from '@/components/InputText.tsx';
import PageContainer from '@/components/PageContainer.tsx';
import WhatsAppMessageEditor from '@/components/WhatsAppMessageEditor';
import PageTitle from '@/components/page-title.tsx';
import { LoadingSpinner } from '@/components/ui/CircularLoading';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card.tsx';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator.tsx';
import { ASSETS } from '@/constants/assets.ts';
import { cn } from '@/lib/utils';
import useBroadcastFormPage from '@/pages/broadcast/useBroadcastFormPage.ts';
import { ROUTES } from '@/routes/routes.ts';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { FormikProvider } from 'formik';
import { Plus } from 'lucide-react';

export default function BroadcastFormPage() {
  const page = useBroadcastFormPage();
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Broadcast WA',
      path: ROUTES.BROADCAST_LIST(),
    },
    {
      label: 'Buat bar',
    },
  ];

  function allCoverage() {
    return (
      <div className={'py-10 flex flexContainer justify-center items-center flex-col'}>
        <img alt={'illustrations'} src={ASSETS.IL_EMPLOYEE} className={'h-48'} />
        <div className="mt-7 font-semibold">Cakupan dipilih untuk semua pegawai yang terdaftar</div>
      </div>
    );
  }

  function workUnitCoverage() {
    const data: ILabelValue<string>[] = page.filteredAgency.map((e) => {
      return {
        label: e.name,
        value: e.id,
      };
    });
    return (
      <div className="grid gap-5">
        <InputAutocomplete
          label="Instansi"
          placeholder="Pilih instansi"
          disableFormik
          options={data}
          id="agency_ids"
          required
          name="agency_data"
          onValueChange={(e) => page.setSelectedAgencyId(e)}
          value={page.selectedAgencyId}
        />
        {page.selectedAgencyId && page.queryWorkUnit?.isPending && (
          <div className="flex items-center justify-center gap-2 py-8">
            <LoadingSpinner />
            <div>LOADING</div>
          </div>
        )}

        {page.selectedAgencyId && !page?.queryWorkUnit?.isPending && (
          <div className="grid gap-5">
            <InputSearch
              placeholder="Cari nama unit kerja"
              setSearchValue={page.setSearchValue}
              searchValue={page.searchValue}
            />
            {page.filteredWorkUnit.length === 0 ? (
              <div className="flex flex-col gap-8 py-8 items-center justify-center">
                <img alt={'illustrations'} src={ASSETS.IL_EMPTY_STATE} className={'h-32'} />

                <div className="font-semibold text-gray-500">Pencarian Tidak ditemukan</div>
              </div>
            ) : (
              page.filteredWorkUnit.map((item, i) => {
                const active = !!page.formik.values.work_unit_id.find((e) => e === item.id);
                return (
                  <div
                    onClick={() => page.onChangeWorkUnit(item.id)}
                    className={cn(
                      'flex border p-4 rounded-md active:border-primary duration-200 justify-between cursor-pointer hover:bg-primary/5 hover:border-primary/10',
                      active ? 'border-primary' : '',
                    )}
                    key={i}
                  >
                    <div className="flex items-center gap-2">
                      <Checkbox checked={active} />
                      <div>
                        <div className="font-semibold">{item.name}</div>
                        <p className="text-gray-500 text-xs">{item.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        )}
      </div>
    );
  }

  function agencyCoverage() {
    return (
      <div>
        <InputSearch
          placeholder="Cari nama agency"
          setSearchValue={page.setSearchValue}
          searchValue={page.searchValue}
        />
        <div className="grid gap-4 mt-6">
          {page.filteredAgency.length === 0 ? (
            <div className="flex flex-col gap-8 py-8 items-center justify-center">
              <img alt={'illustrations'} src={ASSETS.IL_EMPTY_STATE} className={'h-32'} />

              <div className="font-semibold text-gray-500">Pencarian Tidak ditemukan</div>
            </div>
          ) : (
            page.filteredAgency.map((item, i) => {
              const active = !!page.formik.values.agency_id.find((e) => e === item.id);
              return (
                <div
                  onClick={() => page.onChangeAgency(item.id)}
                  className={cn(
                    'flex border p-4 rounded-md active:border-primary duration-200 justify-between cursor-pointer hover:bg-primary/5 hover:border-primary/10',
                    active ? 'border-primary' : '',
                  )}
                  key={i}
                >
                  <div className="flex items-center gap-2">
                    <Checkbox checked={active} />
                    <div>
                      <div className="font-semibold">{item.name}</div>
                      <p className="text-gray-500 text-xs">{item.description}</p>
                    </div>
                  </div>
                  <div>{item.total_employee} pegawai terdaftar</div>
                </div>
              );
            })
          )}
        </div>
      </div>
    );
  }

  function checkingCoverage() {
    switch (page?.formik?.values?.coverage_type) {
      case 'ALL':
        return allCoverage();
      case 'AGENCY':
        return agencyCoverage();
      case 'WORK_UNIT':
        return workUnitCoverage();
      default:
        return (
          <div className={'h-60 flex items-center justify-center flex-col gap-2'}>
            <img alt={'illustrations'} src={ASSETS.IL_SELECT} className={'h-48'} />
            <div className={'text-gray-500'}>Cakupan belum dipilih</div>
          </div>
        );
    }
  }

  return (
    <FormikProvider value={page.formik}>
      <PageContainer>
        <PageTitle title={'Buat broadcast WA'} breadcrumb={breadcrumbs} />
        <Card>
          <CardContent>
            <div className={'grid gap-4'}>
              <InputText
                alignment={'horizontal'}
                name={'title'}
                id={'title'}
                placeholder={'Masukan judul broadcast'}
                label={'Judul'}
                required
              />
              <InputSelect
                id={'coverage_type'}
                name={'coverage_type'}
                options={page.dataListType}
                placeholder={'Pilih cakupan broadcast'}
                label={'Cakupan'}
                required
                alignment={'horizontal'}
              />
              <WhatsAppMessageEditor
                id={'body'}
                name={'body'}
                label={'Isi pesan'}
                placeholder={'Masukan isi pesan'}
                alignment={'horizontal'}
                required={true}
              />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div>Pilih cakupan</div>
          </CardContent>
          <Separator />
          <CardContent>{checkingCoverage()}</CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Checkbox onCheckedChange={(e) => page.setChecked(e as boolean)} checked={page.checked} />
                <div>Konfirmasi untuk membuat broadcast WA</div>
              </div>
              <Button
                loading={page.mutationCreate?.isPending}
                disabled={!page.checkValidButton()}
                onClick={() => page.formik.handleSubmit()}
              >
                <Plus />
                Buat pengajuan broadcast WA
              </Button>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    </FormikProvider>
  );
}
