import { ASSETS } from '@/constants/assets';
import { useAuth } from '@/hooks/use-auth.ts';
import { useSidebar } from '@/hooks/use-sidebar';
import { cn } from '@/lib/utils';
import { ROUTES } from '@/routes/routes';
import { Briefcase, Building2, ChevronDown, LogOut, Search, User } from 'lucide-react';
import { Link } from 'react-router-dom';
import PageContainer from './PageContainer';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';

function getInitials(name: string): string {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 12) return 'Selamat Pagi';
  if (hour < 15) return 'Selamat Siang';
  if (hour < 18) return 'Selamat Sore';
  return 'Selamat Malam';
}

interface IProps {
  showBrand?: boolean;
  full?: boolean;
}

export default function TopBar(props: IProps) {
  const auth = useAuth();
  const sidebar = useSidebar();

  const handleLogout = () => {
    auth?.logOut();
  };

  return (
    <div>
      <div className="h-16 w-full"></div>
      <div
        className="fixed bg-white/95 backdrop-blur-sm border-b border-gray-200 flex top-0 left-0 w-full shadow-sm"
        style={{ zIndex: 1 }}
      >
        {!props.full && (
          <div className={cn('duration-300 transition-all', sidebar.getOpenState() ? 'w-72' : 'w-[90px]')}></div>
        )}
        <div className="flex-1 h-16 flex items-center">
          <PageContainer disablePaddingY>
            <div className="flex items-center justify-between w-full">
              {/* Left Section - Greeting & Search */}
              {props.showBrand ? (
                <Link to={ROUTES.HOME()} className="flex items-center gap-2">
                  <img src={ASSETS.LG_BRAND} alt="nuca lale" className="w-6 h-6 mr-1" />
                  <div>
                    <h1
                      className={cn(
                        'font-bold text-lg whitespace-nowrap transition-[transform,opacity,display] ease-in-out duration-300',
                        'translate-x-0 opacity-100',
                      )}
                    >
                      Nuca Lale
                    </h1>
                    <p className={'text-xs text-gray-500'}>Pemerintah Kab. Manggarai</p>
                  </div>
                </Link>
              ) : (
                <div className="flex items-center gap-6">
                  <div className="hidden sm:block">
                    <div className="flex flex-col">
                      <h1 className="text-lg font-semibold text-gray-900">
                        {getGreeting()}, {auth?.user?.name?.split(' ')[0] || 'User'}! 👋
                      </h1>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3">
                <Button size="sm" variant="ghost" className="md:hidden">
                  <Search className="w-4 h-4" />
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-2 h-auto p-2">
                      <Avatar className="w-8 h-8 border-2 border-gray-200">
                        <AvatarImage src={auth?.user?.profile_picture} alt={auth?.user?.name} />
                        <AvatarFallback className="bg-blue-100 text-blue-700 text-sm font-medium">
                          {auth?.user?.name ? getInitials(auth.user.name) : 'AD'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="hidden sm:flex flex-col items-start">
                        <span className="text-sm font-medium text-gray-900">{auth?.user?.name || 'Admin User'}</span>
                        <span className="text-xs text-gray-500 capitalize">
                          {auth?.user?.role?.toLowerCase() || 'Administrator'}
                        </span>
                      </div>
                      <ChevronDown className="w-4 h-4 text-gray-400 hidden sm:block" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-64">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">{auth?.user?.name || 'Admin User'}</p>
                        <p className="text-xs text-gray-500">{auth?.user?.email || '<EMAIL>'}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    <div className="px-2 py-2">
                      <div className="flex items-center gap-2 text-xs text-gray-600 mb-1">
                        <Building2 className="w-3 h-3" />
                        <span>{auth?.user?.agency_name || 'Perangkat daerah Tidak Diketahui'}</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Briefcase className="w-3 h-3" />
                        <span>{auth?.user?.work_unit_name || 'Unit Kerja Tidak Diketahui'}</span>
                      </div>
                    </div>

                    <DropdownMenuSeparator />

                    <DropdownMenuItem className="cursor-pointer">
                      <User className="w-4 h-4 mr-2" />
                      Profil Saya
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />

                    <DropdownMenuItem
                      className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50"
                      onClick={handleLogout}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Keluar
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </PageContainer>
        </div>
      </div>
    </div>
  );
}
