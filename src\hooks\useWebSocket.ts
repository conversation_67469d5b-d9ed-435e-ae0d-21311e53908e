import { useCallback, useEffect, useRef, useState } from 'react';
import { WebSocketService, type WebSocketCallbacks } from '@/services/websocket.service';
import type { IResSignIn } from '@/types/response/IResSignIn';

export interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connectionStatus: {
    isConnected: boolean;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
  };
  connect: () => Promise<void>;
  disconnect: () => void;
  subscribeToQRCode: (qrCode: string) => void;
  unsubscribeFromCurrentTopic: () => void;
}

export interface UseWebSocketOptions {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  onLoginReceived?: (loginData: IResSignIn) => void;
  autoConnect?: boolean;
}

export function useWebSocket(options: UseWebSocketOptions = {}): UseWebSocketReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
  });

  const webSocketServiceRef = useRef<WebSocketService | null>(null);
  const optionsRef = useRef(options);

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  // Initialize WebSocket service
  useEffect(() => {
    const callbacks: WebSocketCallbacks = {
      onConnect: () => {
        console.log('WebSocket connected via hook');
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        updateConnectionStatus();
        optionsRef.current.onConnect?.();
      },
      onDisconnect: () => {
        console.log('WebSocket disconnected via hook');
        setIsConnected(false);
        setIsConnecting(false);
        updateConnectionStatus();
        optionsRef.current.onDisconnect?.();
      },
      onError: (error: any) => {
        console.error('WebSocket error via hook:', error);
        setError(error?.message || 'WebSocket connection error');
        setIsConnecting(false);
        updateConnectionStatus();
        optionsRef.current.onError?.(error);
      },
      onLoginReceived: (loginData: IResSignIn) => {
        console.log('Login data received via hook:', loginData);
        optionsRef.current.onLoginReceived?.(loginData);
      },
    };

    webSocketServiceRef.current = new WebSocketService(callbacks);

    // Auto-connect if enabled
    if (options.autoConnect) {
      connect();
    }

    return () => {
      // Cleanup on unmount
      if (webSocketServiceRef.current) {
        webSocketServiceRef.current.disconnect();
        webSocketServiceRef.current = null;
      }
    };
  }, []); // Empty dependency array for initialization only

  const updateConnectionStatus = useCallback(() => {
    if (webSocketServiceRef.current) {
      setConnectionStatus(webSocketServiceRef.current.getConnectionStatus());
    }
  }, []);

  const connect = useCallback(async (): Promise<void> => {
    if (!webSocketServiceRef.current) {
      throw new Error('WebSocket service not initialized');
    }

    if (isConnected || isConnecting) {
      console.log('WebSocket already connected or connecting');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      await webSocketServiceRef.current.connect();
      console.log('WebSocket connection established');
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setError(error instanceof Error ? error.message : 'Connection failed');
      setIsConnecting(false);
      throw error;
    }
  }, [isConnected, isConnecting]);

  const disconnect = useCallback(() => {
    if (webSocketServiceRef.current) {
      webSocketServiceRef.current.disconnect();
      setIsConnected(false);
      setIsConnecting(false);
      setError(null);
      updateConnectionStatus();
    }
  }, [updateConnectionStatus]);

  const subscribeToQRCode = useCallback((qrCode: string) => {
    if (!webSocketServiceRef.current) {
      console.warn('WebSocket service not initialized');
      return;
    }

    if (!isConnected) {
      console.warn('WebSocket not connected. Cannot subscribe to QR code topic.');
      return;
    }

    console.log(`Subscribing to QR code: ${qrCode}`);
    webSocketServiceRef.current.subscribeToQRCode(qrCode);
  }, [isConnected]);

  const unsubscribeFromCurrentTopic = useCallback(() => {
    if (webSocketServiceRef.current) {
      webSocketServiceRef.current.unsubscribeFromCurrentTopic();
    }
  }, []);

  // Update callbacks when options change
  useEffect(() => {
    if (webSocketServiceRef.current) {
      const callbacks: WebSocketCallbacks = {
        onConnect: () => {
          setIsConnected(true);
          setIsConnecting(false);
          setError(null);
          updateConnectionStatus();
          optionsRef.current.onConnect?.();
        },
        onDisconnect: () => {
          setIsConnected(false);
          setIsConnecting(false);
          updateConnectionStatus();
          optionsRef.current.onDisconnect?.();
        },
        onError: (error: any) => {
          setError(error?.message || 'WebSocket connection error');
          setIsConnecting(false);
          updateConnectionStatus();
          optionsRef.current.onError?.(error);
        },
        onLoginReceived: (loginData: IResSignIn) => {
          optionsRef.current.onLoginReceived?.(loginData);
        },
      };

      webSocketServiceRef.current.updateCallbacks(callbacks);
    }
  }, [updateConnectionStatus]);

  return {
    isConnected,
    isConnecting,
    error,
    connectionStatus,
    connect,
    disconnect,
    subscribeToQRCode,
    unsubscribeFromCurrentTopic,
  };
}
