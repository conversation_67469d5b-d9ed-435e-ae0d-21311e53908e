import { dayListString } from '@/constants/data-constans';
import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import { mockDataCreateWorkUnitRequest } from '@/mocks/mock-data-schedule';
import { ROUTES } from '@/routes/routes';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqCreateWorkUnit } from '@/types/request/IReqCreateWorkUnit';
import type { IResDetailAgency } from '@/types/response/IResDetailAgency';
import type { BaseResponse } from '@/types/response/IResModel';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import * as yup from 'yup';

export function useFormWorkUnit() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const { agencyId } = useParams();
  const navigate = useNavigate();
  const { toast } = useUi();

  const [activeForm, setActiveForm] = useState<string>('0');

  const initValue: IReqCreateWorkUnit = {
    agency_id: agencyId || '',
    type: 'REGULAR',
    timetable: [],
    shifts: [],
    location: [
      {
        name: '',
        radius: 200,
      },
    ],
    description: '',
    name: '',
    day: dayListString.map((e, i) => {
      return {
        index: i,
        label: e,
        data: [
          {
            end_time: '',
            name: '',
            start_time: '',
          },
        ],
      };
    }),
  };

  const validationSchema = yup.object().shape({
    agency_id: yup.string().required('Agency ID wajib diisi'),
    name: yup.string().required('Nama unit kerja wajib diisi'),
    type: yup.string().oneOf(['REGULAR', 'SHIFT']).required('Tipe penjadwalan wajib dipilih'),
    shifts: yup.array().when('type', {
      is: 'SHIFT',
      then: (schema) =>
        schema
          .of(
            yup.object().shape({
              name: yup.string().required('Nama shift wajib diisi'),
              start_time: yup.string().required('Waktu mulai wajib diisi'),
              end_time: yup.string().required('Waktu selesai wajib diisi'),
              time_tables: yup
                .array()
                .of(
                  yup.object().shape({
                    name: yup.string().required('Nama absensi wajib diisi'),
                    start_time: yup.string().required('Waktu mulai wajib diisi'),
                    end_time: yup.string().required('Waktu selesai wajib diisi'),
                  }),
                )
                .min(1, 'Minimal 1 jadwal absensi per shift'),
            }),
          )
          .min(1, 'Minimal 1 shift untuk tipe SHIFT'),
      otherwise: (schema) => schema.notRequired(),
    }),
    day: yup.array().when('type', {
      is: 'REGULAR',
      then: (schema) =>
        schema.of(
          yup.object().shape({
            index: yup.number().required(),
            label: yup.string().required(),
            data: yup
              .array()
              .of(
                yup.object().shape({
                  name: yup.string(),
                  start_time: yup.string(),
                  end_time: yup
                    .string()
                    .test('is-after-start', 'Waktu toleransi harus setelah waktu absen', function (value) {
                      const { start_time } = this.parent;
                      return !start_time || !value || start_time < value;
                    }),
                }),
              )
              .min(1, 'Minimal 1 data jadwal per hari'),
          }),
        ),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const queryDetail = useQuery({
    queryKey: ['detailAgency', agencyId],
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.DETAIL_AGENCY(agencyId!))
        .then((res: BaseResponse<IResDetailAgency>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
    enabled: !!agencyId,
  });

  const mutationCreate = useMutation({
    mutationFn: (data: any) =>
      httpService
        .POST(ENDPOINT.CREATE_WORK_UNIT(), data)
        .then(() => {
          onSuccessCreate();
        })
        .catch((e) => errorService.fetchApiError(e)),
  });

  const formik = useFormik({
    initialValues: initValue,
    validationSchema: validationSchema,
    onSubmit: (e) => {
      const data: any = {
        name: e.name,
        agency_id: e.agency_id,
        description: e.description,
        type: e.type,
        location: e.location.map((loc) => {
          return {
            ...loc,
            radius: loc.radius ? parseFloat(loc.radius.toString()) : 200,
            lat: loc.lat || 0,
            lng: loc.lng || 0,
          };
        }),
      };

      if (e.type === 'REGULAR') {
        const timetable = e.day
          .flatMap((dayItem) =>
            dayItem.data.map((entry) => ({
              day: dayItem.index,
              name: entry.name,
              start_time: entry.start_time + ':00',
              end_time: entry.end_time + ':00',
            })),
          )
          .filter((item) => item.name && item.start_time && item.end_time);

        data.timetable = timetable;
      } else if (e.type === 'SHIFT') {
        data.timetable = [];
        data.shifts = (e.shifts || []).map((shift) => ({
          ...shift,
          start_time: shift.start_time ? shift.start_time + ':00' : '',
          end_time: shift.end_time ? shift.end_time + ':00' : '',
          time_tables: shift.time_tables.map((timeTable) => ({
            ...timeTable,
            start_time: timeTable.start_time ? timeTable.start_time + ':00' : '',
            end_time: timeTable.end_time ? timeTable.end_time + ':00' : '',
          })),
        }));
      }

      mutationCreate.mutate(data);
    },
  });

  const dataDetail = queryDetail.data;

  useEffect(() => {
    formik.setValues({
      ...formik.values,
      day: mockDataCreateWorkUnitRequest.day,
    });
  }, [dataDetail]);

  // Initialize shifts when type changes to SHIFT
  useEffect(() => {
    if (formik.values.type === 'SHIFT' && (!formik.values.shifts || formik.values.shifts.length === 0)) {
      formik.setFieldValue('shifts', [
        {
          name: '',
          start_time: '',
          end_time: '',
          time_tables: [
            {
              name: 'Jam Masuk',
              start_time: '',
              end_time: '',
            },
            {
              name: 'Jam Pulang',
              start_time: '',
              end_time: '',
            },
          ],
        },
      ]);
    }
  }, [formik.values.type]);

  function onSuccessCreate() {
    navigate(ROUTES.DETAIL_AGENCY(dataDetail?.id || ''));
    toast.success('Data Unit kerja berhasil dibuat !');
  }

  return { dataDetail, formik, activeForm, setActiveForm, mutationCreate };
}
