import { WorkUnitRepository } from '@/repositories/work-unit-repositories';
import type { IReqCreateWorkUnit } from '@/types/request/IReqCreateWorkUnit';
import { useMutation } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import * as yup from 'yup';

export function useFormWorkUnit() {
  const { agencyId } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const validationSchema = yup.object().shape({
    name: yup.string().required(),
    checked: yup.boolean().required().isTrue(),
  });

  const initState: IReqCreateWorkUnit = {
    name: '',
    description: '',
    type: 'REGULAR',
    agency_id: agencyId || '',
    checked: false,
  };

  const mutateCreate = useMutation({
    mutationKey: ['create_work_unit'],

    mutationFn: (e: IReqCreateWorkUnit) =>
      workUnitRepository.createWorkUnit(e).then((res) => {
        setShowSuccess(true);
        return res;
      }),
  });

  const formik = useFormik({
    initialValues: initState,
    validationSchema: validationSchema,
    onSubmit: (e) => {
      const data = {
        ...e,
        checked: undefined,
      };
      mutateCreate.mutate(data);
    },
  });

  return { formik, agencyId, mutateCreate, showSuccess };
}
