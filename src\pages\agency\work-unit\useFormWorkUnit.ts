import { WorkUnitRepository } from '@/repositories/work-unit-repositories';
import type { IReqCreateWorkUnit } from '@/types/request/IReqCreateWorkUnit';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import * as yup from 'yup';

export function useFormWorkUnit() {
  const { agencyId, id } = useParams();

  const workUnitRepository = new WorkUnitRepository();
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  const validationSchema = yup.object().shape({
    name: yup.string().required(),
    checked: yup.boolean().required().isTrue(),
  });

  const initState: IReqCreateWorkUnit = {
    name: '',
    description: '',
    type: 'REGULAR',
    agency_id: agencyId || '',
    checked: false,
  };

  const mutateCreate = useMutation({
    mutationKey: ['create_work_unit'],

    mutationFn: (e: IReqCreateWorkUnit) =>
      workUnitRepository.createWorkUnit(e).then((res) => {
        setShowSuccess(true);
        return res;
      }),
  });

  const mutationEdit = useMutation({
    mutationKey: ['create_work_unit'],
    mutationFn: (e: IReqCreateWorkUnit) =>
      workUnitRepository.editWorkUnit(id || '', e).then((res) => {
        setShowSuccess(true);
        return res;
      }),
  });

  const formik = useFormik({
    initialValues: initState,
    validationSchema: validationSchema,
    onSubmit: (e) => {
      const data = {
        ...e,
        checked: undefined,
      };
      if (id) {
        mutationEdit.mutate(data);
      } else {
        mutateCreate.mutate(data);
      }
    },
  });

  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', id],
    enabled: !!id,
    queryFn: () => {
      return workUnitRepository.getDetailWorkUnit(id!).then((res) => {
        formik.setValues({
          agency_id: res.agency_id,
          name: res.name,
          description: res.description,
          type: res.type,
          checked: false,
        });
        return res;
      });
    },
  });

  return { formik, agencyId, mutateCreate, showSuccess, id, queryDetail, mutationEdit };
}
