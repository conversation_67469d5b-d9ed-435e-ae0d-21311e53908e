import { WorkUnitRepository } from '@/repositories/work-unit-repositories';
import type { IReqCreateWorkUnit } from '@/types/request/IReqCreateWorkUnit';
import { useMutation } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useParams } from 'react-router-dom';

export function useFormWorkUnit() {
  const { agencyId } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const initState: IReqCreateWorkUnit = {
    name: '',
    description: '',
    type: 'REGULAR',
    agency_id: agencyId || '',
    checked: false,
  };

  const mutateCreate = useMutation({
    mutationKey: ['create_work_unit'],
    mutationFn: (e: IReqCreateWorkUnit) => workUnitRepository.createWorkUnit(e),
  });

  const formik = useFormik({
    initialValues: initState,
    onSubmit: (e) => {
      mutateCreate.mutate(e);
    },
  });

  return { formik, agencyId };
}
