import { ENDPOINT } from '@/constants/endpoint.ts';
import ErrorService from '@/services/error.service.ts';
import { HttpService } from '@/services/http.service.ts';
import type { IResListAttendance } from '@/types/response/IResListAttendance.ts';
import type { BaseResponsePaginated } from '@/types/response/IResModel.ts';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';

export class AttendanceRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async getAttendance(param: IFilterList) {
    try {
      const params = buildSearchParams(param);

      const res: BaseResponsePaginated<IResListAttendance[]> = await this.httpService.GET(
        ENDPOINT.LIST_ATTENDANCE() + buildQueryString(params),
      );
      return res.data;
    } catch (e) {
      this.errorService.fetchApiError(e);
    }
  }
}
