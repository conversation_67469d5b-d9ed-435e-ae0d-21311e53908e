import { cn } from '@/lib/utils';
import { Circle<PERSON>heckBig, CircleX } from 'lucide-react';

export default function IsAttendingText({ isAttending }: { isAttending: boolean }) {
  return (
    <div className="flex items-center gap-2">
      {isAttending ? (
        <CircleCheckBig className="text-green-600" size={14} />
      ) : (
        <CircleX className="text-red-600" size={14} />
      )}
      <div className={cn(isAttending ? 'text-green-600' : 'text-red-600', 'font-bold')}>
        {isAttending ? 'Hadir' : 'Tidak Absensi'}
      </div>
    </div>
  );
}
