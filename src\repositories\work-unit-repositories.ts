import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqCreateWorkUnit } from '@/types/request/IReqCreateWorkUnit';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { BaseResponse, BaseResponsePaginated } from '@/types/response/IResModel';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';

export class WorkUnitRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();
  async listWorkUnit(filterData: IFilterList) {
    const params = buildSearchParams(filterData);
    return this.httpService
      .GET(ENDPOINT.LIST_WORK_UNIT() + buildQueryString(params))
      .then((res: BaseResponsePaginated<IResWorkUnit[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }
  async createWorkUnit(data: IReqCreateWorkUnit) {
    return this.httpService
      .POST(ENDPOINT.CREATE_WORK_UNIT(), data)
      .then((res: BaseResponse<IResWorkUnit>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }
}
