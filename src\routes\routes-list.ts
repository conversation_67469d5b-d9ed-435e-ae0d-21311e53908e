import type { PageType } from '@/enums/page-type';
import SignInPage from '@/pages/auth/SignInPage';
import HomePage from '@/pages/home/<USER>';
import { type JSX } from 'react';
import { ROUTES } from './routes';
import AgencyPage from '@/pages/agency/page/agency-page';
import AgencyFormPage from '@/pages/agency/form/agency-form-page';
import ListEmployeePage from '@/pages/employee/list-employee/ListEmployeePage';
import EmployeeFormPage from '@/pages/employee/form-employee/EmployeeFormPage';
import DetailAgencyPage from '@/pages/agency/detail-agency/DetailAgencyPage';
import FormWorkUnit from '@/pages/agency/work-unit/FormWorkUnit';
import DetailWorkUnit from '@/pages/agency/work-unit/DetailWorkUnit';
import DetailEmployeePage from '@/pages/employee/detail-employee/DetailEmployeePage';
import RegisterEmployeePage from '@/pages/employee/register-employee-face/RegisterEmployeePage';
import SettingShiftTable from '@/pages/agency/work-unit/SetttingShiftTable';
import WorkUnitPage from '@/pages/work-unit/work-unit-page';
import SettingWorkUnitLocationPage from '@/pages/agency/work-unit/settings/SettingWorkUnitLocationPage.tsx';
import SettingScheduleRegularWorkUnitPage from '@/pages/agency/work-unit/settings/SettingScheduleRegularWorkUnitPage.tsx';
import SettingShiftWorkUnitPage from '@/pages/agency/work-unit/settings/SettingShiftWorkUnitPage';
import AttendanceListPage from '@/pages/attendance/AttendanceListPage.tsx';
import SettingShiftAttendanceTimePage from '@/pages/agency/work-unit/settings/SettingShiftAttendanceTimePage.tsx';
import AttendanceHistoryPage from '@/pages/attendance/AttendanceHistoryPage.tsx';
import AddWorkUnitManager from '@/pages/agency/work-unit/add-work-unit-manger/AddWorkUnitManager.tsx';

export interface IRouteList {
  element: () => JSX.Element;
  path: string;
  type: PageType;
}

export const privateRoutes: IRouteList[] = [
  {
    element: HomePage,
    path: ROUTES.HOME(),
    type: 'PRIMARY',
  },

  {
    element: AgencyPage,
    path: ROUTES.MASTER_DATA.AGENCY(),
    type: 'PRIMARY',
  },
  {
    element: AgencyFormPage,
    path: ROUTES.MASTER_DATA.NEW_AGENCY(),
    type: 'PRIMARY',
  },
  {
    element: AgencyFormPage,
    path: ROUTES.MASTER_DATA.EDIT_AGENCY(':id'),
    type: 'PRIMARY',
  },
  {
    element: DetailAgencyPage,
    path: ROUTES.DETAIL_AGENCY(':id'),
    type: 'PRIMARY',
  },
  {
    element: FormWorkUnit,
    path: ROUTES.CREATE_WORK_UNIT(':agencyId'),
    type: 'PRIMARY',
  },
  {
    element: DetailWorkUnit,
    path: ROUTES.DETAIL_WORK_UNIT(':workUnitId'),
    type: 'PRIMARY',
  },
  {
    element: SettingShiftTable,
    path: ROUTES.SETTING_SHIFT_TABLE(':id'),
    type: 'PRIMARY',
  },

  {
    element: SettingWorkUnitLocationPage,
    path: ROUTES.SETTING_WORK_UNIT_LOCATION(':id'),
    type: 'PRIMARY',
  },

  /*End agency
   *  --------
   * Start Employee
   * */
  {
    element: DetailEmployeePage,
    path: ROUTES.DETAIL_EMPLOYEE(':id'),
    type: 'PRIMARY',
  },
  {
    element: ListEmployeePage,
    path: ROUTES.EMPLOYEE_LIST(),
    type: 'PRIMARY',
  },
  {
    element: EmployeeFormPage,
    path: ROUTES.CREATE_EMPLOYEE(),
    type: 'PRIMARY',
  },
  {
    element: RegisterEmployeePage,
    path: ROUTES.REGISTER_EMPLOYEE_FACE(':id'),
    type: 'PRIMARY',
  },

  {
    element: WorkUnitPage,
    path: ROUTES.LIST_WORK_UNIT(),
    type: 'PRIMARY',
  },
  {
    element: EmployeeFormPage,
    path: ROUTES.EDIT_EMPLOYEE(':id'),
    type: 'PRIMARY',
  },
  {
    element: FormWorkUnit,
    path: ROUTES.EDIT_WORK_UNIT(':id'),
    type: 'PRIMARY',
  },
  {
    element: SettingScheduleRegularWorkUnitPage,
    path: ROUTES.SETTING_SCHEDULE_REGULAR(':id'),
    type: 'PRIMARY',
  },
  {
    element: SettingShiftWorkUnitPage,
    path: ROUTES.SETTING_SHIFT_WORK_UNIT(':id'),
    type: 'PRIMARY',
  },
  {
    element: AttendanceListPage,
    path: ROUTES.ATTENDANCE_LIST(),
    type: 'PRIMARY',
  },
  {
    element: SettingShiftAttendanceTimePage,
    path: ROUTES.SETTING_SHIFT_ATTENDANCE_TIME(':id'),
    type: 'PRIMARY',
  },
  {
    element: AttendanceHistoryPage,
    path: ROUTES.ATTENDANCE_HISTORY(),
    type: 'PRIMARY',
  },
  {
    element: AddWorkUnitManager,
    path: ROUTES.ADD_WORK_UNIT_MANGER(':id'),
    type: 'PRIMARY',
  },
];

export const publicRoutes: IRouteList[] = [
  {
    element: SignInPage,
    path: ROUTES.SIGN_IN(),
    type: 'FULL_PAGE',
  },
];
