import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { useBroadcastListPage } from '@/pages/broadcast/useBroadcastListPage.ts';
import AppTable, { type ITableColumn } from '@/components/AppTable.tsx';
import type { IResListBroadcast } from '@/types/response/IResListBroadcast.ts';
import AppPagination from '@/components/AppPagination.tsx';
import DateHelper from '@/lib/date-helper.ts';
import BroadcastStatusText from '@/components/BroadcastStatusText.tsx';
import { Button } from '@/components/ui/button.tsx';
import { CheckCircle, Clock, Info, Plus, Send, XCircle, type LucideIcon } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth.ts';
import { Link } from 'react-router-dom';
import { ROUTES } from '@/routes/routes.ts';
import type { BroadcastStatusTypeEnum } from '@/types/type/BroadcastStatusTypeEnum';
import type { ColorType } from '@/types/type/ColorType';
import CardSummaryCount from '@/components/CardSummaryCount';
import { Card, CardContent } from '@/components/ui/card';

export default function BroadcastListPage() {
  const page = useBroadcastListPage();
  const auth = useAuth();
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Broadcast WA',
    },
  ];

  const tableColumn: ITableColumn<IResListBroadcast>[] = [
    {
      headerTitle: 'Judul',
      component: (data) => <div>{data.title}</div>,
    },
    {
      headerTitle: 'Dibuat Oleh',
      component: (data) => (
        <div className={'flex gap-2 items-center'}>
          <img
            className={'h-8 w-8 rounded-full border-black border'}
            src={data.created_by_profile_picture}
            alt={data.title}
          />
          <div className={'grid'}>
            <div className={'font-semibold'}>{data?.created_by_name}</div>
            <p className={'text-xs '}>{DateHelper.toFormatDate(data.created_date, 'dd LLLL, yyyy - HH:mm')}</p>
          </div>
        </div>
      ),
    },
    {
      headerTitle: 'Cakupan',
      component: (e) => <div>{e.coverage_string}</div>,
    },
    {
      headerTitle: 'Status',
      component: (e) => <BroadcastStatusText status={e.status_enum} label={e.status_string} />,
    },
    {
      headerTitle: '',
      component: (e) => (
        <Link to={ROUTES.DETAIL_BROADCAST(e.id)}>
          <Button size={'icon'} variant={'outline'}>
            <Info />
          </Button>
        </Link>
      ),
    },
  ];

  function checkColorAndIconDetailed(status: BroadcastStatusTypeEnum): { color: ColorType; icon: LucideIcon } {
    switch (status) {
      case 'PENDING':
        return {
          color: 'yellow',
          icon: Clock,
        };

      case 'REJECT':
        return {
          color: 'red',
          icon: XCircle,
        };

      case 'READY_TO_SEND':
        return {
          color: 'blue',
          icon: CheckCircle,
        };

      case 'SENT':
        return {
          color: 'green',
          icon: Send,
        };

      default:
        return {
          color: 'gray',
          icon: Clock,
        };
    }
  }

  return (
    <PageContainer loading={page?.queryList?.isPending}>
      <div className={'flex items-center justify-between'}>
        <PageTitle title="Broadcast WA" breadcrumb={breadcrumbs} />
        {auth?.checkingPrivilege('CREATE_BROADCAST') && (
          <Link to={ROUTES.NEW_BROADCAST()}>
            <Button>
              <Plus />
              Buat pesan broadcast
            </Button>
          </Link>
        )}
      </div>
      <Card className="p-0">
        <CardContent className="p-4">
          <div className="grid gap-3 grid-cols-4">
            {(page.querySummaryCount?.data || []).map((e) => (
              <CardSummaryCount
                key={e.key}
                label={e?.label ? e?.label.toString() : ''}
                value={e.value}
                icon={checkColorAndIconDetailed(e.key).icon}
                color={checkColorAndIconDetailed(e.key).color}
              />
            ))}
          </div>
        </CardContent>
      </Card>
      <AppTable data={page.queryList?.data?.response_data || []} column={tableColumn} />
      {page.queryList?.data?.paginated_data && (
        <AppPagination
          onPaginationChange={(e) => page.handlePaginationChange(e)}
          dataPagination={page?.queryList?.data?.paginated_data}
        />
      )}
    </PageContainer>
  );
}
