import DateHelper from '@/lib/date-helper.ts';
import { HttpService } from '@/services/http.service.ts';
import ErrorService from '@/services/error.service.ts';
import { ENDPOINT } from '@/constants/endpoint.ts';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResOverview } from '@/types/response/IResOverview.tsx';

export class MasterDataRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async getOverview() {
    const date = new Date();
    const formatDate = DateHelper.toFormatDate(date, 'yyyy-MM-dd');
    try {
      const res: BaseResponse<IResOverview> = await this.httpService.GET(ENDPOINT.GET_OVERVIEW(formatDate));
      return res.data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }
}
