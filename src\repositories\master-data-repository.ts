import DateHelper from '@/lib/date-helper.ts';
import { HttpService } from '@/services/http.service.ts';
import ErrorService from '@/services/error.service.ts';
import { ENDPOINT } from '@/constants/endpoint.ts';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResOverview } from '@/types/response/IResOverview.tsx';
import type { IResListAgency } from '@/types/response/IResListAgency';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { IResListAllRole } from '@/types/response/IResListAllRole';
import type { IResListAllPrivileges } from '@/types/response/IResListAllPrivileges';
import type { IResRoleAccess } from '@/types/response/IResRoleAccess';
import type { IReqSettingRoleAccess } from '@/types/request/IReqSettingRoleAccess';
import type { ILabelValue } from '@/types/type/ILabelValue.ts';
import type { BroadcastCoverageTypeEnum } from '@/types/type/BroadcastStatusTypeEnum.ts';

export class MasterDataRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async listAllRole() {
    try {
      const res: BaseResponse<IResListAllRole[]> = await this.httpService.GET(ENDPOINT.LIST_ALL_ROLE());
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async listBroadcastCoverageType() {
    return await this.httpService
      .GET(ENDPOINT.LIST_BROADCAST_COVERAGE_TYPE())
      .then((res: BaseResponse<ILabelValue<BroadcastCoverageTypeEnum>[]>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async setRoleAccess(data: IReqSettingRoleAccess) {
    try {
      await this.httpService.POST(ENDPOINT.SETTING_ROLE_ACCESS(), data);
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async listAllPrivileges() {
    try {
      const res: BaseResponse<IResListAllPrivileges[]> = await this.httpService.GET(ENDPOINT.LIST_ALL_PRIVILEGES());
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async getRoleAccess() {
    try {
      const res: BaseResponse<IResRoleAccess[]> = await this.httpService.GET(ENDPOINT.LIST_ROLE_ACCESS());
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async getOverview() {
    const date = new Date();
    const formatDate = DateHelper.toFormatDate(date, 'yyyy-MM-dd');
    try {
      const res: BaseResponse<IResOverview> = await this.httpService.GET(ENDPOINT.GET_OVERVIEW(formatDate));
      return res.data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async getAgency() {
    try {
      const res: BaseResponse<IResListAgency[]> = await this.httpService.GET(ENDPOINT.LIST_AGENCY());
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async getWorkUnitByAgency(agencyId: string) {
    try {
      const res: BaseResponse<IResWorkUnit[]> = await this.httpService.GET(ENDPOINT.LIST_WORK_UNIT_AGENCY(agencyId));
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
    }
  }

  async getListAgencyAdmin() {
    return await this.httpService
      .GET(ENDPOINT.LIST_AGENCY_ADMIN())
      .then((res: BaseResponse<IResListAgency[]>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }
}
