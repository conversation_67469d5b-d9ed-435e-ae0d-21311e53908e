import { Client, StompConfig, type IMessage } from '@stomp/stompjs';
import { ENV } from '@/constants/env';
import type { IResSignIn } from '@/types/response/IResSignIn';

export interface WebSocketCallbacks {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  onLoginReceived?: (loginData: IResSignIn) => void;
}

export class WebSocketService {
  private client: Client | null = null;
  private currentSubscription: any = null;
  private callbacks: WebSocketCallbacks = {};
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000; // 3 seconds

  constructor(callbacks: WebSocketCallbacks = {}) {
    this.callbacks = callbacks;
  }

  /**
   * Initialize and connect to WebSocket
   */
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnected && this.client?.connected) {
        resolve();
        return;
      }

      // Disconnect existing connection if any
      this.disconnect();

      const wsUrl = ENV.WS_URL || 'ws://localhost:8080/ws';

      const stompConfig: StompConfig = {
        brokerURL: wsUrl,
        connectHeaders: {},
        debug: (str) => {
          if (ENV.NODE_ENV === 'DEV') {
            console.log('STOMP Debug:', str);
          }
        },
        reconnectDelay: this.reconnectDelay,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        onConnect: () => {
          console.log('WebSocket connected successfully');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.callbacks.onConnect?.();
          resolve();
        },
        onDisconnect: () => {
          console.log('WebSocket disconnected');
          this.isConnected = false;
          this.callbacks.onDisconnect?.();
        },
        onStompError: (frame) => {
          console.error('STOMP Error:', frame);
          this.callbacks.onError?.(frame);
          reject(new Error(`STOMP Error: ${frame.headers['message']}`));
        },
        onWebSocketError: (error) => {
          console.error('WebSocket Error:', error);
          this.callbacks.onError?.(error);
          reject(error);
        },
        onWebSocketClose: (event) => {
          console.log('WebSocket connection closed:', event);
          this.isConnected = false;

          // Attempt reconnection if not manually disconnected
          if (this.reconnectAttempts < this.maxReconnectAttempts && event.code !== 1000) {
            this.attemptReconnect();
          }
        },
      };

      this.client = new Client(stompConfig);

      try {
        this.client.activate();
      } catch (error) {
        console.error('Failed to activate WebSocket client:', error);
        reject(error);
      }
    });
  }

  /**
   * Subscribe to QR code topic for login notifications
   */
  public subscribeToQRCode(qrCode: string): void {
    if (!this.client?.connected) {
      console.warn('WebSocket not connected. Cannot subscribe to QR code topic.');
      return;
    }

    // Unsubscribe from previous topic if exists
    this.unsubscribeFromCurrentTopic();

    const topic = `/topic/${qrCode}`;
    console.log(`Subscribing to topic: ${topic}`);

    try {
      this.currentSubscription = this.client.subscribe(topic, (message: IMessage) => {
        try {
          const loginData: IResSignIn = JSON.parse(message.body);
          console.log('Received login data via WebSocket:', loginData);
          this.callbacks.onLoginReceived?.(loginData);
        } catch (error) {
          console.error('Failed to parse login data from WebSocket message:', error);
          this.callbacks.onError?.(error);
        }
      });

      console.log(`Successfully subscribed to topic: ${topic}`);
    } catch (error) {
      console.error('Failed to subscribe to QR code topic:', error);
      this.callbacks.onError?.(error);
    }
  }

  /**
   * Unsubscribe from current topic
   */
  public unsubscribeFromCurrentTopic(): void {
    if (this.currentSubscription) {
      try {
        this.currentSubscription.unsubscribe();
        console.log('Unsubscribed from current topic');
      } catch (error) {
        console.error('Error unsubscribing from topic:', error);
      } finally {
        this.currentSubscription = null;
      }
    }
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    this.unsubscribeFromCurrentTopic();

    if (this.client) {
      try {
        this.client.deactivate();
        console.log('WebSocket client deactivated');
      } catch (error) {
        console.error('Error deactivating WebSocket client:', error);
      } finally {
        this.client = null;
        this.isConnected = false;
      }
    }
  }

  /**
   * Check if WebSocket is connected
   */
  public isWebSocketConnected(): boolean {
    return this.isConnected && this.client?.connected === true;
  }

  /**
   * Update callbacks
   */
  public updateCallbacks(callbacks: WebSocketCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Attempt to reconnect to WebSocket
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached. Giving up.');
      return;
    }

    this.reconnectAttempts++;
    console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch((error) => {
        console.error('Reconnection failed:', error);
      });
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
    };
  }
}
