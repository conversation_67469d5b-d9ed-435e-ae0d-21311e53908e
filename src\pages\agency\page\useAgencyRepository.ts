import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResListAgency } from '@/types/response/IResListAgency';
import type { BaseResponse } from '@/types/response/IResModel';
import { useQuery } from '@tanstack/react-query';

export function useAgencyRepository() {
  const httpService = new HttpService();
  const errorService = new ErrorService();

  const listAgency = useQuery({
    queryKey: ['list_agency_page_and_filter'],
    queryFn: () =>
      httpService
        .GET(ENDPOINT.LIST_AGENCY())
        .then((res: BaseResponse<IResListAgency[]>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        }),
  });

  const listAgencyAdmin = useQuery({
    queryKey: ['list_agency_page_and_filter_admin'],
    queryFn: () =>
      httpService
        .GET(ENDPOINT.LIST_AGENCY_ADMIN())
        .then((res: BaseResponse<IResListAgency[]>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        }),
  });

  return {
    listAgency,
    listAgencyAdmin
  };
}
