import Label from '@/components/ui/Label.tsx';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils.ts';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';

interface IProps<T = any> {
  id: string;
  label?: string;
  required?: boolean;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: T;
  onValueChange?: (value: T) => void;
  options: ILabelValue<T>[];
  disabled?: boolean;
  dataTestId?: string;
  disableFormik?: boolean;
  className?: string;
  gridCols?: number;
  orientation?: 'horizontal' | 'vertical';
}

export default function InputRadioGroup<T = string>(props: IProps<T>) {
  const formik = !props.disableFormik ? useFormikContext<any>() : undefined;

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  const handleValueChange = (value: string) => {
    // Find the actual value from options
    const selectedOption = props.options.find((option) => String(option.value) === value);
    const actualValue = selectedOption ? selectedOption.value : value;

    if (props.onValueChange) {
      props.onValueChange(actualValue as T);
    }

    // Update formik field
    if (formik) {
      formik.setFieldValue(props.name, actualValue);
    }
  };

  const gridColsClass = props.gridCols ? `grid-cols-${props.gridCols}` : 'grid-cols-1';
  const isHorizontal = props.orientation === 'horizontal';

  return (
    <div className="grid">
      {props.label && <Label label={props.label} required={props.required} />}

      <RadioGroup
        data-testid={props.dataTestId}
        value={String(currentValue)}
        onValueChange={handleValueChange}
        disabled={props.disabled}
        className={cn('grid gap-2', isHorizontal ? gridColsClass : 'grid-cols-1', props.className)}
      >
        {props.options.map((option, index) => (
          <label
            key={index}
            className={cn(
              'flex items-center gap-3 border py-3 px-4 cursor-pointer rounded-sm transition-colors',
              'hover:bg-gray-50 dark:hover:bg-gray-800',
              String(currentValue) === String(option.value)
                ? 'border-primary bg-primary/5 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700',
              errorMessage ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : '',
              props.disabled ? 'opacity-50 cursor-not-allowed' : '',
            )}
          >
            <RadioGroupItem value={String(option.value)} id={`${props.id}-${index}`} disabled={props.disabled} />
            <div className="flex-1">{option.label}</div>
          </label>
        ))}
      </RadioGroup>

      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
