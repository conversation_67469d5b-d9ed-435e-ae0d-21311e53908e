import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ASSETS } from '@/constants/assets';
import { AlertCircle, CheckCircle, Clock, Download, Shield, Smartphone, Star, Users, Wifi } from 'lucide-react';
import { useState } from 'react';

export default function DownloadAppPage() {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = () => {
    setIsDownloading(true);

    setTimeout(() => {
      const link = document.createElement('a');
      link.href = ASSETS.APK;
      link.setAttribute('download', 'nucalale_1.0.4.apk');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setIsDownloading(false);
    }, 100);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-green-600 to-blue-800">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-24 h-24 bg-white rounded-2xl shadow-2xl flex items-center justify-center">
                <Smartphone className="w-12 h-12 text-blue-600" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              Nuca<span className="text-yellow-300">Lale</span>
            </h1>
            <p className="text-lg text-blue-200 max-w-2xl mx-auto">
              Download aplikasi Nucalale untuk pengalaman mobile yang lebih baik dan akses fitur lengkap di genggaman
              Anda
            </p>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/10 rounded-full"></div>
          <div className="absolute top-20 -left-10 w-32 h-32 bg-white/5 rounded-full"></div>
          <div className="absolute bottom-10 left-1/4 w-24 h-24 bg-white/10 rounded-full"></div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Download Section */}
          <div className="space-y-8">
            {/* Download Card */}
            <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-6">
                <CardTitle className="flex items-center justify-center gap-3 text-2xl">
                  <Download className="w-8 h-8 text-blue-600" />
                  Download APK
                </CardTitle>
                <p className="text-gray-600 mt-2">Dapatkan aplikasi Nucalale versi terbaru untuk Android</p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* App Info */}
                <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-600 rounded-xl flex items-center justify-center">
                        <Smartphone className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">Nucalale Mobile</h3>
                      </div>
                    </div>
                    <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                      Terbaru
                    </Badge>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-gray-900">4.8</div>
                      <div className="flex justify-center mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <div className="text-xs text-gray-600">Rating</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">50MB</div>
                      <div className="text-xs text-gray-600 mt-1">Ukuran</div>
                    </div>
                  </div>
                </div>

                {/* Download Button */}
                <Button
                  onClick={handleDownload}
                  disabled={isDownloading}
                  className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isDownloading ? (
                    <div className="flex items-center gap-3">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Mengunduh...
                    </div>
                  ) : (
                    <div className="flex items-center gap-3">
                      <Download className="w-6 h-6" />
                      Download APK (50MB)
                    </div>
                  )}
                </Button>

                {/* System Requirements */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Shield className="w-4 h-4 text-green-600" />
                    Persyaratan Sistem
                  </h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      Android 6.0 atau lebih tinggi
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      RAM minimal 2GB
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      Ruang penyimpanan 50MB
                    </div>
                    <div className="flex items-center gap-2">
                      <Wifi className="w-4 h-4 text-blue-500" />
                      Koneksi internet untuk fitur lengkap
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Features & Info */}
          <div className="space-y-8">
            {/* Features Card */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-xl">
                  <Star className="w-6 h-6 text-yellow-500" />
                  Fitur Unggulan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-start gap-4 p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Users className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Manajemen Tim</h4>
                      <p className="text-sm text-gray-600">Kelola tim dan karyawan dengan mudah</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors">
                    <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Absensi Real-time</h4>
                      <p className="text-sm text-gray-600">Pantau kehadiran karyawan secara langsung</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors">
                    <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Shield className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Keamanan Terjamin</h4>
                      <p className="text-sm text-gray-600">Data terenkripsi dan sistem keamanan berlapis</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-3 rounded-lg bg-orange-50 hover:bg-orange-100 transition-colors">
                    <div className="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Smartphone className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Interface Modern</h4>
                      <p className="text-sm text-gray-600">Desain yang intuitif dan mudah digunakan</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Installation Guide */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-xl">
                  <AlertCircle className="w-6 h-6 text-orange-500" />
                  Panduan Instalasi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                      1
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Download APK</p>
                      <p className="text-sm text-gray-600">Klik tombol download untuk mengunduh file APK</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                      2
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Izinkan Sumber Tidak Dikenal</p>
                      <p className="text-sm text-gray-600">
                        Aktifkan instalasi dari sumber tidak dikenal di pengaturan
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                      3
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Install Aplikasi</p>
                      <p className="text-sm text-gray-600">Buka file APK dan ikuti petunjuk instalasi</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                      ✓
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Siap Digunakan</p>
                      <p className="text-sm text-gray-600">Aplikasi siap digunakan dengan fitur lengkap</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Bottom Section - Additional Info */}
        <div className="mt-16 space-y-8">
          <Separator />

          {/* Security Notice */}
          <Card className="bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Shield className="w-6 h-6 text-amber-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-amber-900 mb-2">Keamanan & Privasi</h3>
                  <p className="text-amber-800 text-sm mb-3">
                    Aplikasi Nucalale telah melalui pemeriksaan keamanan yang ketat dan tidak mengandung malware. Data
                    Anda akan dienkripsi dan disimpan dengan aman.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="border-amber-300 text-amber-700 bg-amber-50">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Bebas Malware
                    </Badge>
                    <Badge variant="outline" className="border-amber-300 text-amber-700 bg-amber-50">
                      <Shield className="w-3 h-3 mr-1" />
                      Data Terenkripsi
                    </Badge>
                    <Badge variant="outline" className="border-amber-300 text-amber-700 bg-amber-50">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Teruji Keamanan
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Support Info */}
          <div className="text-center space-y-4">
            <h3 className="text-xl font-semibold text-gray-900">Butuh Bantuan?</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Tim support kami siap membantu Anda 24/7. Hubungi kami jika mengalami kesulitan dalam instalasi atau
              penggunaan aplikasi.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button variant="outline" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Hubungi Support
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                FAQ
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
