export interface IReqCreateEmployee {
  name: string;
  phone: string;
  email: string;
  checked?: boolean;
  nip: string;
  role: string;
  agency_id: string;
  work_unit_id: string;
  front_degree: string;
  back_degree: string;
  date_of_birth: string;
  religion: string;
  marital_status: string;
  gender: string;
  province_id: string;
  city_id: string;
  district_id: string;
  sub_district_id: string;
  address: string;
}
