import type { SubmissionStatusEnum, SubmissionTypeEnum } from "../type/enum-type";
export interface IResDetailSubmission {
  id: string;
  account_id: string;
  account_name: string;
  account_profile_picture: string;
  account_email: string;
  account_phone: string;
  account_nip: string;
  created_date: string;
  type: SubmissionTypeEnum;
  start: string;
  end: string;
  reason: string;
  document_url: string;
  status: SubmissionStatusEnum;
  approve_by: string;
  approve_date: string;
  approve_reject_by_name: string;
  approve_reject_by_profile_picture: string;
  approve_reject_by_id: string;
  approve_reject_reason: string;
  work_unit_id: string;
  work_unit_name: string;
  agency_name: string;
  agency_id: string;
}