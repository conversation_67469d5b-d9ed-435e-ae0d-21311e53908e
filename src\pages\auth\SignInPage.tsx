import InputText from '@/components/InputText.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ASSETS } from '@/constants/assets';
import useSignInPage from '@/pages/auth/useSignInPage.ts';
import { Form, FormikProvider } from 'formik';
import { ArrowRight, Clock, Eye, EyeOff, IdCard, Lock, Mail, Phone, Shield, Smartphone, Users } from 'lucide-react';
import { MdPerson } from 'react-icons/md';

export default function SignInPage() {
  const page = useSignInPage();

  const features = [
    {
      icon: Shield,
      title: 'Keamanan Terjamin',
      description: 'Sistem keamanan berlapis dengan enkripsi data',
    },
    {
      icon: Smartphone,
      title: 'Teknologi Modern',
      description: 'Validasi wajah dengan AI dan GPS tracking',
    },
    {
      icon: Clock,
      title: 'Real-time Monitoring',
      description: 'Pantau kehadiran secara langsung dan akurat',
    },
    {
      icon: Users,
      title: 'Multi-instansi',
      description: 'Mendukung berbagai instansi dan unit kerja',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <div className="grid lg:grid-cols-2 min-h-screen">
        <div
          className="relative bg-gradient-to-br bg-cover  lg:flex hidden flex-col justify-between p-8 text-white overflow-hidden"
          style={{ backgroundImage: `url(${ASSETS.IMG_LOGIN_1})` }}
        >
          <div className="absolute inset-0 opacity-10">
            <div
              className="absolute top-0 left-0 w-full h-full"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              }}
            ></div>
          </div>

          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                <img className="h-8" src={ASSETS.LG_BRAND} alt="Kabupaten Manggarai" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Nuca Lale</h1>
                <p className="text-blue-100 text-sm">Sistem Absensi Digital</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold mb-4">
                  Revolusi Digital untuk <br />
                  <span className="text-blue-200">Absensi PNS Modern</span>
                </h2>
                <p className="text-blue-100 text-lg leading-relaxed">
                  Bergabunglah dengan transformasi digital yang menghadirkan efisiensi, transparansi, dan akuntabilitas
                  dalam sistem kehadiran pegawai.
                </p>
              </div>

              {/* Features Grid */}
              <div className="grid grid-cols-2 gap-4 mt-8">
                {features.map((feature, index) => {
                  const IconComponent = feature.icon;
                  return (
                    <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                      <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center mb-3">
                        <IconComponent className="w-4 h-4" />
                      </div>
                      <h3 className="font-semibold text-sm mb-1">{feature.title}</h3>
                      <p className="text-blue-100 text-xs leading-relaxed">{feature.description}</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="relative z-10">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
              <p className="text-sm text-blue-100 italic">"Ama Lewang Ngger Peang, Cama Poe Ngger One"</p>
              <p className="text-xs text-blue-200 mt-1">
                Semangat gotong royong dalam membangun disiplin kerja yang lebih baik
              </p>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-20 right-20 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
        </div>

        {/* Right Side - Login Form */}
        <div className="flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Mobile Header */}
            <div className="lg:hidden text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <img className="h-8" src={ASSETS.LG_BRAND} alt="Kabupaten Manggarai" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Nuca Lale</h1>
                  <p className="text-gray-600 text-sm">Sistem Absensi Digital</p>
                </div>
              </div>
            </div>

            <Card className="  bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <Lock className="w-4 h-4 text-primary" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">Selamat Datang</h2>
                  </div>
                  <p className="text-gray-600">Masuk ke akun Anda untuk mengakses sistem absensi digital</p>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <FormikProvider value={page.formik}>
                  <Form onSubmit={(e) => e.preventDefault()}>
                    <div className="space-y-4">
                      {/* Login Input */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                          <div className="w-4 h-4 bg-gray-100 rounded flex items-center justify-center">
                            <Mail className="w-3 h-3 text-gray-600" />
                          </div>
                          Email / No HP / NIP
                        </label>
                        <InputText
                          id="data"
                          name="data"
                          placeholder="Masukkan email, nomor HP, atau NIP"
                          endIcon={<MdPerson className="text-gray-400" />}
                          required
                        />
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                          <div className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            <span>Email</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            <span>No HP</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <IdCard className="w-3 h-3" />
                            <span>NIP</span>
                          </div>
                        </div>
                      </div>

                      {/* Password Input */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                          <div className="w-4 h-4 bg-gray-100 rounded flex items-center justify-center">
                            <Lock className="w-3 h-3 text-gray-600" />
                          </div>
                          Password
                        </label>
                        <InputText
                          type={page.showPassword ? 'text' : 'password'}
                          id="password"
                          name="password"
                          placeholder="Masukkan password Anda"
                          autoComplete="current-password"
                          required
                          endIcon={
                            <button
                              type="button"
                              onClick={() => page.setShowPassword((e) => !e)}
                              className="cursor-pointer text-gray-400 hover:text-gray-600 transition-colors"
                            >
                              {page.showPassword ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                            </button>
                          }
                        />
                      </div>

                      <Button
                        loading={page.loading}
                        onClick={() => page.formik.handleSubmit()}
                        className="w-full h-12 "
                        disabled={page.loading}
                      >
                        {page.loading ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            Memproses...
                          </>
                        ) : (
                          <>
                            Masuk ke Sistem
                            <ArrowRight className="w-4 h-4" />
                          </>
                        )}
                      </Button>
                    </div>
                  </Form>
                </FormikProvider>

                {/* Security Notice */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <Shield className="w-4 h-4 text-gray-600 mt-0.5 flex-shrink-0" />
                    <div className="text-xs text-gray-600">
                      <p className="font-medium mb-1">Keamanan Data Terjamin</p>
                      <p>Sistem menggunakan enkripsi SSL dan autentikasi berlapis untuk melindungi informasi Anda.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Footer */}
            <div className="text-center mt-6 text-xs text-gray-500">
              <p>© 2024 Kabupaten Manggarai. Semua hak dilindungi.</p>
              <p className="mt-1">Sistem Absensi Digital PNS - Nuca Lale v2.0</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
