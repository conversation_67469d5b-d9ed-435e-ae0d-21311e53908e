import InputText from '@/components/InputText.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ASSETS } from '@/constants/assets';
import useSignInPage from '@/pages/auth/useSignInPage.ts';
import { Form, FormikProvider } from 'formik';
import { ArrowRight, Clock, Eye, EyeOff, IdCard, Lock, Mail, Phone, QrCode, Shield, Smartphone } from 'lucide-react';
import Qr from 'react-qr-code';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ENV } from '@/constants/env.ts';
import { Md<PERSON>erson } from 'react-icons/md';
import SignInInformation from './SignInInformation';

export default function SignInPage() {
  const page = useSignInPage();

  function alertDialogSignInQr() {
    return (
      <AlertDialog open={!!page?.dataMessageSignIn}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <div>
                Konfirmasi Masuk sebagai <strong>{page?.dataMessageSignIn?.account?.name || '-'}</strong>
              </div>
            </AlertDialogTitle>
            <AlertDialogDescription>
              Anda akan masuk ke sistem admin. Pastikan ini adalah tindakan yang Anda inginkan, karena aktivitas Anda
              akan dicatat dan memiliki akses penuh terhadap sistem.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => page.setDataMessageSignIn(undefined)}>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={page.onNextSignInQr}>Lanjutkan</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {alertDialogSignInQr()}
      <div className="grid lg:grid-cols-2 min-h-screen">
        <SignInInformation />
        {/* Right Side - Login Form */}
        <div className="flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Mobile Header */}
            <div className="lg:hidden text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <img className="h-8" src={ASSETS.LG_BRAND} alt="Kabupaten Manggarai" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Nuca Lale</h1>
                  <p className="text-gray-600 text-sm">Sistem Absensi Digital</p>
                </div>
              </div>
            </div>

            <Card className="  bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-6">
                <div className="space-y-2">
                  <div className="flex items-center  gap-2 mb-4">
                    <div className="w-8 h-8 bg-green-100 justify-center rounded-lg flex items-center ">
                      <Lock className="w-4 h-4 text-primary" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 text-start">Selamat Datang</h2>
                  </div>
                  <p className="text-gray-600 text-start">Masuk ke akun Anda untuk mengakses sistem absensi digital</p>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div>
                  <div className="grid gap-3">
                    {page.loginType !== 'QR' && (
                      <Card
                        onClick={() => page.onClickQr()}
                        className="hover:-translate-y-1 hover:border-primary cursor-pointer duration-500"
                      >
                        <CardContent>
                          <div className="flex gap-2 items-center justify-center">
                            <QrCode />
                            <div>Login dengan QR Code</div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                    {page.loginType !== 'EMAIL' && (
                      <Card
                        onClick={() => page.setLoginType('EMAIL')}
                        className="hover:-translate-y-1 hover:border-primary cursor-pointer duration-500"
                      >
                        <CardContent>
                          <div className="flex gap-2 items-center justify-center">
                            <Mail />
                            <div>Login dengan Email</div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
                {page.loginType === 'EMAIL' && (
                  <FormikProvider value={page.formik}>
                    <Form onSubmit={(e) => e.preventDefault()}>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <div className="w-4 h-4 bg-gray-100 rounded flex items-center justify-center">
                              <Mail className="w-3 h-3 text-gray-600" />
                            </div>
                            Email / No HP / NIP
                          </label>
                          <InputText
                            id="data"
                            name="data"
                            placeholder="Masukkan email, nomor HP, atau NIP"
                            endIcon={<MdPerson className="text-gray-400" />}
                            required
                          />
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                            <div className="flex items-center gap-1">
                              <Mail className="w-3 h-3" />
                              <span>Email</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="w-3 h-3" />
                              <span>No HP</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <IdCard className="w-3 h-3" />
                              <span>NIP</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <div className="w-4 h-4 bg-gray-100 rounded flex items-center justify-center">
                              <Lock className="w-3 h-3 text-gray-600" />
                            </div>
                            Password
                          </label>
                          <InputText
                            type={page.showPassword ? 'text' : 'password'}
                            id="password"
                            name="password"
                            placeholder="Masukkan password Anda"
                            autoComplete="current-password"
                            required
                            endIcon={
                              <button
                                type="button"
                                onClick={() => page.setShowPassword((e) => !e)}
                                className="cursor-pointer text-gray-400 hover:text-gray-600 transition-colors"
                              >
                                {page.showPassword ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                              </button>
                            }
                          />
                        </div>

                        <Button
                          loading={page.loading}
                          onClick={() => page.formik.handleSubmit()}
                          className="w-full h-12 "
                          disabled={page.loading}
                        >
                          {page.loading ? (
                            <>
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                              Memproses...
                            </>
                          ) : (
                            <>
                              Masuk ke Sistem
                              <ArrowRight className="w-4 h-4" />
                            </>
                          )}
                        </Button>
                      </div>
                    </Form>
                  </FormikProvider>
                )}
                {page.loginType === 'QR' && page?.qrValue && (
                  <div className="space-y-4">
                    <div className="relative border-2 border-green-200 p-8 rounded-xl bg-gradient-to-br from-green-50 to-indigo-50">
                      <div className="flex justify-center mb-4">
                        <div className="relative bg-white p-4 rounded-lg ">
                          <Qr
                            size={200}
                            style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
                            value={page.qrValue}
                            viewBox={`0 0 256 256`}
                          />

                          <div className="absolute -top-2 -right-2">
                            <div
                              className={`px-3 py-1 rounded-full text-xs font-bold  ${
                                page.count <= 10
                                  ? 'bg-red-500 text-white animate-pulse'
                                  : page.count <= 30
                                    ? 'bg-yellow-500 text-white'
                                    : 'bg-green-500 text-white'
                              }`}
                            >
                              {page.formatCountdown(page.count)}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="text-center space-y-3">
                        <div className="flex items-center justify-center gap-2 text-green-700">
                          <Smartphone className="w-5 h-5" />
                          <h3 className="font-semibold text-lg">Scan QR di Mobile App</h3>
                        </div>

                        <p className="text-green-600 text-sm leading-relaxed max-w-sm mx-auto">
                          Buka aplikasi mobile Nuca Lale dan scan QR code di atas untuk login secara otomatis
                        </p>
                      </div>
                    </div>

                    {/* Countdown Info */}
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-gray-600" />
                          <span className="text-sm font-medium text-gray-700">QR Code akan berubah dalam:</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span
                            className={`text-lg font-bold font-mono ${
                              page.count <= 10
                                ? 'text-red-600'
                                : page.count <= 30
                                  ? 'text-yellow-600'
                                  : 'text-green-600'
                            }`}
                          >
                            {page.formatCountdown(page.count)}
                          </span>
                          {page.count <= 10 && <div className="w-2 h-2 bg-red-500 rounded-full animate-ping"></div>}
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mt-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-1000 ${
                              page.count <= 10 ? 'bg-red-500' : page.count <= 30 ? 'bg-yellow-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${(page.count / 60) * 100}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>0:00</span>
                          <span>1:00</span>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <Button
                        onClick={page.generateNewQrCode}
                        variant="outline"
                        className="flex-1 flex items-center gap-2"
                      >
                        <QrCode className="w-4 h-4" />
                        Generate QR Baru
                      </Button>
                    </div>
                  </div>
                )}

                {/* Security Notice */}
                {page.loginType !== 'QR' && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <Shield className="w-4 h-4 text-gray-600 mt-0.5 flex-shrink-0" />
                      <div className="text-xs text-gray-600">
                        <p className="font-medium mb-1">Keamanan Data Terjamin</p>
                        <p>Sistem menggunakan enkripsi SSL dan autentikasi berlapis untuk melindungi informasi Anda.</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Footer */}
            <div className="text-center mt-6 text-xs text-gray-500">
              <p>© 2025 Kabupaten Manggarai. Semua hak dilindungi.</p>
              <p className="mt-1">Sistem Absensi Digital PNS - Nuca Lale V {ENV.VERSION}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
