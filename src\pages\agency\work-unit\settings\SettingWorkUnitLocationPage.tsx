import PageContainer from '@/components/PageContainer.tsx';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { ROUTES } from '@/routes/routes.ts';
import { useParams } from 'react-router-dom';
import PageTitle from '@/components/page-title.tsx';
import { useSettingWorkUnitLocationPage } from '@/pages/agency/work-unit/settings/useSettingWorkUnitLocationPage.ts';
import CardLoading from '@/components/CardLoading.tsx';
import { Card, CardContent, CardFooter } from '@/components/ui/card.tsx';
import { Building, MapPin, Plus } from 'lucide-react';
import { Fragment } from 'react';
import MapsSearch from '@/components/MapsSearch.tsx';
import InputText from '@/components/InputText.tsx';
import { FormikProvider } from 'formik';
import { Separator } from '@/components/ui/separator.tsx';
import { Button } from '@/components/ui/button.tsx';
import ConfirmationCheckBox from '@/components/ConfirmationCheckbox.tsx';

export default function SettingWorkUnitLocationPage() {
  const { id } = useParams();
  const page = useSettingWorkUnitLocationPage();
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Unit Kerja',
      path: ROUTES.LIST_WORK_UNIT(),
    },
    {
      label: page?.queryDetail?.data?.name || '',
      path: ROUTES.DETAIL_WORK_UNIT(id || ''),
    },
    {
      label: 'Kelola lokasi unit kerja',
    },
  ];
  return (
    <PageContainer>
      <PageTitle title={'Kelola lokasi unit kerja'} breadcrumb={breadcrumbs} />
      {page?.queryDetail?.isPending ? (
        <CardLoading />
      ) : (
        <FormikProvider value={page.formik}>
          <Card>
            <CardContent>
              <div className={'font-semibold capitalize text-2xl'}>{page?.queryDetail?.data?.name}</div>
              <div className={'flex gap-2 items-center mt-2'}>
                <Building size={20} />
                {page?.queryDetail?.data?.agency_name || '-'}
              </div>
            </CardContent>
          </Card>
          <Card>
            {Array.isArray(page.formik.values.data) &&
              page.formik.values.data.map((item, i) => (
                <Fragment key={i}>
                  <CardContent>
                    <p className={'font-semibold text-xl flex items-center gap-3 mb-4'}>
                      <MapPin />
                      Lokasi ke {i + 1}
                    </p>
                    <div className={'grid grid-cols-2 gap-3 mb-4'}>
                      <InputText
                        required
                        name={`data[${i}].name`}
                        label="Nama lokasi"
                        placeholder="Masukan nama lokasi"
                      />
                      <InputText
                        required
                        name={`data[${i}].radius`}
                        label="Radius lokasi"
                        placeholder="Masukan radius lokasi"
                        type={'number'}
                      />
                    </div>
                    <MapsSearch
                      radius={item.radius}
                      value={{ lng: item?.lng, lat: item?.lat }}
                      onChange={(e) => {
                        page.formik.setFieldValue(`data[${i}].lat`, e.lat);
                        page.formik.setFieldValue(`data[${i}].lng`, e.lng);
                      }}
                    />
                  </CardContent>
                </Fragment>
              ))}
            <CardContent>
              <div className={'flex items-center justify-end'}>
                <Button onClick={page.onClickAddLocation}>
                  <Plus />
                  Tambah Lokasi
                </Button>
              </div>
            </CardContent>
            <Separator />

            <CardFooter>
              <div className={'w-full grid gap-4'}>
                <ConfirmationCheckBox
                  label={'Konfirmasi'}
                  checked={page.formik.values.checked}
                  description={'Konfirmasi untuk mengelola data lokasi'}
                  onCheckedChange={(e) => page.formik.setFieldValue('checked', e)}
                />
                <Button
                  loading={page.mutationSubmit.isPending}
                  disabled={!page.formik.values.checked}
                  onClick={() => page.formik.handleSubmit()}
                  className={'w-full'}
                >
                  KIRIM
                </Button>
              </div>
            </CardFooter>
          </Card>
        </FormikProvider>
      )}
    </PageContainer>
  );
}
