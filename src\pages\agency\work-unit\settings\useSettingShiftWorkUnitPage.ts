import { WorkUnitRepository } from '@/repositories/work-unit-repositories';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';
import type { IReqSettingShiftWorkUnit } from '@/types/request/IReqSettingShiftWorkUnit.ts';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { normalizeTime } from '@/lib/utils.ts';
import { useUi } from '@/hooks/useUi.ts';
import { useState } from 'react';

export function useSettingShiftWorkUnit() {
  const { id } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const { toast } = useUi();
  const navigate = useNavigate();

  const [loadingDeleteId, setLoadingDeleteId] = useState<string | undefined>(undefined);

  const validationSchema = Yup.object({
    checked: Yup.boolean(),
    data: Yup.array()
      .of(
        Yup.object().shape({
          name: Yup.string().required('Nama shift wajib diisi'),
          start_time: Yup.string().required('Jam mulai wajib diisi'),
          end_time: Yup.string().required('Jam selesai wajib diisi'),
          code: Yup.string().required('Kode wajib diisi'),
        }),
      )
      .min(1, 'Minimal 1 shift diperlukan'),
  });
  const initValue: { data: IReqSettingShiftWorkUnit[]; checked?: boolean } = {
    checked: false,
    data: [
      {
        name: '',
        start_time: '',
        end_time: '',
        code: '',
      },
    ],
  };

  const mutationSubmit = useMutation({
    mutationFn: (e: IReqSettingShiftWorkUnit[]) =>
      workUnitRepository.settingShiftWorkUnit(id || '', e).then(() => {
        navigate(-1);
        toast.success('Shift kerja berhasil diubah');
      }),
  });

  const formik = useFormik({
    initialValues: initValue,
    validationSchema: validationSchema,
    onSubmit: (e) => {
      const data: IReqSettingShiftWorkUnit[] = e.data.map((item) => {
        return {
          name: item.name,
          code: item.code,
          start_time: normalizeTime(item.start_time),
          end_time: normalizeTime(item.end_time),
          id: item.id,
        };
      });
      mutationSubmit.mutate(data);
    },
  });

  const queryList = useQuery({
    queryKey: ['list_shift_work_unit', id],
    queryFn: () =>
      workUnitRepository.getListShift(id || '').then((res) => {
        if (res.length > 0) {
          const data: IReqSettingShiftWorkUnit[] = res.map((e) => {
            return {
              name: e.name,
              end_time: e.end_time,
              start_time: e.start_time,
              id: e.id,
              code: e.code,
            };
          });
          formik.setFieldValue('data', data);
        }
        return res;
      }),
  });

  async function onDelete(index: number, id?: string) {
    setLoadingDeleteId(id);
    try {
      if (id) {
        await workUnitRepository.deleteShift(id);
      }

      const updatedData = [...formik.values.data];
      updatedData.splice(index, 1);
      await formik.setFieldValue('data', updatedData);
    } finally {
      setLoadingDeleteId(undefined);
    }
  }

  return { queryList, formik, mutationSubmit, onDelete, loadingDeleteId, setLoadingDeleteId };
}
