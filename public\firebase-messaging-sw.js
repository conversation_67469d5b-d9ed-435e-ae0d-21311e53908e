// public/firebase-messaging-sw.js

importScripts('https://www.gstatic.com/firebasejs/10.12.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.2/firebase-messaging-compat.js');

const firebaseConfig = {
  apiKey: 'AIzaSyDAgGSyXQ8Jilifz8xZmwZfL7wiekH02Zg',
  authDomain: 'nuca-lale-e1f0a.firebaseapp.com',
  projectId: 'nuca-lale-e1f0a',
  storageBucket: 'nuca-lale-e1f0a.firebasestorage.app',
  messagingSenderId: '1028184975159',
  appId: '1:1028184975159:web:d602597d0c025c6330dcb2',
  measurementId: 'G-CM0FMG1MD8',
};
firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
  const { title, body } = payload.notification;

  const notificationOptions = {
    body,
    icon: '/firebase-logo.png', // ganti dengan icon kamu
  };

  self.registration.showNotification(title, notificationOptions);
});
