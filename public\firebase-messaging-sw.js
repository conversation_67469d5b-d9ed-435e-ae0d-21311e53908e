// public/firebase-messaging-sw.js

importScripts('https://www.gstatic.com/firebasejs/10.12.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.2/firebase-messaging-compat.js');

const firebaseConfig = {
  apiKey: 'AIzaSyCo2CHk6A5BQj4ejQiGv3aGt0fx0T_6Hh4',
  authDomain: 'nuca-lale.firebaseapp.com',
  projectId: 'nuca-lale',
  storageBucket: 'nuca-lale.firebasestorage.app',
  messagingSenderId: '25176508765',
  appId: '1:25176508765:web:bfff8105d3c5175ddd00b4',
  measurementId: 'G-PB952T5BP5',
};
firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
  const { title, body } = payload.notification;

  const notificationOptions = {
    body,
    icon: '/firebase-logo.png', // ganti dengan icon kamu
  };

  self.registration.showNotification(title, notificationOptions);
});
