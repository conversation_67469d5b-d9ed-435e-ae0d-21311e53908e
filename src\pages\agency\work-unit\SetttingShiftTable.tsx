import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { format, startOfWeek } from 'date-fns';
import { CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import { useSettingShiftTablePage } from './useSettingShiftTablePage';

export default function SettingShiftTable() {
  const page = useSettingShiftTablePage();

  
  return (
    <PageContainer size="base">
      <PageTitle title="Setting Shift" />

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Jadwal Shift Mingguan</CardTitle>

            <div className="flex items-center gap-2">
              <Button variant="outline" className="flex items-center gap-2" onClick={page.generateFairShiftSchedule}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-wand-2"
                >
                  <path d="m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z" />
                  <path d="m14 7 3 3" />
                  <path d="M5 6v4" />
                  <path d="M19 14v4" />
                  <path d="M10 2v2" />
                  <path d="M7 8H3" />
                  <path d="M21 16h-4" />
                  <path d="M11 3h2" />
                </svg>
                Generate Jadwal Adil
              </Button>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    <span>
                      {format(page.currentWeekStart, 'd MMM')} -{' '}
                      {format(page.daysOfWeek[page.daysOfWeek.length - 1], 'd MMM yyyy')}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    mode="single"
                    selected={page.selectedDate}
                    onSelect={(date) => {
                      if (date) {
                        page.setSelectedDate(date);
                        page.setCurrentWeekStart(startOfWeek(date, { weekStartsOn: 1 }));
                      }
                    }}
                  />
                </PopoverContent>
              </Popover>

              <div className="flex items-center gap-1">
                <Button variant="outline" size="icon" onClick={page.goToPreviousWeek}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={page.goToNextWeek}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <Separator />
        <CardContent>
          <div className="relative overflow-x-auto">
            <div className="flex items-center gap-2 mb-3">
              {page.dataShift.map((item, i) => (
                <div key={i} className="bg-white py-1 px-3 border rounded-lg">
                  {item.name} ({item.start_time} - {item.end_time})
                </div>
              ))}
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader className="bg-muted/50 sticky top-0 z-10">
                  <TableRow>
                    <TableHead className="sticky left-0 z-20 bg-muted/50 w-[200px]">Pegawai</TableHead>
                    {page.daysOfWeek.map((day) => (
                      <TableHead key={day.toString()} className="text-center min-w-[120px]">
                        <div className="font-medium">{page.formatDay(day)}</div>
                        <div className="text-xs text-muted-foreground">{page.formatDate(day)}</div>
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...page.dataEmployee].map((employee) => (
                    <TableRow key={employee.account_id}>
                      <TableCell className="font-medium sticky left-0 bg-white z-10">{employee.name}</TableCell>
                      {page.daysOfWeek.map((day) => {
                        const dayKey = format(day, 'yyyy-MM-dd');
                        const employeeShift = page.getEmployeeShift(employee.account_id, day);

                        return (
                          <TableCell key={`${employee.account_id}-${dayKey}`} className="p-1 text-center">
                            <Select
                              value={employeeShift}
                              onValueChange={(value) => {
                                // Update jadwal ketika nilai berubah
                                const newSchedule = { ...page.shiftSchedule };
                                if (!newSchedule[employee.account_id]) {
                                  newSchedule[employee.account_id] = {};
                                }
                                newSchedule[employee.account_id][dayKey] = value;
                                page.setShiftSchedule(newSchedule);
                              }}
                            >
                              <SelectTrigger
                                className={cn(
                                  'w-full h-9 px-2',
                                  // Default putih standar
                                  'bg-white',
                                  // Warna berdasarkan value
                                  {
                                    'bg-red-50 border-red-200': employeeShift === 'FREE',
                                    'bg-blue-50 border-blue-200': page.dataShift[0]?.id.toString() === employeeShift,
                                    'bg-yellow-50 border-yellow-200':
                                      page.dataShift[1]?.id.toString() === employeeShift,
                                    'bg-purple-50 border-purple-200':
                                      page.dataShift[2]?.id.toString() === employeeShift,
                                    'bg-green-50 border-green-200': page.dataShift[3]?.id.toString() === employeeShift,
                                    'bg-orange-50 border-orange-200':
                                      page.dataShift[4]?.id.toString() === employeeShift,
                                  },
                                )}
                              >
                                <SelectValue placeholder="Pilih shift" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="FREE" className="text-red-500">
                                  Libur
                                </SelectItem>
                                {page.dataShift.map((shift, index) => (
                                  <SelectItem
                                    key={shift.id}
                                    value={shift.id.toString()}
                                    className={cn(
                                      index === 0 && 'text-blue-600',
                                      index === 1 && 'text-yellow-600',
                                      index === 2 && 'text-purple-600',
                                      index === 3 && 'text-green-600',
                                      index === 4 && 'text-orange-600',
                                    )}
                                  >
                                    {shift.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold ">Ringkuman Hari Kerja dan Libur</h2>
        </CardHeader>
        <Separator />
        <CardContent>
          <div className="">
            {page.dataEmployee.map((employee) => {
              const { workDays, freeDays } = page.getEmployeeWorkDays(employee.account_id);

              return (
                <div key={employee.account_id} className="flex justify-between items-center py-1 border-b">
                  <span className="font-medium">{employee.name}</span>
                  <div className="flex gap-3">
                    <span className="text-sm bg-blue-50 text-blue-700 px-2 py-1 rounded">{workDays} hari kerja</span>
                    <span className="text-sm bg-red-50 text-red-700 px-2 py-1 rounded">{freeDays} hari libur</span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Jumlah Karyawan per Shift</h2>
        </CardHeader>
        <Separator />
        <CardContent>
          <div className="grid gap-4">
            {page.daysOfWeek.map((day) => {
              const dayKey = format(day, 'yyyy-MM-dd');
              const dayName = page.formatDay(day);
              const dateStr = page.formatDate(day);

              const shiftCounts = page.getShiftCountsForDay(day);

              return (
                <div key={dayKey} className="border rounded-md p-3 bg-muted">
                  <div className="font-medium mb-2">
                    {dayName}, {dateStr}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span>Libur: {shiftCounts['FREE']} orang</span>
                    </div>

                    {page.dataShift.map((shift) => {
                      const shiftId = shift.id.toString();
                      const count = shiftCounts[shiftId] || 0;

                      return (
                        <div key={shiftId} className="flex items-center  gap-2">
                          <div className={`w-3 h-3 rounded-full bg-blue-500`}></div>
                          <span>
                            {shift.name}: {count} orang
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
      <div className="flex justify-end gap-2">
        <Button variant="outline">Batal</Button>
        <Button>Simpan Perubahan</Button>
      </div>
    </PageContainer>
  );
}
