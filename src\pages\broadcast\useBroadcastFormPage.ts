import { useUi } from '@/hooks/useUi';
import { BroadcastRepository } from '@/repositories/broadcast-repository';
import { MasterDataRepository } from '@/repositories/master-data-repository.ts';
import type { IReqBroadcastForm } from '@/types/request/IReqBroadcastForm.ts';
import type { BroadcastCoverageTypeEnum } from '@/types/type/BroadcastStatusTypeEnum.ts';
import type { ILabelValue } from '@/types/type/ILabelValue.ts';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
export default function useBroadcastFormPage() {
  const masterDataRepository = new MasterDataRepository();
  const broadcastRepository = new BroadcastRepository();

  const navigate = useNavigate();

  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedAgencyId, setSelectedAgencyId] = useState<string>('');
  const [checked, setChecked] = useState<boolean>(false);

  const { toast } = useUi();

  const initForm: IReqBroadcastForm = {
    title: '',
    account_ids: [],
    body: '',
    agency_id: [],
    coverage_type: undefined,
    work_unit_id: [],
  };

  const validationSchema = Yup.object({
    title: Yup.string().required('Title wajib diisi').max(200, 'Maksimal 200 karakter'),
    body: Yup.string().required('Body wajib diisi'),
    coverage_type: Yup.mixed<BroadcastCoverageTypeEnum>()
      .oneOf(['ALL', 'AGENCY', 'WORK_UNIT'], 'Coverage type tidak valid')
      .required('Coverage type wajib diisi'),
    account_ids: Yup.array(),
    work_unit_id: Yup.array()
      .of(Yup.string())
      .when('coverage_type', {
        is: 'WORK_UNIT',
        then: (schema) => schema.min(1, 'Work unit wajib dipilih'),
        otherwise: (schema) => schema.notRequired(),
      }),
    agency_id: Yup.array()
      .of(Yup.string())
      .when('coverage_type', {
        is: 'AGENCY',
        then: (schema) => schema.min(1, 'Agency wajib dipilih'),
        otherwise: (schema) => schema.notRequired(),
      }),
  });

  const mutationCreate = useMutation({
    mutationFn: async (data: IReqBroadcastForm) =>
      await broadcastRepository.createBroadcast(data).then(() => {
        toast.success('Broadcast berhasil dibuat');
        navigate(-1);
      }),
  });

  const formik = useFormik({
    initialValues: initForm,
    validationSchema: validationSchema,
    onSubmit: (e) => mutationCreate.mutate(e),
  });

  const queryType = useQuery({
    queryKey: ['list_broadcast_coverage_type'],
    queryFn: async () => await masterDataRepository.listBroadcastCoverageType(),
  });

  const queryAgency = useQuery({
    queryKey: ['list_agency'],
    enabled: formik.values.coverage_type === 'AGENCY' || formik.values.coverage_type === 'WORK_UNIT',
    queryFn: async () => await masterDataRepository.getListAgencyAdmin(),
  });

  const queryWorkUnit = useQuery({
    queryKey: ['list_work_unit_id'],
    enabled: !!selectedAgencyId,
    queryFn: async () => await masterDataRepository.getWorkUnitByAgency(selectedAgencyId),
  });

  const dataListType: ILabelValue<BroadcastCoverageTypeEnum>[] = queryType?.data || [];
  const dataAgency = queryAgency.data || [];
  const dataWorkUnit = queryWorkUnit.data || [];

  const filteredAgency =
    searchValue && formik.values.coverage_type === 'AGENCY'
      ? dataAgency.filter((agency) => agency.name.toLowerCase().includes(searchValue.toLowerCase()))
      : dataAgency;

  const filteredWorkUnit =
    searchValue && formik.values.coverage_type === 'WORK_UNIT'
      ? dataWorkUnit.filter((workUnit) => workUnit.name.toLowerCase().includes(searchValue.toLowerCase()))
      : dataWorkUnit;

  useEffect(() => {
    setSearchValue('');
  }, [formik.values.coverage_type]);

  useEffect(() => {
    formik.setFieldValue('work_unit_id', []);
  }, [selectedAgencyId]);

  function onChangeAgency(agencyId: string) {
    const data = formik.values.agency_id || [];

    if (data.includes(agencyId)) {
      formik.setFieldValue(
        'agency_id',
        data.filter((v) => v !== agencyId),
      );
    } else {
      formik.setFieldValue('agency_id', [...data, agencyId]);
    }
  }

  function onChangeWorkUnit(workUnitId: string) {
    const data = formik.values.work_unit_id || [];

    if (data.includes(workUnitId)) {
      formik.setFieldValue(
        'work_unit_id',
        data.filter((v) => v !== workUnitId),
      );
    } else {
      formik.setFieldValue('work_unit_id', [...data, workUnitId]);
    }
  }

  function checkValidButton(): boolean {
    const values = formik.values;
    if (!values.title.trim()) return false;
    if (!values.body.trim()) return false;
    if (!values.coverage_type) return false;
    if (values.coverage_type === 'AGENCY' && values.agency_id.length === 0) return false;
    if (values.coverage_type === 'WORK_UNIT' && values.work_unit_id.length === 0) return false;
    return checked;
  }

  return {
    dataListType,
    formik,
    filteredAgency,
    onChangeAgency,
    setSearchValue,
    searchValue,
    selectedAgencyId,
    setSelectedAgencyId,
    queryWorkUnit,
    onChangeWorkUnit,
    filteredWorkUnit,
    checked,
    setChecked,
    checkValidButton,
    mutationCreate,
  };
}
