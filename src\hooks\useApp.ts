import { messaging } from '@/configs/firebase';
import { useAuth } from '@/hooks/use-auth.ts';
import { ROUTES } from '@/routes/routes.ts';
import { getToken } from 'firebase/messaging';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export function useApp() {
  const auth = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const { VITE_APP_VAPID_KEY } = import.meta.env;

  async function requestPermission() {
    const permission = await Notification.requestPermission();

    if (permission === 'granted') {
      const token = await getToken(messaging, {
        vapidKey: VITE_APP_VAPID_KEY,
      });
      auth.saveFcmToken(token);
    }
  }

  useEffect(() => {
    requestPermission().then();
  }, []);

  useEffect(() => {
    if (location.pathname !== ROUTES.SIGN_IN()) {
      if (!auth.token) {
        navigate(ROUTES.SIGN_IN());
      }
    }
  }, [location.pathname]);
  return {};
}
