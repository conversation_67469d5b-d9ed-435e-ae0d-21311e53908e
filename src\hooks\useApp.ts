import { useAuth } from '@/hooks/use-auth.ts';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from '@/routes/routes.ts';

export function useApp() {
  const auth = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  useEffect(() => {
    if (location.pathname !== ROUTES.SIGN_IN()) {
      if (!auth.token) {
        navigate(ROUTES.SIGN_IN());
      }
    }
  }, [location.pathname]);
  return {};
}
