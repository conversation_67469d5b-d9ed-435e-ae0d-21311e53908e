import { SubmissionRepository } from '@/repositories/submission-repository';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';

export function useDetailSubmissionPage() {
  const submissionRepository = new SubmissionRepository();
  const { id } = useParams();

  const queryDetail = useQuery({
    queryKey: ['detail_submission'],
    enabled: !!id,
    queryFn: async () => await submissionRepository.detailSubmission(id || ''),
  });

  return {queryDetail};
}
