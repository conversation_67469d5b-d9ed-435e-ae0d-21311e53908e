# Form Work Unit - Shift Management

## Overview
Form Work Unit telah diupdate untuk mendukung dua tipe penjadwalan:
1. **REGULAR** - <PERSON><PERSON><PERSON> absensi harian tradisional
2. **SHIFT** - Sistem shift kerja dengan jadwal absensi per shift

## Struktur Data

### Tipe REGULAR
```json
{
  "name": "Unit Kerja Regular",
  "description": "Deskripsi unit kerja",
  "agency_id": "47AABDD278A94654A53265D2940A877D",
  "type": "REGULAR",
  "timetable": [
    {
      "day": 0,
      "name": "Clock In",
      "start_time": "08:00:00",
      "end_time": "08:30:00"
    },
    {
      "day": 0,
      "name": "Clock Out",
      "start_time": "17:00:00",
      "end_time": "17:30:00"
    }
  ],
  "location": [
    {
      "lat": -8.600418793478012,
      "lng": 120.46767622232439,
      "radius": 200,
      "name": "kantor"
    }
  ]
}
```

### Tipe SHIFT
```json
{
  "name": "Unit Kerja Shift",
  "description": "Deskripsi unit kerja dengan shift",
  "agency_id": "47AABDD278A94654A53265D2940A877D",
  "type": "SHIFT",
  "timetable": [],
  "shifts": [
    {
      "name": "Shift Pagi",
      "start_time": "07:00:00",
      "end_time": "15:00:00",
      "time_tables": [
        {
          "name": "Jam Masuk",
          "start_time": "07:00:00",
          "end_time": "07:30:00"
        },
        {
          "name": "Jam Pulang",
          "start_time": "15:00:00",
          "end_time": "15:30:00"
        }
      ]
    },
    {
      "name": "Shift Siang",
      "start_time": "15:00:00",
      "end_time": "23:00:00",
      "time_tables": [
        {
          "name": "Jam Masuk",
          "start_time": "15:00:00",
          "end_time": "15:30:00"
        },
        {
          "name": "Jam Pulang",
          "start_time": "23:00:00",
          "end_time": "23:30:00"
        }
      ]
    }
  ],
  "location": [
    {
      "lat": -8.600418793478012,
      "lng": 120.46767622232439,
      "radius": 200,
      "name": "kantor"
    }
  ]
}
```

## Fitur Baru

### 1. Pemilihan Tipe Penjadwalan
- Radio button untuk memilih antara REGULAR atau SHIFT
- Form akan berubah secara dinamis berdasarkan pilihan

### 2. Form Shift Management
- Tambah/hapus shift
- Konfigurasi waktu mulai dan selesai shift
- Tambah/hapus jadwal absensi per shift
- Validasi waktu yang komprehensif

### 3. Validasi
- Validasi format waktu (HH:MM:SS untuk API)
- Validasi logika waktu (end_time > start_time)
- Validasi minimal 1 shift untuk tipe SHIFT
- Validasi minimal 1 jadwal absensi per shift

### 4. Preview Data
- Tombol "Preview Data" untuk melihat struktur JSON yang akan dikirim
- Data ditampilkan di console browser

## Cara Penggunaan

### Membuat Unit Kerja Regular
1. Pilih "Regular" pada tipe penjadwalan
2. Isi informasi dasar unit kerja
3. Konfigurasi jadwal absensi harian
4. Set lokasi dan radius
5. Submit form

### Membuat Unit Kerja Shift
1. Pilih "Shift" pada tipe penjadwalan
2. Isi informasi dasar unit kerja
3. Tambah shift sesuai kebutuhan
4. Untuk setiap shift:
   - Set nama shift
   - Set waktu mulai dan selesai shift
   - Tambah jadwal absensi (minimal Jam Masuk dan Jam Pulang)
5. Set lokasi dan radius
6. Submit form

## Komponen yang Dimodifikasi

### 1. `IReqCreateWorkUnit.ts`
- Tambah field `type: 'REGULAR' | 'SHIFT'`
- Tambah interface `IShift` dan `ITimeTable`
- Tambah field `shifts?: IShift[]`

### 2. `useFormWorkUnit.ts`
- Update initial values dengan field `type` dan `shifts`
- Update validation schema untuk mendukung conditional validation
- Update onSubmit untuk handle data shift
- Tambah useEffect untuk auto-initialize shifts

### 3. `FormWorkUnit.tsx`
- Tambah fungsi `renderShiftForm()` dan `renderRegularForm()`
- Update `ScheduleForm()` untuk render conditional
- Tambah preview data functionality
- Tambah informasi visual untuk tipe yang dipilih

## Testing

Gunakan tombol "Preview Data" untuk melihat struktur JSON yang akan dikirim ke API. Data akan ditampilkan di browser console dengan format yang sesuai dengan requirement.

## API Endpoint

Data akan dikirim ke endpoint yang sama dengan struktur yang berbeda berdasarkan tipe:
- `POST /api/work-unit` dengan payload sesuai tipe yang dipilih
