import PageContainer from '@/components/PageContainer';
import TopBar from '@/components/TopBar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft } from 'lucide-react';

export default function SettingPage() {
  return (
    <div>
      <TopBar full showBrand />
      <PageContainer>
        <div className="flex w-full gap-3">
          <Card className="py-0 gap-0">
            <CardContent className="py-2">
              <div className="flex gap-1 items-center">
                <Button size={'default'} variant={'ghost'}>
                  <ArrowLeft />
                  Kembali
                </Button>
              </div>
            </CardContent>
            <Separator className="py-0" />
            <CardContent className="py-3">
              <div>SIDEBAR</div>
            </CardContent>
          </Card>
          <Card className="flex-1 py-0 gap-0">
            <CardContent className="py-4">
              <div className="flex gap-1 text-3xl  items-center">Pengaturan</div>
            </CardContent>
            <CardContent className="py-3">
              <div>SIDEBAR</div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </div>
  );
}
