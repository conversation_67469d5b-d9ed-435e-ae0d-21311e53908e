import type { SubmissionStatusEnum, SubmissionTypeEnum } from '@/types/type/enum-type.ts';

export interface IResListSubmission {
  id: string;
  type: SubmissionTypeEnum;
  type_string: string;
  status: SubmissionStatusEnum;
  status_string: string;
  created_date: Date;
  start_date: Date;
  end_date: Date;
  work_unit_id: string;
  work_unit_name: string;
  agency_id: string;
  agency_name: string;
  account_id: string;
  account_name: string;
  account_profile_picture: string;
  reason: string;
}
