import type { IReqSignIn } from '@/types/request/IReqSignIn.ts';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useState } from 'react';
import { useAuth } from '@/hooks/use-auth.ts';
import { ENV } from '@/constants/env.ts';
import type { LoginType } from '@/enums/page-type';
import { generateUniqueCode } from '@/lib/utils';

export default function useSignInPage() {
  const auth = useAuth();
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [loginType, setLoginType] = useState<LoginType | undefined>(undefined);
  const [qrValue, setQrValue] = useState<string | undefined>();
  const initValue: IReqSignIn = {
    data: ENV.NODE_ENV === 'DEV' ? '<EMAIL>' : '',
    password: ENV.NODE_ENV === 'DEV' ? 'admin' : '',
  };
  const validationSchema = yup.object().shape({
    data: yup.string().required(),
    password: yup.string().required(),
  });
  const formik = useFormik({
    initialValues: initValue,
    validationSchema: validationSchema,
    onSubmit: (e) => auth.loginAction(e, setLoading),
  });

  function onClickQr() {
    const code = generateUniqueCode();
    setLoginType('QR');
    alert(code);
    setQrValue(code);
  }

  return { formik, showPassword, setShowPassword, loading, setLoginType, loginType, onClickQr, qrValue };
}
