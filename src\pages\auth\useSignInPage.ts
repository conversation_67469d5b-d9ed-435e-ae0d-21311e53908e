import type { IReqSignIn } from '@/types/request/IReqSignIn.ts';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/hooks/use-auth.ts';
import { ENV } from '@/constants/env.ts';
import type { LoginType } from '@/enums/page-type';
import { generateUniqueCode } from '@/lib/utils';
import { getToken } from 'firebase/messaging';
import { messaging, onMessageListener } from '@/configs/firebase';
import type { IMessageSignInWithQr } from '@/types/response/IResSignIn';

export default function useSignInPage() {
  const { VITE_APP_VAPID_KEY } = import.meta.env;
  const auth = useAuth();
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [loginType, setLoginType] = useState<LoginType | undefined>(undefined);
  const [qrValue, setQrValue] = useState<string | undefined>();
  const [count, setCount] = useState<number>(60);
  const [isQrActive, setIsQrActive] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const [fcmToken, setFcmToken] = useState<string | undefined>(auth?.fcmToken);
  const [dataMessageSignIn, setDataMessageSignIn] = useState<IMessageSignInWithQr | undefined>();

  const initValue: IReqSignIn = {
    data: ENV.NODE_ENV === 'DEV' ? '<EMAIL>' : '',
    password: ENV.NODE_ENV === 'DEV' ? 'admin' : '',
  };
  const validationSchema = yup.object().shape({
    data: yup.string().required(),
    password: yup.string().required(),
  });
  const formik = useFormik({
    initialValues: initValue,
    validationSchema: validationSchema,
    onSubmit: (e) => auth.loginAction(e, setLoading),
  });

  // Function untuk generate QR code baru
  const generateNewQrCode = useCallback(() => {
    const code = generateUniqueCode() + ',' + fcmToken;
    setQrValue(code);
    setCount(60);
  }, [fcmToken]);

  useEffect(() => {
    onMessageListener()
      .then((payload: any) => {
        const data = payload?.data as IMessageSignInWithQr;
        const code = qrValue?.split(',')[0];

        if (code !== data.code) {
          alert(code);
          return;
        }

        setDataMessageSignIn({
          ...data,
          account: typeof data.account === 'string' ? JSON.parse(data.account) : data.account,
        });
      })
      .catch((err) => console.error('Failed to receive message:', err));

    return () => {};
  }, [qrValue]);
  const startCountdown = useCallback(() => {
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
    }

    countdownRef.current = setInterval(() => {
      setCount((prevCount) => {
        if (prevCount <= 1) {
          generateNewQrCode();
          return 60;
        }
        return prevCount - 1;
      });
    }, 1000); // Update setiap 1 detik
  }, [generateNewQrCode]);

  // Function untuk memulai QR code timer (generate QR baru setiap 1 menit)
  const startQrTimer = useCallback(() => {
    // Clear existing interval jika ada
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Generate QR code baru setiap 60 detik (1 menit)
    intervalRef.current = setInterval(() => {
      generateNewQrCode();
    }, 60000); // 60000ms = 1 menit
  }, [generateNewQrCode]);

  // Function untuk stop semua timer
  const stopQrTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
    setIsQrActive(false);
  }, []);

  async function onClickQr() {
    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      const token = await getToken(messaging, {
        vapidKey: VITE_APP_VAPID_KEY,
      });

      setLoginType('QR');
      const code = generateUniqueCode() + ',' + token;
      setQrValue(code);
      setFcmToken(token);
      setCount(60);
      setIsQrActive(true);
      auth.saveFcmToken(token);
    } else if (permission === 'denied') {
      alert('Ijinkan Akses Notifikasi terlebih dahulu');
      await Notification.requestPermission();
    }

    startCountdown();
    startQrTimer();
  }

  useEffect(() => {
    setFcmToken(auth.fcmToken);
  }, [auth.fcmToken]);

  // Cleanup saat component unmount
  useEffect(() => {
    return () => {
      stopQrTimer();
    };
  }, [stopQrTimer]);

  // Function untuk format waktu countdown (mm:ss)
  const formatCountdown = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  function onNextSignInQr() {
    if (dataMessageSignIn) {
      auth.onSignInQr(dataMessageSignIn);
    }
  }

  return {
    formik,
    showPassword,
    setShowPassword,
    loading,
    setLoginType,
    loginType,
    onClickQr,
    qrValue,
    count,
    isQrActive,
    formatCountdown,
    stopQrTimer,
    generateNewQrCode,
    dataMessageSignIn,
    setDataMessageSignIn,
    onNextSignInQr,
  };
}
