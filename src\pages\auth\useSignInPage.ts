import type { IReqSignIn } from '@/types/request/IReqSignIn.ts';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/hooks/use-auth.ts';
import { ENV } from '@/constants/env.ts';
import type { LoginType } from '@/enums/page-type';
import { generateUniqueCode } from '@/lib/utils';

export default function useSignInPage() {
  const auth = useAuth();
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [loginType, setLoginType] = useState<LoginType | undefined>(undefined);
  const [qrValue, setQrValue] = useState<string | undefined>();
  const [count, setCount] = useState<number>(60); // Countdown dalam detik (1 menit = 60 detik)
  const [isQrActive, setIsQrActive] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  const initValue: IReqSignIn = {
    data: ENV.NODE_ENV === 'DEV' ? '<EMAIL>' : '',
    password: ENV.NODE_ENV === 'DEV' ? 'admin' : '',
  };
  const validationSchema = yup.object().shape({
    data: yup.string().required(),
    password: yup.string().required(),
  });
  const formik = useFormik({
    initialValues: initValue,
    validationSchema: validationSchema,
    onSubmit: (e) => auth.loginAction(e, setLoading),
  });

  // Function untuk generate QR code baru
  const generateNewQrCode = useCallback(() => {
    const code = generateUniqueCode();
    setQrValue(code);
    setCount(60); // Reset countdown ke 60 detik
    console.log('QR Code baru dibuat:', code);
  }, []);

  // Function untuk memulai countdown timer
  const startCountdown = useCallback(() => {
    // Clear existing countdown jika ada
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
    }

    countdownRef.current = setInterval(() => {
      setCount((prevCount) => {
        if (prevCount <= 1) {
          // Jika countdown habis, generate QR code baru
          generateNewQrCode();
          return 60;
        }
        return prevCount - 1;
      });
    }, 1000); // Update setiap 1 detik
  }, [generateNewQrCode]);

  // Function untuk memulai QR code timer (generate QR baru setiap 1 menit)
  const startQrTimer = useCallback(() => {
    // Clear existing interval jika ada
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Generate QR code baru setiap 60 detik (1 menit)
    intervalRef.current = setInterval(() => {
      generateNewQrCode();
    }, 60000); // 60000ms = 1 menit
  }, [generateNewQrCode]);

  // Function untuk stop semua timer
  const stopQrTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
    setIsQrActive(false);
  }, []);

  // Function yang dipanggil saat user klik QR
  function onClickQr() {
    const code = generateUniqueCode();
    setLoginType('QR');
    setQrValue(code);
    setCount(60);
    setIsQrActive(true);

    // Mulai countdown dan timer untuk generate QR baru
    startCountdown();
    startQrTimer();
  }

  // Cleanup saat component unmount
  useEffect(() => {
    return () => {
      stopQrTimer();
    };
  }, [stopQrTimer]);

  // Function untuk format waktu countdown (mm:ss)
  const formatCountdown = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    formik,
    showPassword,
    setShowPassword,
    loading,
    setLoginType,
    loginType,
    onClickQr,
    qrValue,
    count,
    isQrActive,
    formatCountdown,
    stopQrTimer,
    generateNewQrCode,
  };
}
