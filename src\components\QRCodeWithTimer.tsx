import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Clock, QrCode, Smartphone } from 'lucide-react';
import Qr from 'react-qr-code';

interface QRCodeWithTimerProps {
  qrValue: string;
  count: number;
  formatCountdown: (seconds: number) => string;
  onRefresh: () => void;
  onStop: () => void;
}

export default function QRCodeWithTimer({
  qrValue,
  count,
  formatCountdown,
  onRefresh,
  onStop,
}: QRCodeWithTimerProps) {
  return (
    <div className="space-y-4">
      {/* QR Code Container */}
      <div className="relative border-2 border-blue-200 p-8 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50">
        {/* QR Code */}
        <div className="flex justify-center mb-4">
          <div className="relative bg-white p-4 rounded-lg shadow-lg">
            <Qr
              size={200}
              style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
              value={qrValue}
              viewBox={`0 0 256 256`}
            />
            
            {/* Countdown Badge */}
            <div className="absolute -top-2 -right-2">
              <div className={`px-3 py-1 rounded-full text-xs font-bold shadow-lg ${
                count <= 10 
                  ? 'bg-red-500 text-white animate-pulse' 
                  : count <= 30 
                  ? 'bg-yellow-500 text-white' 
                  : 'bg-green-500 text-white'
              }`}>
                {formatCountdown(count)}
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="text-center space-y-3">
          <div className="flex items-center justify-center gap-2 text-blue-700">
            <Smartphone className="w-5 h-5" />
            <h3 className="font-semibold text-lg">Scan QR di Mobile App</h3>
          </div>
          
          <p className="text-blue-600 text-sm leading-relaxed max-w-sm mx-auto">
            Buka aplikasi mobile Nuca Lale dan scan QR code di atas untuk login secara otomatis
          </p>

          {/* Steps */}
          <div className="grid grid-cols-3 gap-3 mt-4 text-xs text-blue-600">
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-1">
                <span className="font-bold">1</span>
              </div>
              <p>Buka App</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-1">
                <span className="font-bold">2</span>
              </div>
              <p>Scan QR</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-1">
                <span className="font-bold">3</span>
              </div>
              <p>Login</p>
            </div>
          </div>
        </div>
      </div>

      {/* Countdown Info */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">QR Code akan berubah dalam:</span>
          </div>
          <div className="flex items-center gap-2">
            <span className={`text-lg font-bold font-mono ${
              count <= 10 ? 'text-red-600' : count <= 30 ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {formatCountdown(count)}
            </span>
            {count <= 10 && (
              <div className="w-2 h-2 bg-red-500 rounded-full animate-ping"></div>
            )}
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-1000 ${
                count <= 10 
                  ? 'bg-red-500' 
                  : count <= 30 
                  ? 'bg-yellow-500' 
                  : 'bg-green-500'
              }`}
              style={{ width: `${(count / 60) * 100}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0:00</span>
            <span>1:00</span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          onClick={onRefresh}
          variant="outline"
          className="flex-1 flex items-center gap-2"
        >
          <QrCode className="w-4 h-4" />
          Generate QR Baru
        </Button>
        <Button
          onClick={onStop}
          variant="destructive"
          className="flex-1"
        >
          Stop Timer
        </Button>
      </div>
    </div>
  );
}
