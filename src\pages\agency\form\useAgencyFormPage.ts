import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import { ROUTES } from '@/routes/routes';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqAgencyForm } from '@/types/request/IReqAgencyForm.ts';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useNavigate, useParams } from 'react-router-dom';
import * as yup from 'yup';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResDetailAgency } from '@/types/response/IResDetailAgency.ts';
import { useEffect } from 'react';

export function useAgencyFormPage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useUi();
  const initValue: IReqAgencyForm = {
    name: '',
    logo_url: '',
    checked: false,
  };

  const validationSchema = yup.object().shape({
    name: yup.string().required(),
    lat: yup.number().required(),
    lng: yup.number().required(),
    checked: yup.boolean().required().isTrue(),
  });

  const mutation = useMutation({
    mutationFn: async (e: IReqAgencyForm) => {
      const data = {
        lat: e.lat,
        lng: e.lng,
        name: e.name,
        logo_url: e.logo_url,
        description: e.description,
      };
      try {
        await httpService.POST(ENDPOINT.LIST_AGENCY(), data);
        toast.success('Perangkat daerah berhasil dibuat');
        navigate(ROUTES.MASTER_DATA.AGENCY());
      } catch (e_1) {
        errorService.fetchApiError(e_1);
      }
    },
  });

  const mutationEdit = useMutation({
    mutationFn: async (e: IReqAgencyForm) => {
      const data = {
        lat: e.lat,
        lng: e.lng,
        name: e.name,
        logo_url: e.logo_url,
        description: e.description,
      };
      try {
        await httpService.PUT(ENDPOINT.DETAIL_AGENCY(id || ''), data);
        toast.success('Perangkat daerah berhasil diupdate');
        navigate(ROUTES.MASTER_DATA.AGENCY());
      } catch (e_1) {
        errorService.fetchApiError(e_1);
      }
    },
  });

  const queryDetail = useQuery({
    queryKey: ['detail_for_edit_agency'],
    enabled: !!id,
    queryFn: () =>
      httpService
        .GET(ENDPOINT.DETAIL_AGENCY(id || ''))
        .then((res: BaseResponse<IResDetailAgency>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        }),
  });

  const dataDetail = queryDetail.data;

  useEffect(() => {
    if (dataDetail && id) {
      formik.setValues({
        name: dataDetail.name,
        logo_url: dataDetail.logo_url,
        description: dataDetail.description,
        lng: dataDetail.lng,
        lat: dataDetail.lat,
        checked: false,
      });
    }
  }, [dataDetail]);

  const formik = useFormik({
    initialValues: initValue,
    validationSchema: validationSchema,
    onSubmit: (e) => (id ? mutationEdit.mutate(e) : mutation.mutate(e)),
  });

  return { formik, mutation, id };
}
