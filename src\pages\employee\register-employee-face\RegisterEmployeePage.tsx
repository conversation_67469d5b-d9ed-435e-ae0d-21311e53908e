import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Check,
  RefreshCw,
  Camera,
  User,
  ArrowLeft,
  Eye,
  RotateCcw,
  RotateCw,
  Smile,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap,
  ZapOff,
} from 'lucide-react';
import { useEffect } from 'react';
import RegisterCamCard from '@/components/RegisterCamCard';
import { useRegisterEmployeeFacePage } from './useRegisterEmployeFacePage';
import { Link } from 'react-router-dom';

function getInitials(name: string): string {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

function getInstructionIcon(detectionCriteria: string) {
  switch (detectionCriteria) {
    case 'lookingCenter':
      return Eye;
    case 'lookingLeft':
      return RotateCcw;
    case 'lookingRight':
      return RotateCw;
    case 'mouthOpen':
      return AlertCircle;
    case 'showingTeeth':
      return Smile;
    default:
      return User;
  }
}

export default function RegisterEmployeePage() {
  const page = useRegisterEmployeeFacePage();
  const data = page.queryDetail?.data;
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Pegawai',
      path: ROUTES.EMPLOYEE_LIST(),
    },
    {
      label: 'Pendaftaran Wajah',
      path: ROUTES.DETAIL_EMPLOYEE(data?.account_id || ''),
    },
    {
      label: data?.name || '',
    },
  ];

  // Clean up on unmount
  useEffect(() => {
    return () => {
      page.resetCapture();
    };
  }, []);

  // Get current instruction
  const currentInstruction = page.faceInstructions[page.currentInstructionIndex];
  const completedCount = page.faceInstructions.filter((inst) => inst.completed).length;
  const progressPercentage = (completedCount / page.faceInstructions.length) * 100;

  return (
    <div className="space-y-6">
      <PageContainer className="max-w-6xl mx-auto">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <PageTitle title="Pendaftaran Wajah Pegawai" breadcrumb={breadcrumb} />
          <div className="flex items-center gap-3">
            <Link to={ROUTES.DETAIL_EMPLOYEE(data?.account_id || '')}>
              <Button variant="outline" className="w-full sm:w-auto">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
            </Link>
          </div>
        </div>

        {/* Employee Info Card */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <Avatar className="w-16 h-16 border-2 border-gray-200">
                <AvatarImage src={data?.profile_picture} alt={data?.name} />
                <AvatarFallback className="bg-blue-100 text-blue-700 text-lg font-medium">
                  {data?.name ? getInitials(data.name) : 'PE'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900">{data?.name || 'Nama Pegawai'}</h2>
                <p className="text-gray-600">{data?.nip || 'NIP tidak tersedia'}</p>
                <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                  <span>{data?.agency_name || 'Perangkat daerah tidak diketahui'}</span>
                  <span>•</span>
                  <span>{data?.work_unit_name || 'Unit kerja tidak diketahui'}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">
                  {completedCount}/{page.faceInstructions.length}
                </div>
                <div className="text-sm text-gray-600">Foto Selesai</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Progress Pendaftaran</span>
                <span>{progressPercentage.toFixed(0)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Camera Section */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Camera className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">Kamera Pendaftaran</CardTitle>
                      <p className="text-sm text-gray-600">
                        {currentInstruction
                          ? `Langkah ${page.currentInstructionIndex + 1}: ${currentInstruction.instruction}`
                          : 'Semua foto selesai'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant={page.autoCapture ? 'default' : 'outline'}
                      onClick={page.toggleAutoCapture}
                      className="flex items-center gap-2"
                    >
                      {page.autoCapture ? <Zap className="w-4 h-4" /> : <ZapOff className="w-4 h-4" />}
                      {page.autoCapture ? 'Auto' : 'Manual'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <Separator />
              <CardContent className="p-0">
                <div className="relative aspect-video bg-gray-900 overflow-hidden">
                  <RegisterCamCard
                    onCapture={page.handleCapturedImage}
                    showDetections={true}
                    onFaceAnalysis={page.handleFaceAnalysis}
                    webcamRef={page.webcamRef as any}
                  />

                  {/* Countdown overlay */}
                  {page.captureReady && page.captureCountdown > 0 && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm z-30">
                      <div className="text-center">
                        <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-white text-3xl font-bold">{page.captureCountdown}</span>
                        </div>
                        <p className="text-white text-lg font-medium">Bersiap untuk foto...</p>
                      </div>
                    </div>
                  )}

                  {/* Current instruction overlay */}
                  {currentInstruction && !currentInstruction.completed && (
                    <div className="absolute bottom-4 left-4 right-4 z-30">
                      <div className="bg-black/90 backdrop-blur-sm text-white p-4 rounded-lg">
                        <div className="flex items-center gap-3 mb-2">
                          {(() => {
                            const IconComponent = getInstructionIcon(currentInstruction.detectionCriteria);
                            return <IconComponent className="w-5 h-5" />;
                          })()}
                          <span className="font-medium">{currentInstruction.instruction}</span>
                        </div>

                        {/* Detection status */}
                        <div className="flex items-center gap-2">
                          {page.faceAnalysis?.[currentInstruction.detectionCriteria] ? (
                            <div className="flex items-center gap-1 text-green-400">
                              <CheckCircle className="w-4 h-4" />
                              <span className="text-sm">Pose terdeteksi</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-yellow-400">
                              <Clock className="w-4 h-4" />
                              <span className="text-sm">Posisikan sesuai instruksi</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Face guide overlay */}
                  {!page.faceAnalysis?.faceDetected && !page.captureReady && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
                      <div className="border-2 border-white/50 rounded-full w-64 h-64 flex items-center justify-center">
                        <div className="border-2 border-white/30 rounded-full w-48 h-48 flex items-center justify-center">
                          <div className="text-white/80 text-center">
                            <User className="w-8 h-8 mx-auto mb-2" />
                            <p className="text-sm">Posisikan wajah di sini</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-center gap-3">
                <Button
                  onClick={page.captureManually}
                  disabled={page.captureReady || currentInstruction?.completed}
                  className="flex items-center gap-2"
                >
                  <Camera className="w-4 h-4" />
                  Ambil Foto Manual
                </Button>

                {page.capturedImages.length > 0 && (
                  <Button variant="outline" onClick={page.resetCapture} className="flex items-center gap-2">
                    <RefreshCw className="w-4 h-4" />
                    Reset Semua
                  </Button>
                )}
              </CardFooter>
            </Card>
          </div>

          {/* Instructions & Progress Panel */}
          <div className="space-y-6">
            {/* Instructions Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Instruksi Pose</CardTitle>
                <p className="text-sm text-gray-600">Ikuti setiap langkah untuk hasil terbaik</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {page.faceInstructions.map((instruction, index) => {
                    const IconComponent = getInstructionIcon(instruction.detectionCriteria);
                    const isActive = index === page.currentInstructionIndex;
                    const isCompleted = instruction.completed;

                    return (
                      <div
                        key={instruction.id}
                        className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                          isActive && !isCompleted
                            ? 'bg-blue-50 border-blue-200 shadow-sm'
                            : isCompleted
                              ? 'bg-green-50 border-green-200'
                              : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div
                          className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            isCompleted ? 'bg-green-100' : isActive ? 'bg-blue-100' : 'bg-gray-100'
                          }`}
                        >
                          {isCompleted ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <IconComponent className={`w-4 h-4 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                          )}
                        </div>
                        <div className="flex-1">
                          <p
                            className={`text-sm font-medium ${
                              isCompleted ? 'text-green-700 line-through' : isActive ? 'text-blue-700' : 'text-gray-700'
                            }`}
                          >
                            {index + 1}. {instruction.instruction}
                          </p>
                          {isActive && !isCompleted && <p className="text-xs text-blue-600 mt-1">Sedang aktif</p>}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Captured Images */}
            {page.capturedImages.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Foto Terambil</CardTitle>
                  <p className="text-sm text-gray-600">
                    {page.capturedImages.length} dari {page.faceInstructions.length} foto
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    {page.capturedImages.map((img, index) => (
                      <div
                        key={index}
                        className="relative aspect-square rounded-lg overflow-hidden border-2 border-green-200"
                      >
                        <img src={img} alt={`Foto ${index + 1}`} className="w-full h-full object-cover" />
                        <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                        <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                          Pose {index + 1}
                        </div>
                      </div>
                    ))}

                    {/* Placeholder for remaining photos */}
                    {Array.from({ length: page.faceInstructions.length - page.capturedImages.length }).map(
                      (_, index) => (
                        <div
                          key={`placeholder-${index}`}
                          className="aspect-square rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center bg-gray-50"
                        >
                          <div className="text-center">
                            <Camera className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                            <p className="text-xs text-gray-500">Pose {page.capturedImages.length + index + 1}</p>
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tips Card */}
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-lg text-blue-900">Tips Pendaftaran</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-blue-700">
                  <div className="flex items-start gap-2">
                    <div className="w-4 h-4 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold text-blue-700">1</span>
                    </div>
                    <p>Pastikan pencahayaan ruangan cukup terang</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-4 h-4 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold text-blue-700">2</span>
                    </div>
                    <p>Ikuti setiap instruksi pose dengan tepat</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-4 h-4 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold text-blue-700">3</span>
                    </div>
                    <p>Gunakan mode auto untuk hasil optimal</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-4 h-4 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold text-blue-700">4</span>
                    </div>
                    <p>Tahan posisi hingga foto terambil otomatis</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <Card className="mt-6">
          <CardFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between">
            <Link to={ROUTES.DETAIL_EMPLOYEE(data?.account_id || '')} className="w-full sm:w-auto">
              <Button variant="outline" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Batal
              </Button>
            </Link>

            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
              {page.capturedImages.length > 0 && page.capturedImages.length < page.faceInstructions.length && (
                <Button variant="outline" onClick={page.resetCapture} className="w-full sm:w-auto">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset Semua Foto
                </Button>
              )}

              <Button
                onClick={() => page.registerFaceMutation.mutate()}
                disabled={
                  page.capturedImages.length < page.faceInstructions.length || page.registerFaceMutation.isPending
                }
                loading={page.registerFaceMutation.isPending}
                className="w-full sm:w-auto"
              >
                <User className="w-4 h-4 mr-2" />
                {page.registerFaceMutation.isPending ? 'Mendaftarkan...' : 'Daftarkan Wajah'}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </PageContainer>
    </div>
  );
}
