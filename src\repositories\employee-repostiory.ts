import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailEmployee } from '@/types/response/IResDetailEmployee';
import type { IResEmplloyeSummary } from '@/types/response/IResEmployeeSummary';
import type { IResListEmployee } from '@/types/response/IResListEmployee';
import type { BaseResponse, BaseResponsePaginated } from '@/types/response/IResModel';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';

export class EmployeeRepository {
  httpService = new HttpService();
  errorService = new ErrorService();

  async getListAllAgencyId(id: string) {
    return this.httpService
      .GET(ENDPOINT.LIST_EMPLOYEE_BY_AGENCY_ID(id || ''))
      .then((res: BaseResponsePaginated<IResListEmployee[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async setActiveEmployee(id: string) {
    try {
      const res: BaseResponse<any> = await this.httpService.PATCH(ENDPOINT.SET_ACTIVE_EMPLOYEE(id));
      return res.data;
    } catch (e) {
      this.errorService.fetchApiError(e);
    }
  }

  async getDetailEmployee(id?: string): Promise<IResDetailEmployee> {
    try {
      const res: BaseResponse<IResDetailEmployee> = await this.httpService.GET(ENDPOINT.DETAIL_EMPLOYEE(id || ''));
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async listEmployee(filterData: IFilterList, ) : Promise<any> {
    const params = buildSearchParams(filterData);
    const url = `${ENDPOINT.LIST_EMPLOYEE()}` + buildQueryString(params);
    return await this.httpService
      .GET(url)
      .then((res: BaseResponsePaginated<IResListEmployee[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        return [];
      });
  }

  async getEmployeeSummary() {
    return await this.httpService
      .GET(ENDPOINT.EMPLOYEE_SUMMARY())
      .then((res: BaseResponse<IResEmplloyeSummary[]>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        return []

      });
  }
}
