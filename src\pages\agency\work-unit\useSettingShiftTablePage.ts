import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResListEmployee } from '@/types/response/IResListEmployee';
import type { IResListShift } from '@/types/response/IResListShift';
import type { BaseResponse } from '@/types/response/IResModel';
import { useQuery } from '@tanstack/react-query';
import { addDays, addWeeks, eachDayOfInterval, endOfWeek, format, startOfWeek, subWeeks } from 'date-fns';
import { useState } from 'react';
import { useParams } from 'react-router-dom';

export function useSettingShiftTablePage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const { toast } = useUi();
  const { id } = useParams();

  const queryEmployee = useQuery({
    queryKey: ['employeeByWorkUnitId', id],
    enabled: !!id,
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.EMPLOYEE_BY_WORK_UNIT_ID(id || ''))
        .then((res: BaseResponse<IResListEmployee[]>) => {
          return res.data.response_data || [];
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
  });

  const queryShift = useQuery({
    queryKey: ['shiftByWorkUnitId', id],
    enabled: !!id,
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.LIST_SHIFT_BY_WORK_UNIT_ID(id || ''))
        .then((res: BaseResponse<IResListShift[]>) => {
          return res.data.response_data || [];
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
  });

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));

  const daysOfWeek = eachDayOfInterval({
    start: currentWeekStart,
    end: endOfWeek(currentWeekStart, { weekStartsOn: 1 }),
  });

  const goToPreviousWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };

  const formatDate = (date: Date) => {
    return format(date, 'd MMM');
  };

  const formatDay = (date: Date) => {
    return format(date, 'EEEE');
  };

  const dataEmployee = queryEmployee.data || [];
  const dataShift = queryShift.data || [];

  const [shiftSchedule, setShiftSchedule] = useState<Record<string, Record<string, string>>>({});

  // Function untuk menghasilkan jadwal shift secara adil
  const generateFairShiftSchedule = () => {
    if (!dataEmployee.length || !dataShift.length) {
      return;
    }

    const newSchedule: Record<string, Record<string, string>> = {};

    // Inisialisasi struktur data untuk melacak jumlah shift per karyawan
    const employeeShiftCounts: Record<string, Record<string, number>> = {};
    dataEmployee.forEach((employee) => {
      employeeShiftCounts[employee.account_id] = {};
      dataShift.forEach((shift) => {
        employeeShiftCounts[employee.account_id][shift.id.toString()] = 0;
      });
      // Tambahkan penghitung untuk hari libur
      employeeShiftCounts[employee.account_id]['FREE'] = 0;

      // Inisialisasi jadwal kosong untuk setiap karyawan
      newSchedule[employee.account_id] = {};
    });

    // Tentukan hari kerja dan hari libur untuk setiap karyawan
    dataEmployee.forEach((employee, employeeIndex) => {
      // Tentukan hari libur (2 hari) secara bergiliran
      // Pastikan tidak semua karyawan libur di hari yang sama
      const daysOff: any[] = [];

      // Tentukan hari libur pertama (bergilir antara hari kerja)
      const firstDayOff = employeeIndex % 5; // 0-4 (Senin-Jumat)
      daysOff.push(firstDayOff);

      // Tentukan hari libur kedua (bergilir antara Sabtu dan Minggu)
      // Pastikan tidak semua karyawan libur di Sabtu atau Minggu
      const secondDayOff = employeeIndex % 2 === 0 ? 5 : 6; // Sabtu atau Minggu
      daysOff.push(secondDayOff);

      // Isi jadwal untuk setiap hari
      daysOfWeek.forEach((day, dayIndex) => {
        const dayKey = format(day, 'yyyy-MM-dd');

        // Jika hari ini adalah hari libur untuk karyawan ini
        if (daysOff.includes(dayIndex)) {
          newSchedule[employee.account_id][dayKey] = 'FREE';
          employeeShiftCounts[employee.account_id]['FREE']++;
        } else {
          let fairestShift = '';

          let minShiftCount = Infinity;

          const shiftsWithRandomOrder = [...dataShift]
            .sort(() => Math.random() - 0.5)
            .map((shift) => shift.id.toString());

          shiftsWithRandomOrder.forEach((shiftId) => {
            if (employeeShiftCounts[employee.account_id][shiftId] < minShiftCount) {
              minShiftCount = employeeShiftCounts[employee.account_id][shiftId];
              fairestShift = shiftId;
            }
          });

          const yesterdayKey = format(addDays(day, -1), 'yyyy-MM-dd');
          if (newSchedule[employee.account_id][yesterdayKey] === fairestShift) {
            const alternativeShift = shiftsWithRandomOrder.find((shiftId) => shiftId !== fairestShift) || fairestShift;
            fairestShift = alternativeShift;
          }

          newSchedule[employee.account_id][dayKey] = fairestShift;
          employeeShiftCounts[employee.account_id][fairestShift]++;
        }
      });
    });

    daysOfWeek.forEach((day, dayIndex) => {
      const dayKey = format(day, 'yyyy-MM-dd');

      if (dayIndex >= 5) {
        // Hitung berapa karyawan yang bekerja di hari ini
        const workingEmployees = dataEmployee.filter((employee) => newSchedule[employee.account_id][dayKey] !== 'FREE');

        // Jika kurang dari 30% karyawan bekerja di akhir pekan, tambahkan beberapa
        const minWorkingEmployees = Math.ceil(dataEmployee.length * 0.3);

        if (workingEmployees.length < minWorkingEmployees) {
          // Pilih beberapa karyawan yang libur untuk bekerja
          const employeesToWork = dataEmployee
            .filter((employee) => newSchedule[employee.account_id][dayKey] === 'FREE')
            .slice(0, minWorkingEmployees - workingEmployees.length);

          // Tetapkan shift untuk karyawan yang dipilih
          employeesToWork.forEach((employee) => {
            // Pilih shift secara acak
            const randomShift = dataShift[Math.floor(Math.random() * dataShift.length)].id.toString();
            newSchedule[employee.account_id][dayKey] = randomShift;

            // Berikan libur di hari lain sebagai gantinya
            const weekdayKeys = daysOfWeek
              .filter((_d, i) => i < 5) // Senin-Jumat
              .map((d) => format(d, 'yyyy-MM-dd'));

            // Cari hari kerja yang bisa diganti dengan libur
            for (const wdKey of weekdayKeys) {
              if (newSchedule[employee.account_id][wdKey] !== 'FREE') {
                newSchedule[employee.account_id][wdKey] = 'FREE';
                break;
              }
            }
          });
        }
      }
    });

    setShiftSchedule(newSchedule);
    toast.success('Jadwal shift berhasil digenerate secara adil');
  };

  const getEmployeeShift = (employeeId: string, day: Date) => {
    const dayKey = format(day, 'yyyy-MM-dd');
    // Jika jadwal sudah di-generate, gunakan nilai yang ada
    // Jika belum, kembalikan string kosong (tidak ada shift)
    return shiftSchedule[employeeId]?.[dayKey] || '';
  };

  // Fungsi untuk menghitung jumlah hari kerja per karyawan
  const getEmployeeWorkDays = (employeeId: string) => {
    let workDays = 0;
    let freeDays = 0;

    daysOfWeek.forEach((day) => {
      const shift = getEmployeeShift(employeeId, day);
      if (shift && shift !== 'FREE') {
        workDays++;
      } else if (shift === 'FREE') {
        freeDays++;
      }
    });

    return { workDays, freeDays };
  };

  // Fungsi untuk menghitung jumlah karyawan per shift per hari
  const getShiftCountsForDay = (day: Date) => {
    const counts: Record<string, number> = { FREE: 0 };

    dataShift.forEach((shift) => {
      counts[shift.id.toString()] = 0;
    });

    dataEmployee.forEach((employee) => {
      const shift = getEmployeeShift(employee.account_id, day);
      if (shift) {
        counts[shift] = (counts[shift] || 0) + 1;
      }
    });

    return counts;
  };

  return {
    dataEmployee,
    dataShift,
    selectedDate,
    setSelectedDate,
    currentWeekStart,
    setCurrentWeekStart,
    daysOfWeek,
    goToPreviousWeek,
    goToNextWeek,
    formatDate,
    formatDay,
    generateFairShiftSchedule,
    getEmployeeShift,
    shiftSchedule,
    setShiftSchedule,
    getEmployeeWorkDays,
    getShiftCountsForDay,
  };
}
