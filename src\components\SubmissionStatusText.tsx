// SubmissionStatusText.tsx
import type { SubmissionStatusEnum } from '@/types/type/enum-type.ts';
import { clsx } from 'clsx';

interface IProps {
  string?: string;
  enum: SubmissionStatusEnum;
}

export default function SubmissionStatusText({ string, enum: status }: IProps) {
  const statusLabel: Record<SubmissionStatusEnum, string> = {
    PENDING: 'Menunggu Persetujuan',
    APPROVE: 'Disetujui',
    REJECT: 'Ditolak',
  };

  const statusColor: Record<SubmissionStatusEnum, string> = {
    PENDING: 'text-yellow-600',
    APPROVE: 'text-green-600',
    REJECT: 'text-red-600',
  };

  return <div className={clsx(statusColor[status], 'font-bold')}>{string ?? statusLabel[status]}</div>;
}
