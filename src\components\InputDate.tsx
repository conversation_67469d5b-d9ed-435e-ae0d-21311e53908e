import type { FocusEventHandler, ReactNode } from 'react';
import { cn } from '@/lib/utils.ts';
import Label from '@/components/ui/Label.tsx';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { useState } from 'react';

interface IProps {
  id?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: Date | string;
  onBlur?: FocusEventHandler<HTMLButtonElement>;
  onChange?: (date: Date | undefined) => void;
  dataTestId?: string;
  alignment?: 'horizontal' | 'vertical';
  startDate?: Date; // Tanggal minimum yang bisa dipilih
  endDate?: Date; // Tanggal maksimum yang bisa dipilih
  dateFormat?: string; // Format tanggal yang ditampilkan
  disabled?: boolean;
}

export default function InputDate(props: IProps) {
  const alignment = props.alignment || 'vertical';
  const dateFormat = props.dateFormat || 'dd-MM-yyyy';
  const formik = useFormikContext<any>();
  const [open, setOpen] = useState(false);

  const [currentDate, setCurrentDate] = useState(new Date());

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  const currentValue = props.value ?? getIn(formik?.values, props.name);
  const selectedDate = currentValue
    ? currentValue instanceof Date
      ? currentValue
      : new Date(currentValue)
    : undefined;

  const handleDateSelect = (date: Date | undefined) => {
    if (props.onChange) {
      props.onChange(date);
    } else if (formik) {
      formik.setFieldValue(props.name, date);
    }
    setOpen(false);
  };

  const handleBlur = (event: React.FocusEvent<HTMLButtonElement>) => {
    if (props.onBlur) {
      props.onBlur(event);
    } else if (formik) {
      formik.handleBlur(event);
    }
  };

  const isDateDisabled = (date: Date) => {
    if (props.startDate && date < props.startDate) {
      return true;
    }
    if (props.endDate && date > props.endDate) {
      return true;
    }
    return false;
  };

  // Generate array of years (dari 100 tahun lalu sampai 10 tahun ke depan)
  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 100;
    const endYear = currentYear + 10;
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
      years.push(year);
    }
    return years.reverse(); // Urutkan dari terbaru ke terlama
  };

  // Array nama bulan
  const months = [
    'Januari',
    'Februari',
    'Maret',
    'April',
    'Mei',
    'Juni',
    'Juli',
    'Agustus',
    'September',
    'Oktober',
    'November',
    'Desember',
  ];

  const handleMonthChange = (monthIndex: string) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(parseInt(monthIndex));
    setCurrentDate(newDate);
  };

  const handleYearChange = (year: string) => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(parseInt(year));
    setCurrentDate(newDate);
  };

  const handlePreviousMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  const handleNextMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  return (
    <div className={cn('grid', alignment === 'horizontal' ? 'grid-cols-2' : '')}>
      {props.label && <Label className="flex items-center" label={props.label} required={props.required} />}
      <div>
        <div className={cn('relative flex items-center dark:bg-card bg-white')}>
          {props.startIcon && (
            <span className="absolute text-gray-500 left-3 flex items-center pr-3 z-10">{props.startIcon}</span>
          )}

          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                data-testid={props.dataTestId}
                variant="outline"
                disabled={props.disabled}
                onBlur={handleBlur}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && props.onEnter) {
                    e.preventDefault();
                    props.onEnter();
                  }
                }}
                name={props.name}
                className={cn(
                  'w-full justify-start text-left font-normal h-10',
                  props.startIcon ? 'pl-12' : 'pl-3',
                  props.endIcon ? 'pr-9' : 'pr-10',
                  errorMessage ? 'outline-red-500 border-red-500 bg-red-100' : '',
                  !selectedDate && 'text-muted-foreground',
                )}
                id={props.id}
              >
                {selectedDate ? format(selectedDate, dateFormat) : <span>{props.placeholder || 'Pilih tanggal'}</span>}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="center">
              <div className="flex items-center gap-3 justify-between p-3 border-b">
                <Button variant="outline" size="icon" className="h-7 w-7" onClick={handlePreviousMonth}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                <div className="flex items-center gap-2">
                  <Select value={currentDate.getMonth().toString()} onValueChange={handleMonthChange}>
                    <SelectTrigger className="w-32 h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {months.map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={currentDate.getFullYear().toString()} onValueChange={handleYearChange}>
                    <SelectTrigger className="w-20 h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="max-h-40">
                      {generateYears().map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button variant="outline" size="icon" className="h-7 w-7" onClick={handleNextMonth}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="w-full flex justify-center">
                <Calendar
                  style={{ width: '100%' }}
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelect}
                  disabled={isDateDisabled}
                  month={currentDate}
                  onMonthChange={setCurrentDate}
                />
              </div>
            </PopoverContent>
          </Popover>

          {props.endIcon && (
            <span className="absolute text-gray-500 right-3 flex items-center pl-3 z-10">{props.endIcon}</span>
          )}
        </div>
        {(errorMessage || props.helperText) && (
          <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
            {errorMessage || props.helperText}
          </p>
        )}
      </div>
    </div>
  );
}
