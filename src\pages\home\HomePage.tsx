import PageContainer from '@/components/PageContainer';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/use-auth';
import { MasterDataRepository } from '@/repositories/master-data-repository.ts';
import { useQuery } from '@tanstack/react-query';
import { Briefcase, Building2, Camera, ClipboardCheck, Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';
import { DemoValidateUser } from '../demo-validate-user/DemoValidateUserPage';
import ChartAttendancePerDay from './ChartAttendancePerDay';
import SummaryEmployeeStatusCard from './SummaryEmployeeStatus';

function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 12) return 'Selamat Pagi';
  if (hour < 15) return 'Selamat Siang';
  if (hour < 18) return 'Selamat Sore';
  return 'Selamat Malam';
}

function formatDate(date: Date): string {
  return date.toLocaleDateString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export default function HomePage() {
  const [showDemo, setShowDemo] = useState<boolean>(false);
  const masterDataRepository = new MasterDataRepository();
  const auth = useAuth();
  const currentDate = new Date();
  const date = new Date();
  const queryOverview = useQuery({
    queryKey: ['overview', date.toLocaleDateString()],
    queryFn: () => masterDataRepository.getOverview(),
  });

  const dashboardStats: any = {
    totalEmployees: queryOverview.data?.response_data?.total_employee || 0,
    totalAgencies: queryOverview.data?.response_data?.total_agency || 0,
    totalAttendance: queryOverview.data?.response_data?.total_attendance || 0,
    totalWorkUnit: queryOverview.data?.response_data?.total_work_unit || 0,
  };
  const statsConfig = [
    {
      key: 'totalAgencies',
      label: 'Total Perangkat daerah',
      description: 'Perangkat daerah terdaftar',
      icon: Building2,
      colors: {
        card: 'border-green-200 bg-gradient-to-br from-green-50 to-green-100/50',
        text: 'text-green-700',
        number: 'text-green-900',
        desc: 'text-green-600',
        iconBg: 'bg-green-100',
        icon: 'text-green-600',
      },
    },
    {
      key: 'totalAttendance',
      label: 'Total Kehadiran',
      description: 'Data kehadiran hari ini',
      icon: ClipboardCheck,
      colors: {
        card: 'border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100/50',
        text: 'text-purple-700',
        number: 'text-purple-900',
        desc: 'text-purple-600',
        iconBg: 'bg-purple-100',
        icon: 'text-purple-600',
      },
    },
    {
      key: 'totalWorkUnit',
      label: 'Total Unit Kerja',
      description: 'Unit kerja aktif',
      icon: Briefcase,
      colors: {
        card: 'border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100/50',
        text: 'text-orange-700',
        number: 'text-orange-900',
        desc: 'text-orange-600',
        iconBg: 'bg-orange-100',
        icon: 'text-orange-600',
      },
    },
  ];

  return (
    <div className="space-y-6">
      <PageContainer loading={queryOverview?.isLoading}>
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {getGreeting()}, {auth?.user?.name?.split(' ')[0] || 'Admin'}! 👋
              </h1>
              <p className="text-gray-600 mt-1">
                {formatDate(currentDate)} • {auth?.user?.agency_name || 'Dashboard Admin'}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant={showDemo ? 'destructive' : 'default'}
                onClick={() => setShowDemo(!showDemo)}
                className="flex items-center gap-2"
              >
                {showDemo ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showDemo ? 'Sembunyikan Demo' : 'Demo Validasi Wajah'}
              </Button>
            </div>
          </div>
        </div>

        {showDemo && (
          <div className="mb-8">
            <Card className="border-blue-200 bg-blue-50/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Camera className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg text-blue-900">Demo Validasi Wajah</CardTitle>
                    <p className="text-sm text-blue-700 mt-1">Uji coba sistem validasi wajah untuk absensi pegawai</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <DemoValidateUser />
              </CardContent>
            </Card>
          </div>
        )}

        <Card>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ">
              {statsConfig.map((stat) => {
                const IconComponent = stat.icon;
                return (
                  <Card key={stat.key} className={stat.colors.card}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className={`text-sm font-medium ${stat.colors.text}`}>{stat.label}</p>
                          <p className={`text-2xl font-bold ${stat.colors.number}`}>
                            {dashboardStats[stat.key].toLocaleString()}
                          </p>
                          <p className={`text-xs ${stat.colors.desc} mt-1`}>{stat.description}</p>
                        </div>
                        <div className={`w-12 h-12 ${stat.colors.iconBg} rounded-lg flex items-center justify-center`}>
                          <IconComponent className={`w-6 h-6 ${stat.colors.icon}`} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
        <SummaryEmployeeStatusCard />
        <ChartAttendancePerDay />
      </PageContainer>
    </div>
  );
}
