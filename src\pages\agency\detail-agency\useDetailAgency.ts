import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailAgency } from '@/types/response/IResDetailAgency';
import type { IResListEmployee } from '@/types/response/IResListEmployee';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { BaseResponse, BaseResponsePaginated } from '@/types/response/IResModel';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { useParams } from 'react-router-dom';

export const useDetailAgency = () => {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const [totalEmployee, setTotalEmployee] = useState<number>(0);
  const { id } = useParams();
  const queryDetail = useQuery({
    queryKey: ['detailAgency', id],
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.DETAIL_AGENCY(id!))
        .then((res: BaseResponse<IResDetailAgency>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
    enabled: !!id,
  });

  const queryWorkUnit = useQuery({
    initialData: [],
    queryKey: ['work_unit_agency', id],
    enabled: !!id,
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.LIST_WORK_UNIT_AGENCY(id || ''))
        .then((res: BaseResponse<IResWorkUnit[]>) => {
          return res.data.response_data || [];
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
  });

  const queryEmployee = useQuery({
    initialData: [],
    queryKey: ['employeeByAgencyId', id],
    enabled: !!id,
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.LIST_EMPLOYEE_BY_AGENCY_ID(id || ''))
        .then((res: BaseResponsePaginated<IResListEmployee[]>) => {
          setTotalEmployee(res.data.paginated_data.total_data);
          return res.data.response_data || [];
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
  });

  const dataDetail: IResDetailAgency | undefined = queryDetail?.data || undefined;
  const dataWorkUnit = queryWorkUnit.data || [];
  return { dataDetail, dataWorkUnit, queryEmployee, totalEmployee, queryDetail };
};
