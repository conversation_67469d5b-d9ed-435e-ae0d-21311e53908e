export const mockDataCreateWorkUnitRequest = {
  agency_id: '5601E4B86B564B2F9DE7FC13CC65AAB9',
  description: 'descrition',
  name: 'nama',
  timetable: [],
  location: [
    {
      name: 'Hello',
      radius: 100,
      lat: 1.4455469,
      lng: 125.1789124,
    },
    {
      name: 'Sentra Medika',
      radius: 100,
      lat: 1.4853032,
      lng: 124.8308776,
    },
  ],
  day: [
    {
      index: 0,
      label: 'Senin',
      data: [
        { name: '<PERSON> Masuk', start_time: '07:00', end_time: '07:30' },
        { name: '<PERSON>', start_time: '17:00', end_time: '17:30' },
      ],
    },
    {
      index: 1,
      label: '<PERSON><PERSON><PERSON>',
      data: [
        { name: '<PERSON> Masu<PERSON>', start_time: '07:00', end_time: '07:30' },
        { name: '<PERSON>lu<PERSON>', start_time: '17:00', end_time: '17:30' },
      ],
    },
    {
      index: 2,
      label: '<PERSON><PERSON>',
      data: [
        { name: '<PERSON>', start_time: '07:00', end_time: '07:30' },
        { name: '<PERSON>', start_time: '17:00', end_time: '17:30' },
      ],
    },
    {
      index: 3,
      label: 'Kamis',
      data: [
        { name: '<PERSON> <PERSON><PERSON>k', start_time: '07:00', end_time: '07:30' },
        { name: '<PERSON> <PERSON>luar', start_time: '17:00', end_time: '17:30' },
      ],
    },
    {
      index: 4,
      label: 'Jumat',
      data: [
        { name: '<PERSON> Masuk', start_time: '07:00', end_time: '07:30' },
        { name: 'Jam Keluar', start_time: '17:00', end_time: '17:30' },
      ],
    },
    {
      index: 5,
      label: 'Sabtu',
      data: [{ name: 'cloin', start_time: '20:20', end_time: '20:34' }],
    },
    {
      index: 6,
      label: 'Minggu',
      data: [{ name: 'info', start_time: '03:00', end_time: '12:00' }],
    },
  ],
};
