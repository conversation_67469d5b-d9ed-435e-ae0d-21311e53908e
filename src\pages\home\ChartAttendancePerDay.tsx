import { ChartContainer } from '@/components/ui/chart';
import { useQuery } from '@tanstack/react-query';
import { subDays } from 'date-fns';
import { <PERSON>, Bar<PERSON><PERSON>, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from 'recharts';
import { AttendanceRepository } from '@/repositories/attendance-repository';
import DateHelper from '@/lib/date-helper';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import CardLoading from '@/components/CardLoading';
import { Calendar, TrendingUp, Users } from 'lucide-react';
import { useState, useEffect } from 'react';

export default function ChartAttendancePerDay() {
  const [isAnimated, setIsAnimated] = useState(false);
  const attendanceRepository = new AttendanceRepository();
  const startDate = new Date();
  const endDate = subDays(startDate, 60);

  const { data: chartData = [], isLoading } = useQuery({
    queryKey: ['chart-data-attendance'],
    queryFn: () =>
      attendanceRepository
        .getAttendanceChart(
          DateHelper.toFormatDate(endDate, 'yyyy-MM-dd'),
          DateHelper.toFormatDate(startDate, 'yyyy-MM-dd'),
        )
        .then((res) =>
          res?.response_data.map((item) => ({
            date: DateHelper.toFormatDate(new Date(item.date), 'dd MMM'),
            fullDate: item.date,
            data: item.value,
          })),
        ),
  });

  // Calculate statistics
  const totalAttendance = chartData.reduce((sum, item) => sum + item.data, 0);
  const averageAttendance = chartData.length > 0 ? Math.round(totalAttendance / chartData.length) : 0;
  const maxAttendance = chartData.length > 0 ? Math.max(...chartData.map((item) => item.data)) : 0;

  // Trigger animation when data loads
  useEffect(() => {
    if (chartData.length > 0) {
      const timer = setTimeout(() => setIsAnimated(true), 100);
      return () => clearTimeout(timer);
    }
  }, [chartData]);

  const chartConfig = {
    data: {
      label: 'Kehadiran',
      color: 'hsl(var(--chart-1))',
    },
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
          <p className="text-sm font-medium text-gray-900 dark:text-white">
            {DateHelper.toFormatDate(new Date(data.payload.fullDate), 'dd MMMM yyyy')}
          </p>
          <p className="text-sm text-blue-600 dark:text-blue-400 flex items-center gap-1">
            <Users className="w-4 h-4" />
            {data.value} orang hadir
          </p>
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card className="overflow-hidden pt-0">
        <CardContent className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400 animate-pulse" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">Grafik Kehadiran Harian</CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Memuat data kehadiran...</p>
            </div>
          </div>
        </CardContent>
        <CardContent className="p-6">
          <CardLoading />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={`overflow-hidden pt-0  transition-all duration-300 ${isAnimated ? 'animate-in fade-in-0 slide-in-from-bottom-4' : ''}`}
    >
      {/* Enhanced Header */}
      <CardContent className="bg-gradient-to-r py-4 border-b border-blue-100 dark:border-blue-800/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center ">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">Grafik Kehadiran Harian</CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {DateHelper.toFormatDate(endDate, 'dd MMMM yyyy')} -{' '}
                {DateHelper.toFormatDate(startDate, 'dd MMMM yyyy')}
              </p>
            </div>
          </div>
        </div>
      </CardContent>

      <CardContent className="p-6 bg-gray-50/50 dark:bg-gray-900/20">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4  border border-gray-200 dark:border-gray-700  transition-shadow duration-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Kehadiran</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalAttendance.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4  border border-gray-200 dark:border-gray-700   duration-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Rata-rata Harian</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averageAttendance}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4  border border-gray-200 dark:border-gray-700   duration-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Kehadiran Tertinggi</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{maxAttendance}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Chart Section */}
        <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
          <ResponsiveContainer width="100%" height={350}>
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" className="dark:stroke-gray-600" />
              <XAxis
                dataKey="date"
                tick={{ fontSize: 12, fill: '#6b7280' }}
                tickLine={{ stroke: '#d1d5db' }}
                axisLine={{ stroke: '#d1d5db' }}
              />
              <YAxis
                tick={{ fontSize: 12, fill: '#6b7280' }}
                tickLine={{ stroke: '#d1d5db' }}
                axisLine={{ stroke: '#d1d5db' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="data"
                fill="url(#colorGradient)"
                radius={[4, 4, 0, 0]}
                className="hover:opacity-80 transition-opacity duration-200"
              />
              <defs>
                <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8} />
                  <stop offset="100%" stopColor="#1d4ed8" stopOpacity={0.6} />
                </linearGradient>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
