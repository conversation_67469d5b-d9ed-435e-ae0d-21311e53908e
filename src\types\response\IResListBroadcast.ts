import type { BroadcastStatusTypeEnum } from '@/types/type/BroadcastStatusTypeEnum.ts';

export interface IResListBroadcast {
  id: string;
  title: string;
  status_enum: BroadcastStatusTypeEnum;
  status_string: string;
  coverage_enum: string;
  coverage_string: string;
  created_date: Date;
  created_by_name: string;
  created_by_id: string;
  created_by_profile_picture: string;
  created_by_work_unit_id: string;
  created_by_work_unit_name: string;
  created_by_agency_id: string;
  created_by_agency_name: string;
}
