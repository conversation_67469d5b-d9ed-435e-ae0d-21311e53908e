import IconContainer from '@/components/IconContainer';
import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import { useParams } from 'react-router-dom';
import { useSettingShiftWorkUnit } from './useSettingShiftWorkUnitPage';
import { FieldArray, FormikProvider } from 'formik';
import InputText from '@/components/InputText.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Plus, Send, Trash } from 'lucide-react';
import { Fragment } from 'react';
import ConfirmationCheckBox from '@/components/ConfirmationCheckbox.tsx';
import InputTime from '@/components/InputTime.tsx';

export default function SettingShiftWorkUnitPage() {
  const { id } = useParams();
  const page = useSettingShiftWorkUnit();
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Unit Kerja',
      path: ROUTES.LIST_WORK_UNIT(),
    },
    {
      label: 'Detail Unit Kerja',
      path: ROUTES.DETAIL_WORK_UNIT(id || ''),
    },
    {
      label: 'Management shift',
    },
  ];

  return (
    <PageContainer loading={page?.queryList?.isFetching}>
      <PageTitle breadcrumb={breadcrumbs} title="Setting shift" />
      <Card>
        <CardHeader>
          <div className="flex gap-3 items-center">
            <IconContainer icon="Clock" variant="purple" />
            <div>
              <div className="text-xl font-semibold">Atur shift</div>
              <p>Atur jam kerja shift </p>
            </div>
          </div>
        </CardHeader>
        <Separator />
        <FormikProvider value={page.formik}>
          <FieldArray name="data">
            {({ push }) => (
              <div>
                {page.formik.values.data.map((item, i) => (
                  <Fragment key={i}>
                    <CardContent>
                      <div className={'grid gap-3 relative py-4'}>
                        <div className={'flex items-end gap-3 '}>
                          <div className={'flex-1'}>
                            <InputText
                              label={'Nama shift'}
                              required={true}
                              name={`data[${i}].name`}
                              placeholder="Nama Shift"
                              endIcon={
                                page.formik.values.data.length > 1 && (
                                  <>
                                    {page.loadingDeleteId === item.id && item.id ? (
                                      <div>LOADING</div>
                                    ) : (
                                      <Button
                                        variant={'link'}
                                        className={'text-red-800'}
                                        type={'button'}
                                        onClick={() => page.onDelete(i, item.id)}
                                      >
                                        <Trash />
                                        hapus
                                      </Button>
                                    )}
                                  </>
                                )
                              }
                            />
                          </div>
                        </div>
                        <InputText
                          required
                          label="Kode"
                          name={`data[${i}].code`}
                          placeholder="Masukan kode, exp : (P) untuk shift pagi "
                        />
                        <div className={'grid grid-cols-2 gap-3'}>
                          <InputTime
                            label="Jam mulai"
                            name={`data[${i}].start_time`}
                            placeholder="Start Time (HH:mm)"
                          />
                          <InputTime label="Jam Pulang" name={`data[${i}].end_time`} placeholder="End Time (HH:mm)" />
                        </div>
                      </div>
                    </CardContent>
                    {page.formik.values.data.length - 1 !== i && <Separator />}
                  </Fragment>
                ))}
                <CardContent>
                  <div className={'flex justify-end w-full '}>
                    <Button
                      type="button"
                      variant={'outline'}
                      onClick={() =>
                        push({
                          name: '',
                          start_time: '',
                          end_time: '',
                        })
                      }
                    >
                      <Plus />
                      Tambah Shift
                    </Button>
                  </div>
                </CardContent>
              </div>
            )}
          </FieldArray>
        </FormikProvider>
        <Separator />
        <CardFooter>
          <div className={'flex items-center justify-between w-full'}>
            <ConfirmationCheckBox
              label={'Konfirmasi perubahan'}
              onCheckedChange={(e) => page.formik.setFieldValue('checked', e)}
              checked={page.formik.values.checked}
              description={'Konfirmasi untuk mengelola data shift'}
            />
            <Button
              type={'button'}
              loading={page.mutationSubmit.isPending}
              disabled={!page.formik.values.checked}
              onClick={() => page.formik.handleSubmit()}
            >
              <Send /> Kirim perubahan
            </Button>
          </div>
        </CardFooter>
      </Card>
    </PageContainer>
  );
}
