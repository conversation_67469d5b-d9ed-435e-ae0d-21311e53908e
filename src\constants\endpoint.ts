export const ENDPOINT = {
  SIGN_IN: () => `/auth/v1/admin/sign-in`,
  GET_ME: () => `/account/v1/me`,
  LIST_AGENCY: () => `/agency/v1`,
  DETAIL_AGENCY: (id: string) => `/agency/v1/${id}`,
  LIST_RELIGION: () => `/master-data/v1/religion`,
  LIST_MARITAL_STATUS: () => `/master-data/v1/marital-status`,
  CREATE_EMPLOYEE: () => `/employee/v1`,
  LIST_EMPLOYEE: () => `/employee/v1`,
  LIST_WORK_UNIT_AGENCY: (agencyId: string) => `/schedule/v1/work-unit/agency/${agencyId}`,
  CREATE_WORK_UNIT: () => `/schedule/v1/work-unit`,
  DETAIL_WORK_UNIT: (id: string) => `/schedule/v1/work-unit/detail/${id}`,
  LIST_EMPLOYEE_BY_AGENCY_ID: (id: string) => `/employee/v1?agency_id=${id}&size=9999`,
  DETAIL_EMPLOYEE: (id: string) => `/employee/v1/${id}`,
  REGISTER_EMPLOYEE_FACE: (id: string) => `/employee/v1/register-face/${id}`,
  EMPLOYEE_BY_WORK_UNIT_ID: (id: string) => `/employee/v1/work-unit/${id}`,
  LIST_SHIFT_BY_WORK_UNIT_ID: (id: string) => `/schedule/v1/shifts-by-work-unit/${id}`,
  LIST_WORK_UNIT: () => `/schedule/v1/work-unit`,
  SET_ACTIVE_EMPLOYEE: (id: string) => `/employee/v1/activated-employee/${id}`,
};
