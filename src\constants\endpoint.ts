export const ENDPOINT = {
  SIGN_IN: () => `/auth/v1/admin/sign-in`,
  GET_ME: () => `/account/v1/me`,
  LIST_AGENCY: () => `/agency/v1`,
  LIST_AGENCY_ADMIN: () => `/agency/v1/admin`,
  DETAIL_AGENCY: (id: string) => `/agency/v1/${id}`,
  LIST_RELIGION: () => `/master-data/v1/religion`,
  LIST_MARITAL_STATUS: () => `/master-data/v1/marital-status`,
  CREATE_EMPLOYEE: () => `/employee/v1`,
  EDIT_EMPLOYEE: (id: string) => `/employee/v1/${id}`,
  LIST_EMPLOYEE: () => `/employee/v1`,
  LIST_WORK_UNIT_AGENCY: (agencyId: string) => `/schedule/v1/work-unit/agency/${agencyId}`,
  CREATE_WORK_UNIT: () => `/work-unit/v1`,
  DETAIL_WORK_UNIT: (id: string) => `/schedule/v1/work-unit/detail/${id}`,
  EDIT_WORK_UNIT: (id: string) => `/work-unit/v1/${id}`,
  LIST_EMPLOYEE_BY_AGENCY_ID: (id: string) => `/employee/v1?agency_id=${id}&size=9999`,
  DETAIL_EMPLOYEE: (id: string) => `/employee/v1/${id}`,
  REGISTER_EMPLOYEE_FACE: (id: string) => `/employee/v1/register-face/${id}`,
  EMPLOYEE_BY_WORK_UNIT_ID: (id: string) => `/employee/v1/work-unit/${id}`,
  LIST_SHIFT_BY_WORK_UNIT_ID: (id: string) => `/work-unit/v1/shift/${id}`,
  LIST_WORK_UNIT: () => `/work-unit/v1/list/admin`,
  SET_ACTIVE_EMPLOYEE: (id: string) => `/employee/v1/activated-employee/${id}`,
  LIST_LOCATION_WORK_UNI: (id: string) => `/work-unit/v1/location/${id}`,
  SETTING_WORK_UNIT_LOCATION: (id: string) => `/work-unit/v1/location/${id}`,
  LIST_SCHEDULE_WORK_UNIT: (id: string) => `/work-unit/v1/schedule/${id}`,
  SETTING_SCHEDULE_REGULAR_WORK_UNIT: (id: string) => `/work-unit/v1/schedule/regular/${id}`,
  SETTING_SHIFT_WORK_UNIT: (id: string) => `/work-unit/v1/shift/${id}`,
  DELETE_SCHEDULE: (id: string) => `/work-unit/v1/schedule/${id}`,
  MANAGE_SHIFT: (id: string) => `/work-unit/v1/manage-shift/${id}`,
  SETTING_SHIFT_ATTENDANCE_TIME: (id: string) => `/work-unit/v1/shift/manage-attendance-time/${id}`,
  LIST_ATTENDANCE: () => `/attendance/v1/history`,
  LIST_RECAP_HISTORY_ATTENDANCE: () => `/attendance/v1/history-recap`,
  LIST_SCHEDULE_SHIFT_WORK_UNIT_BY_DATE: (id: string, date: { start: string; end: string }) => {
    return `/work-unit/v1/shift/list-schedule/${id}?start_date=${date.start}&end_date=${date.end}`;
  },
  GET_OVERVIEW: (date: string) => `/master-data/v1/overview?date=${date}`,
  SEND_ADD_MANAGER: () => `/work-unit/v1/add-manager`,
  ASSIGN_ADMIN: (id: string) => `/employee/v1/assign-admin/${id}`,
  LIST_CHART: (startDate: string, endDate: string) =>
    `/attendance/v1/chart?start_date=${startDate}&end_date=${endDate}`,
  LIST_SUBMISSION: () => `/submission/v1/admin`,
  APPROVE_SUBMISSION: (id: string) => `/submission/v1/approve/${id}`,
  REJECT_SUBMISSION: (id: string) => `/submission/v1/reject/${id}`,
  LIST_PROVINCE: () => `/area/province`,
  LIST_CITY: (id: string) => `/area/city/${id}`,
  LIST_DISTRICT: (id: string) => `/area/district/${id}`,
  SUB_DISTRICT: (id: string) => `/area/sub-district/${id}`,
  LIST_ALL_ROLE: () => `/role-access/list-all`,
  LIST_ALL_PRIVILEGES: () => `/role-access/privileges`,
  LIST_ROLE_ACCESS: () => `/role-access`,
  SETTING_ROLE_ACCESS: () => `/role-access/setting`,
  EMPLOYEE_SUMMARY: () => `/employee/v1/summary`,
  DETAIL_SUBMISSION: (id: string) => `/submission/v1/${id}`,
  LIST_BROADCAST: () => `/broadcast/v1`,
  LIST_BROADCAST_COVERAGE_TYPE: () => `/master-data/v1/broadcast-type`,
  CREATE_NEW_BROADCAST: () => `/broadcast/v1`,
  DETAIL_BROADCAST: (id: string) => `/broadcast/v1/${id}`,
  APPROVE_BROADCAST: (id: string) => `/broadcast/v1/approve/${id}`,
  REJECT_BROADCAST: (id: string) => `/broadcast/v1/reject/${id}`,
  SEND_BROADCAST: (id: string) => `/broadcast/v1/send-broadcast/${id}`,
  GET_SUMMARY_COUNT: () => `/broadcast/v1/summary-count`,
};
