import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { getInitials } from '@/lib/utils';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import {
  Briefcase,
  Building2,
  Calendar,
  CardSim,
  ChevronDown,
  Clock,
  Edit,
  Eye,
  FileText,
  MapPin,
  Settings,
  UserPlus,
  Users,
} from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import DetailWorkUnitSkeleton from './DetailWorkUnitSkeleton';
import { useDetailWorkUnitPage } from './useDetailWorkUnitPage';

export default function DetailWorkUnit() {
  const navigate = useNavigate();
  const page = useDetailWorkUnitPage();
  const data = page?.queryDetail?.data;
  const agencyData = page?.queryAgency?.data;

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: data?.agency_name || '-',
      path: ROUTES.DETAIL_AGENCY(data?.agency_id || ''),
    },
    {
      label: 'Unit Kerja',
      path: ROUTES.DETAIL_AGENCY(page.agencyId || ''),
    },
    {
      label: data?.name || '-',
    },
  ];

  if (page.queryDetail?.isPending) {
    return (
      <PageContainer>
        <PageTitle title="Detail Unit Kerja" breadcrumb={breadcrumb} />
        <DetailWorkUnitSkeleton />
      </PageContainer>
    );
  }

  if (page.queryDetail?.isError) {
    return (
      <PageContainer>
        <PageTitle title="Detail Unit Kerja" breadcrumb={breadcrumb} />
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <FileText className="w-8 h-8 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Gagal Memuat Data</h3>
              <p className="text-gray-600 mb-4">Terjadi kesalahan saat memuat detail unit kerja.</p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Coba Lagi
              </Button>
            </div>
          </div>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <PageTitle title="Detail Unit Kerja" breadcrumb={breadcrumb} />

        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={() => navigate(ROUTES.DETAIL_AGENCY(page.agencyId || ''))}>
            <Eye className="w-4 h-4 mr-2" />
            Lihat Instansi
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <Settings className="w-4 h-4 mr-2" />
                Kelola
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <Link
                to={
                  data?.type === 'SHIFT'
                    ? ROUTES.SETTING_SHIFT_TABLE(data.id)
                    : ROUTES.SETTING_SCHEDULE_REGULAR(data?.id || '')
                }
              >
                <DropdownMenuItem>
                  <Calendar className="w-4 h-4 mr-2" />
                  Atur Jadwal
                </DropdownMenuItem>
              </Link>
              {data?.type === 'SHIFT' && (
                <Link to={ROUTES.SETTING_SHIFT_ATTENDANCE_TIME(data?.id || '')}>
                  <DropdownMenuItem>
                    <CardSim className="w-4 h-4 mr-2" />
                    Kelola Waktu Absen
                  </DropdownMenuItem>
                </Link>
              )}
              <Link to={ROUTES.EDIT_WORK_UNIT(data?.id || '')}>
                <DropdownMenuItem>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Unit Kerja
                </DropdownMenuItem>
              </Link>
              <Link to={ROUTES.SETTING_WORK_UNIT_LOCATION(data?.id || '')}>
                <DropdownMenuItem>
                  <MapPin className="w-4 h-4 mr-2" />
                  Kelola lokasi
                </DropdownMenuItem>
              </Link>
              {data?.type === 'SHIFT' && (
                <Link to={ROUTES.SETTING_SHIFT_WORK_UNIT(data?.id || '')}>
                  <DropdownMenuItem>
                    <Clock className="w-4 h-4 mr-2" />
                    Kelola Shift
                  </DropdownMenuItem>
                </Link>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="space-y-6 animate-in fade-in-50 duration-500">
        <Card className="overflow-hidden transition-all duration-300  py-0">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 h-32"></div>
          <CardContent className="relative pt-0 pb-6">
            <div className="flex flex-col sm:flex-row gap-6 -mt-16">
              <div className="relative">
                <Avatar className="w-32 h-32 border-4 border-white ">
                  <AvatarImage src={agencyData?.logo_url} alt={data?.name} className="object-cover" />
                  <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {data?.name ? getInitials(data.name) : 'UK'}
                  </AvatarFallback>
                </Avatar>
              </div>

              <div className="flex-1 pt-4 sm:pt-8 min-w-0">
                <div className="flex flex-col pt-4 sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2 break-words">
                      {data?.name || 'Nama Unit Kerja'}
                    </h1>
                    <div className="flex flex-wrap items-center gap-3 text-gray-600 mb-3">
                      <div className="flex items-center gap-2 min-w-0">
                        <Building2 className="w-4 h-4 flex-shrink-0" />
                        <span className="font-medium break-words">{data?.agency_name || agencyData?.name || '-'}</span>
                      </div>
                    </div>
                    {data?.description && (
                      <div className="flex items-start gap-2 min-w-0">
                        <FileText className="w-4 h-4 text-gray-500 flex-shrink-0 mt-0.5" />
                        <p className="text-gray-700 break-words">{data.description}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col items-start sm:items-end gap-2 flex-shrink-0">
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Briefcase className="w-3 h-3" />
                      Unit Kerja
                    </Badge>
                    <div className="text-sm text-gray-500 break-all">ID: {data?.id || '-'}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Information Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card className="transition-all duration-300 hover:shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Briefcase className="w-5 h-5 text-blue-600" />
                Informasi Dasar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Nama Unit Kerja</span>
                </div>
                <span className="text-sm font-semibold text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                  {data?.name || '-'}
                </span>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <Building2 className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Instansi</span>
                </div>
                <span className="text-sm font-semibold text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                  {data?.agency_name || agencyData?.name || '-'}
                </span>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <Badge variant="outline" className="flex items-center gap-1 w-fit">
                    <Clock className="w-3 h-3" />
                    {data?.type === 'SHIFT' ? 'Shift' : 'Regular'}
                  </Badge>
                  <span className="text-sm font-medium text-gray-600">Tipe Jadwal</span>
                </div>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Deskripsi</span>
                </div>
                <span className="text-sm text-gray-700 break-words sm:text-right sm:max-w-[60%]">
                  {data?.description || 'Tidak ada deskripsi'}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card className="transition-all duration-300 hover:shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="w-5 h-5 text-green-600" />
                Statistik
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <Users className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Total Pegawai</span>
                </div>
                <Badge
                  variant="secondary"
                  className="self-start sm:self-center bg-blue-50 text-blue-700 border-blue-200"
                >
                  0 Pegawai
                </Badge>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Lokasi Kerja</span>
                </div>
                <Badge
                  variant="secondary"
                  className="self-start sm:self-center bg-purple-50 text-purple-700 border-purple-200"
                >
                  0 Lokasi
                </Badge>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-3">
                <div className="flex items-center gap-3 min-w-0">
                  <Calendar className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Jadwal Kerja</span>
                </div>
                <Badge
                  variant="secondary"
                  className="self-start sm:self-center bg-orange-50 text-orange-700 border-orange-200"
                >
                  Belum Diatur
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Managers Section */}
        <Card className="transition-all duration-300 hover:shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Users className="w-5 h-5 text-purple-600" />
              Atasan Unit Kerja
              {data?.managers && data.managers.length > 0 && (
                <Badge variant="outline" className="ml-2">
                  {data.managers.length} Atasan
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {data?.managers && data.managers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.managers.map((manager: any) => (
                  <div
                    key={manager.id}
                    className="flex items-center gap-4 p-4 rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-white hover:shadow-md transition-all duration-200 hover:border-purple-300"
                  >
                    <Avatar className="w-12 h-12 border-2 border-white shadow-sm">
                      <AvatarImage
                        src={manager.profile_picture}
                        alt={manager.name}
                        className="object-cover"
                        onError={(e) => {
                          // Fallback to initials if image fails to load
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                      <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white font-semibold text-sm">
                        {getInitials(manager.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 text-sm break-words">{manager.name}</h4>
                      <p className="text-xs text-gray-500 break-all">ID: {manager.id}</p>
                      {manager.work_unit_name && (
                        <p className="text-xs text-purple-600 font-medium mt-1">{manager.work_unit_name}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Belum Ada Atasan</h3>
                <p className="text-gray-500 mb-4">Unit kerja ini belum memiliki atasan yang ditugaskan.</p>
                <Link to={ROUTES.ADD_WORK_UNIT_MANGER(page.workUnitId || '')}>
                  <Button variant="outline" className="flex items-center gap-2">
                    <UserPlus className="w-4 h-4" />
                    Tambah Atasan
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="transition-all duration-300 hover:shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Settings className="w-5 h-5 text-orange-600" />
              Aksi Cepat
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                variant="outline"
                className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200 hover:shadow-md hover:border-blue-300 hover:bg-blue-50 min-w-0"
                onClick={() =>
                  navigate(
                    data?.type === 'SHIFT'
                      ? ROUTES.SETTING_SHIFT_TABLE(data.id)
                      : ROUTES.SETTING_SCHEDULE_REGULAR(data?.id || ''),
                  )
                }
              >
                <Calendar className="w-5 h-5 flex-shrink-0 text-blue-600" />
                <div className="text-left min-w-0">
                  <div className="font-semibold break-words text-gray-900">Atur Jadwal</div>
                  <div className="text-xs text-gray-500 break-words">Kelola jadwal kerja</div>
                </div>
              </Button>

              <Link className={'w-full'} to={ROUTES.ADD_WORK_UNIT_MANGER(page.workUnitId || '')}>
                <Button
                  variant="outline"
                  className="flex w-full items-center gap-3 h-auto py-4 px-4 transition-all duration-200 hover:shadow-md hover:border-green-300 hover:bg-green-50 min-w-0"
                >
                  <UserPlus className="w-5 h-5 flex-shrink-0 text-green-600" />
                  <div className="text-left min-w-0">
                    <div className="font-semibold break-words text-gray-900">Kelola Atasan</div>
                    <div className="text-xs text-gray-500 break-words">Kelola atasan unit kerja</div>
                  </div>
                </Button>
              </Link>

              <Link to={ROUTES.SETTING_WORK_UNIT_LOCATION(page?.workUnitId || '')} className={'w-full'}>
                <Button
                  variant="outline"
                  className="flex items-center w-full gap-3 h-auto py-4 px-4 transition-all duration-200 hover:shadow-md hover:border-purple-300 hover:bg-purple-50 min-w-0"
                >
                  <MapPin className="w-5 h-5 flex-shrink-0 text-purple-600" />
                  <div className="text-left min-w-0">
                    <div className="font-semibold break-words text-gray-900">Kelola Lokasi</div>
                    <div className="text-xs text-gray-500 break-words">Atur lokasi kerja</div>
                  </div>
                </Button>
              </Link>

              <Link to={ROUTES.EDIT_WORK_UNIT(data?.id || '')} className={'w-full'}>
                <Button
                  variant="outline"
                  className="flex items-center w-full gap-3 h-auto py-4 px-4 transition-all duration-200 hover:shadow-md hover:border-orange-300 hover:bg-orange-50 min-w-0"
                >
                  <Edit className="w-5 h-5 flex-shrink-0 text-orange-600" />
                  <div className="text-left min-w-0">
                    <div className="font-semibold break-words text-gray-900">Edit Unit Kerja</div>
                    <div className="text-xs text-gray-500 break-words">Ubah informasi unit</div>
                  </div>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
